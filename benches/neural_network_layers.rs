//! Neural Network Layers Performance Benchmarks
//!
//! Comprehensive benchmarking suite for all neural network layers in the Qilin inference engine.
//! This benchmark suite measures:
//! - Forward pass performance across different input sizes
//! - Memory allocation patterns
//! - Parameter initialization performance
//! - Scaling characteristics with batch size and sequence length
//! - Comparison between different layer types and configurations

#[cfg(feature = "benchmarks")]
use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId, Throughput};

#[cfg(feature = "benchmarks")]
use qilin_inference::layers::*;
#[cfg(feature = "benchmarks")]
use qilin_inference::tensor::{
    Tensor, Shape, TensorOps, TensorFactory,
    cpu::{CpuTensor, CpuTensorFactory, PooledCpuTensorFactory},
    memory_pool::{TensorMemoryPool, MemoryPoolConfig, GlobalMemoryPool},
};

#[cfg(feature = "benchmarks")]
use std::time::Instant;

#[cfg(feature = "benchmarks")]
/// Benchmark configuration for different model sizes
struct BenchmarkConfig {
    name: &'static str,
    batch_size: usize,
    seq_len: usize,
    input_dim: usize,
    hidden_dim: usize,
    output_dim: usize,
}

#[cfg(feature = "benchmarks")]
const BENCHMARK_CONFIGS: &[BenchmarkConfig] = &[
    BenchmarkConfig {
        name: "tiny",
        batch_size: 1,
        seq_len: 128,
        input_dim: 256,
        hidden_dim: 512,
        output_dim: 256,
    },
    BenchmarkConfig {
        name: "small",
        batch_size: 4,
        seq_len: 512,
        input_dim: 512,
        hidden_dim: 1024,
        output_dim: 512,
    },
    BenchmarkConfig {
        name: "medium",
        batch_size: 8,
        seq_len: 1024,
        input_dim: 768,
        hidden_dim: 2048,
        output_dim: 768,
    },
    BenchmarkConfig {
        name: "large",
        batch_size: 16,
        seq_len: 2048,
        input_dim: 1024,
        hidden_dim: 4096,
        output_dim: 1024,
    },
];

#[cfg(feature = "benchmarks")]
fn bench_linear_layers(c: &mut Criterion) {
    let mut group = c.benchmark_group("linear_layers");
    
    for config in BENCHMARK_CONFIGS {
        let input_shape = Shape::new(vec![config.batch_size, config.seq_len, config.input_dim]);
        let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1).unwrap();
        
        // Standard linear layer
        let linear_config = LinearConfig::new(config.input_dim, config.hidden_dim);
        let mut linear = Linear::<f32>::new(linear_config).unwrap();
        linear.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        // Calculate throughput (elements processed per second)
        let elements = config.batch_size * config.seq_len * config.input_dim;
        group.throughput(Throughput::Elements(elements as u64));
        
        group.bench_with_input(
            BenchmarkId::new("forward", config.name),
            &input,
            |b, input| {
                b.iter(|| {
                    black_box(linear.forward(black_box(input.clone())).unwrap())
                })
            },
        );
        
        // Benchmark with bias disabled
        let linear_config_no_bias = LinearConfig::new(config.input_dim, config.hidden_dim)
            .with_bias(false);
        let mut linear_no_bias = Linear::<f32>::new(linear_config_no_bias).unwrap();
        linear_no_bias.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        group.bench_with_input(
            BenchmarkId::new("forward_no_bias", config.name),
            &input,
            |b, input| {
                b.iter(|| {
                    black_box(linear_no_bias.forward(black_box(input.clone())).unwrap())
                })
            },
        );
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
fn bench_normalization_layers(c: &mut Criterion) {
    let mut group = c.benchmark_group("normalization_layers");
    
    for config in BENCHMARK_CONFIGS {
        let input_shape = Shape::new(vec![config.batch_size, config.seq_len, config.input_dim]);
        let input = CpuTensorFactory::randn(&input_shape, 0.0, 1.0).unwrap();
        
        let elements = config.batch_size * config.seq_len * config.input_dim;
        group.throughput(Throughput::Elements(elements as u64));
        
        // LayerNorm
        let layernorm_config = LayerNormConfig::new(vec![config.input_dim]);
        let mut layernorm = LayerNorm::<f32>::new(layernorm_config).unwrap();
        layernorm.init_parameters(ParameterInit::Ones).unwrap();
        
        group.bench_with_input(
            BenchmarkId::new("layernorm", config.name),
            &input,
            |b, input| {
                b.iter(|| {
                    black_box(layernorm.forward(black_box(input.clone())).unwrap())
                })
            },
        );
        
        // RMSNorm
        let rmsnorm_config = RMSNormConfig::new(vec![config.input_dim]);
        let mut rmsnorm = RMSNorm::<f32>::new(rmsnorm_config).unwrap();
        rmsnorm.init_parameters(ParameterInit::Ones).unwrap();
        
        group.bench_with_input(
            BenchmarkId::new("rmsnorm", config.name),
            &input,
            |b, input| {
                b.iter(|| {
                    black_box(rmsnorm.forward(black_box(input.clone())).unwrap())
                })
            },
        );
        
        // BatchNorm (for 3D input: [batch, seq, features])
        let batchnorm_config = BatchNormConfig::new(config.input_dim);
        let mut batchnorm = BatchNorm::<f32>::new(batchnorm_config).unwrap();
        batchnorm.init_parameters(ParameterInit::Ones).unwrap();
        
        group.bench_with_input(
            BenchmarkId::new("batchnorm", config.name),
            &input,
            |b, input| {
                b.iter(|| {
                    black_box(batchnorm.forward(black_box(input.clone())).unwrap())
                })
            },
        );
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
fn bench_activation_functions(c: &mut Criterion) {
    let mut group = c.benchmark_group("activation_functions");
    
    let activation_types = [
        ActivationType::ReLU,
        ActivationType::GELU,
        ActivationType::Swish,
        ActivationType::Mish,
        ActivationType::Sigmoid,
        ActivationType::Tanh,
        ActivationType::LeakyReLU { negative_slope: 0.01 },
        ActivationType::ELU { alpha: 1.0 },
    ];
    
    for config in BENCHMARK_CONFIGS {
        let input_shape = Shape::new(vec![config.batch_size, config.seq_len, config.input_dim]);
        let input = CpuTensorFactory::randn(&input_shape, 0.0, 1.0).unwrap();
        
        let elements = config.batch_size * config.seq_len * config.input_dim;
        group.throughput(Throughput::Elements(elements as u64));
        
        for &activation_type in &activation_types {
            let activation_config = ActivationConfig::new(activation_type);
            let activation = Activation::<f32>::new(activation_config).unwrap();
            
            let activation_name = match activation_type {
                ActivationType::ReLU => "relu",
                ActivationType::GELU => "gelu",
                ActivationType::Swish => "swish",
                ActivationType::Mish => "mish",
                ActivationType::Sigmoid => "sigmoid",
                ActivationType::Tanh => "tanh",
                ActivationType::LeakyReLU { .. } => "leaky_relu",
                ActivationType::ELU { .. } => "elu",
                _ => "unknown",
            };
            
            group.bench_with_input(
                BenchmarkId::new(activation_name, config.name),
                &input,
                |b, input| {
                    b.iter(|| {
                        black_box(activation.forward(black_box(input.clone())).unwrap())
                    })
                },
            );
        }
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
fn bench_embedding_layers(c: &mut Criterion) {
    let mut group = c.benchmark_group("embedding_layers");
    
    let vocab_sizes = [1000, 10000, 50000];
    
    for config in BENCHMARK_CONFIGS {
        for &vocab_size in &vocab_sizes {
            // Create token IDs for embedding lookup
            let num_tokens = config.batch_size * config.seq_len;
            let token_ids: Vec<usize> = (0..num_tokens)
                .map(|i| i % vocab_size)
                .collect();
            
            let embedding_config = EmbeddingConfig::new(vocab_size, config.input_dim);
            let mut embedding = Embedding::<f32>::new(embedding_config).unwrap();
            embedding.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.02 }).unwrap();
            
            group.throughput(Throughput::Elements(num_tokens as u64));
            
            group.bench_with_input(
                BenchmarkId::new(format!("vocab_{}", vocab_size), config.name),
                &token_ids,
                |b, token_ids| {
                    b.iter(|| {
                        black_box(embedding.forward(black_box(token_ids.clone())).unwrap())
                    })
                },
            );
        }
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
fn bench_feedforward_networks(c: &mut Criterion) {
    let mut group = c.benchmark_group("feedforward_networks");
    
    for config in BENCHMARK_CONFIGS {
        let input_shape = Shape::new(vec![config.batch_size, config.seq_len, config.input_dim]);
        let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1).unwrap();
        
        let elements = config.batch_size * config.seq_len * config.input_dim;
        group.throughput(Throughput::Elements(elements as u64));
        
        // Standard FFN with GELU
        let ffn_config = FeedForwardConfig::new(config.input_dim, config.hidden_dim)
            .with_activation(ActivationType::GELU);
        let mut ffn = FeedForward::<f32>::new(ffn_config).unwrap();
        ffn.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        group.bench_with_input(
            BenchmarkId::new("standard_gelu", config.name),
            &input,
            |b, input| {
                b.iter(|| {
                    black_box(ffn.forward(black_box(input.clone())).unwrap())
                })
            },
        );
        
        // Gated FFN (SwiGLU)
        let gated_ffn_config = FeedForwardConfig::new(config.input_dim, config.hidden_dim)
            .with_gated(true)
            .with_activation(ActivationType::Swish);
        let mut gated_ffn = FeedForward::<f32>::new(gated_ffn_config).unwrap();
        gated_ffn.init_parameters(ParameterInit::XavierUniform).unwrap();
        
        group.bench_with_input(
            BenchmarkId::new("gated_swiglu", config.name),
            &input,
            |b, input| {
                b.iter(|| {
                    black_box(gated_ffn.forward(black_box(input.clone())).unwrap())
                })
            },
        );
    }
    
    group.finish();
}

#[cfg(feature = "benchmarks")]
criterion_group!(
    benches,
    bench_linear_layers,
    bench_normalization_layers,
    bench_activation_functions,
    bench_embedding_layers,
    bench_feedforward_networks
);

#[cfg(feature = "benchmarks")]
criterion_main!(benches);

#[cfg(not(feature = "benchmarks"))]
fn main() {
    println!("Neural network layer benchmarks require the 'benchmarks' feature to be enabled");
    println!("Run with: cargo bench --features benchmarks neural_network_layers");
}
