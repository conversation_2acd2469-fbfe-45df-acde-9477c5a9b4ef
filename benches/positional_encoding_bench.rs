//! Positional encoding benchmarks.

use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use qilin_inference::attention::positional::{
    PositionalEncoding, PositionalConfig,
    SinusoidalPositionalEncoding, LearnedPositionalEncoding,
    RelativePositionalEncoding, RotaryPositionalEncoding,
};
use qilin_inference::tensor::{Tensor, Shape};
use qilin_inference::tensor::cpu::{CpuTensor, CpuTensorFactory};
use qilin_inference::tensor::TensorFactory;

fn create_test_embeddings(batch_size: usize, seq_len: usize, hidden_size: usize) -> CpuTensor<f32> {
    CpuTensorFactory::ones(&Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap()
}

fn bench_sinusoidal_encoding(c: &mut Criterion) {
    let mut group = c.benchmark_group("sinusoidal_encoding");
    
    let config = PositionalConfig::new(2048, 512);
    let pos_enc = SinusoidalPositionalEncoding::<f32>::new(config).unwrap();
    
    for seq_len in [32, 128, 512, 1024].iter() {
        let embeddings = create_test_embeddings(2, *seq_len, 512);
        
        group.bench_with_input(
            BenchmarkId::new("encode", seq_len),
            seq_len,
            |b, &_seq_len| {
                b.iter(|| {
                    black_box(pos_enc.encode(black_box(&embeddings), None).unwrap())
                })
            },
        );
    }
    
    group.finish();
}

fn bench_learned_encoding(c: &mut Criterion) {
    let mut group = c.benchmark_group("learned_encoding");
    
    let config = PositionalConfig::new(2048, 512);
    let mut pos_enc = LearnedPositionalEncoding::<f32>::zeros(config).unwrap();
    
    // Initialize with some pattern
    let mut pos_data = vec![0.0; 2048 * 512];
    for i in 0..pos_data.len() {
        pos_data[i] = (i % 10) as f32 * 0.01;
    }
    let pos_embeddings = CpuTensor::from_data(pos_data, Shape::new(vec![2048, 512])).unwrap();
    pos_enc.update_embeddings(pos_embeddings).unwrap();
    
    for seq_len in [32, 128, 512, 1024].iter() {
        let embeddings = create_test_embeddings(2, *seq_len, 512);
        
        group.bench_with_input(
            BenchmarkId::new("encode", seq_len),
            seq_len,
            |b, &_seq_len| {
                b.iter(|| {
                    black_box(pos_enc.encode(black_box(&embeddings), None).unwrap())
                })
            },
        );
    }
    
    group.finish();
}

fn bench_relative_encoding(c: &mut Criterion) {
    let mut group = c.benchmark_group("relative_encoding");
    
    let config = PositionalConfig::new(2048, 512);
    let pos_enc = RelativePositionalEncoding::<f32>::new(config, 64).unwrap();
    
    for seq_len in [32, 128, 256].iter() { // Smaller sizes due to O(n²) complexity
        let embeddings = create_test_embeddings(2, *seq_len, 512);
        
        group.bench_with_input(
            BenchmarkId::new("encode", seq_len),
            seq_len,
            |b, &_seq_len| {
                b.iter(|| {
                    black_box(pos_enc.encode(black_box(&embeddings), None).unwrap())
                })
            },
        );
        
        group.bench_with_input(
            BenchmarkId::new("attention_encodings", seq_len),
            seq_len,
            |b, &seq_len| {
                b.iter(|| {
                    black_box(pos_enc.get_relative_encodings_for_attention(
                        black_box(seq_len), 
                        black_box(2)
                    ).unwrap())
                })
            },
        );
    }
    
    group.finish();
}

fn bench_rotary_encoding(c: &mut Criterion) {
    let mut group = c.benchmark_group("rotary_encoding");
    
    let config = PositionalConfig::new(2048, 512);
    let rope = RotaryPositionalEncoding::<f32>::new(config, 10000.0).unwrap();
    
    for seq_len in [32, 128, 512, 1024].iter() {
        let query = create_test_embeddings(2, *seq_len, 512);
        let key = create_test_embeddings(2, *seq_len, 512);
        
        group.bench_with_input(
            BenchmarkId::new("apply_rotation", seq_len),
            seq_len,
            |b, &_seq_len| {
                b.iter(|| {
                    black_box(rope.apply_rotary_encoding(
                        black_box(&query), 
                        black_box(&key), 
                        None
                    ).unwrap())
                })
            },
        );
    }
    
    group.finish();
}

fn bench_encoding_creation(c: &mut Criterion) {
    let mut group = c.benchmark_group("encoding_creation");
    
    let config = PositionalConfig::new(2048, 512);
    
    group.bench_function("sinusoidal_new", |b| {
        b.iter(|| {
            black_box(SinusoidalPositionalEncoding::<f32>::new(black_box(config.clone())).unwrap())
        })
    });
    
    group.bench_function("learned_new", |b| {
        b.iter(|| {
            black_box(LearnedPositionalEncoding::<f32>::zeros(black_box(config.clone())).unwrap())
        })
    });
    
    group.bench_function("relative_new", |b| {
        b.iter(|| {
            black_box(RelativePositionalEncoding::<f32>::new(black_box(config.clone()), 64).unwrap())
        })
    });
    
    group.bench_function("rotary_new", |b| {
        b.iter(|| {
            black_box(RotaryPositionalEncoding::<f32>::new(black_box(config.clone()), 10000.0).unwrap())
        })
    });
    
    group.finish();
}

fn bench_get_specific_encodings(c: &mut Criterion) {
    let mut group = c.benchmark_group("get_specific_encodings");
    
    let config = PositionalConfig::new(2048, 512);
    let sinusoidal = SinusoidalPositionalEncoding::<f32>::new(config.clone()).unwrap();
    let mut learned = LearnedPositionalEncoding::<f32>::zeros(config.clone()).unwrap();
    let rope = RotaryPositionalEncoding::<f32>::new(config, 10000.0).unwrap();
    
    // Initialize learned encoding
    let mut pos_data = vec![0.0; 2048 * 512];
    for i in 0..pos_data.len() {
        pos_data[i] = (i % 10) as f32 * 0.01;
    }
    let pos_embeddings = CpuTensor::from_data(pos_data, Shape::new(vec![2048, 512])).unwrap();
    learned.update_embeddings(pos_embeddings).unwrap();
    
    let positions = vec![0, 1, 5, 10, 20, 50, 100];
    
    group.bench_function("sinusoidal_get_encodings", |b| {
        b.iter(|| {
            black_box(sinusoidal.get_encodings(black_box(&positions), 512).unwrap())
        })
    });
    
    group.bench_function("learned_get_encodings", |b| {
        b.iter(|| {
            black_box(learned.get_encodings(black_box(&positions), 512).unwrap())
        })
    });
    
    group.bench_function("rotary_get_encodings", |b| {
        b.iter(|| {
            black_box(rope.get_encodings(black_box(&positions), 512).unwrap())
        })
    });
    
    group.finish();
}

fn bench_incremental_generation(c: &mut Criterion) {
    let mut group = c.benchmark_group("incremental_generation");
    
    let config = PositionalConfig::new(2048, 512);
    let sinusoidal = SinusoidalPositionalEncoding::<f32>::new(config.clone()).unwrap();
    let rope = RotaryPositionalEncoding::<f32>::new(config, 10000.0).unwrap();
    
    let embeddings = create_test_embeddings(1, 1, 512); // Single token
    let query = create_test_embeddings(1, 1, 512);
    let key = create_test_embeddings(1, 1, 512);
    
    for offset in [0, 10, 50, 100, 500].iter() {
        group.bench_with_input(
            BenchmarkId::new("sinusoidal_with_offset", offset),
            offset,
            |b, &offset| {
                b.iter(|| {
                    black_box(sinusoidal.encode(
                        black_box(&embeddings), 
                        Some(black_box(offset))
                    ).unwrap())
                })
            },
        );
        
        group.bench_with_input(
            BenchmarkId::new("rotary_with_offset", offset),
            offset,
            |b, &offset| {
                b.iter(|| {
                    black_box(rope.apply_rotary_encoding(
                        black_box(&query), 
                        black_box(&key), 
                        Some(black_box(offset))
                    ).unwrap())
                })
            },
        );
    }
    
    group.finish();
}

criterion_group!(
    benches,
    bench_sinusoidal_encoding,
    bench_learned_encoding,
    bench_relative_encoding,
    bench_rotary_encoding,
    bench_encoding_creation,
    bench_get_specific_encodings,
    bench_incremental_generation
);

criterion_main!(benches);
