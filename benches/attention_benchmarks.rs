//! Qilin注意力系统性能基准测试
//! 
//! 使用Criterion进行精确的性能测量和回归检测

use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use qilin_inference::attention::*;
use qilin_inference::attention::variants::*;
use qilin_inference::attention::positional::*;
use qilin_inference::tensor::cpu::CpuTensor;
use qilin_inference::tensor::{Tensor, TensorOps, Shape, TensorFactory};
use qilin_inference::tensor::cpu::CpuTensorFactory;
use qilin_inference::layers::ParameterInit;

/// 创建测试张量的辅助函数
fn create_test_tensor(batch_size: usize, seq_len: usize, hidden_size: usize) -> CpuTensor<f32> {
    CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0).unwrap()
}

/// 基准测试：多头注意力性能随序列长度变化
fn bench_multi_head_attention_seq_len(c: &mut Criterion) {
    let mut group = c.benchmark_group("multi_head_attention_seq_len");
    
    let hidden_size = 512;
    let num_heads = 8;
    let batch_size = 1;
    
    // 创建并初始化注意力层
    let config = AttentionConfig::new(hidden_size, num_heads);
    let mut mha = MultiHeadAttention::<f32>::new(config).unwrap();
    mha.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    for seq_len in [32, 64, 128, 256, 512].iter() {
        let input = create_test_tensor(batch_size, *seq_len, hidden_size);
        
        group.bench_with_input(
            BenchmarkId::new("forward", seq_len),
            seq_len,
            |b, _| {
                b.iter(|| {
                    let result = mha.forward(
                        black_box(&input),
                        black_box(&input),
                        black_box(&input),
                        None
                    );
                    black_box(result)
                });
            },
        );
    }
    
    group.finish();
}

/// 基准测试：多头注意力性能随批大小变化
fn bench_multi_head_attention_batch_size(c: &mut Criterion) {
    let mut group = c.benchmark_group("multi_head_attention_batch_size");
    
    let hidden_size = 512;
    let num_heads = 8;
    let seq_len = 128;
    
    let config = AttentionConfig::new(hidden_size, num_heads);
    let mut mha = MultiHeadAttention::<f32>::new(config).unwrap();
    mha.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    for batch_size in [1, 2, 4, 8, 16].iter() {
        let input = create_test_tensor(*batch_size, seq_len, hidden_size);
        
        group.bench_with_input(
            BenchmarkId::new("forward", batch_size),
            batch_size,
            |b, _| {
                b.iter(|| {
                    let result = mha.forward(
                        black_box(&input),
                        black_box(&input),
                        black_box(&input),
                        None
                    );
                    black_box(result)
                });
            },
        );
    }
    
    group.finish();
}

/// 基准测试：不同注意力变体的性能对比
fn bench_attention_variants(c: &mut Criterion) {
    let mut group = c.benchmark_group("attention_variants");
    
    let hidden_size = 512;
    let num_heads = 8;
    let seq_len = 128;
    let batch_size = 2;
    
    let input = create_test_tensor(batch_size, seq_len, hidden_size);
    
    // 自注意力
    let self_config = AttentionConfig::new(hidden_size, num_heads);
    let self_attn = SelfAttention::<f32>::new(self_config).unwrap();
    
    group.bench_function("self_attention", |b| {
        b.iter(|| {
            let result = self_attn.forward(black_box(&input), None, None);
            black_box(result)
        });
    });
    
    // 因果注意力
    let causal_config = AttentionConfig::causal(hidden_size, num_heads);
    let causal_attn = CausalAttention::<f32>::new(causal_config).unwrap();
    
    group.bench_function("causal_attention", |b| {
        b.iter(|| {
            let result = causal_attn.forward(black_box(&input), None, None);
            black_box(result)
        });
    });
    
    // 交叉注意力
    let cross_config = AttentionConfig::new(hidden_size, num_heads);
    let cross_attn = CrossAttention::<f32>::new(cross_config).unwrap();
    let context = create_test_tensor(batch_size, seq_len + 20, hidden_size);
    
    group.bench_function("cross_attention", |b| {
        b.iter(|| {
            let result = cross_attn.forward(black_box(&input), Some(black_box(&context)), None);
            black_box(result)
        });
    });
    
    group.finish();
}

/// 基准测试：位置编码性能
fn bench_positional_encodings(c: &mut Criterion) {
    let mut group = c.benchmark_group("positional_encodings");
    
    let hidden_size = 512;
    let seq_len = 256;
    let batch_size = 4;
    let num_heads = 8;
    let head_dim = hidden_size / num_heads;
    
    let embeddings = create_test_tensor(batch_size, seq_len, hidden_size);
    let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, num_heads, head_dim]), 0.0, 1.0).unwrap();
    let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, num_heads, head_dim]), 0.0, 1.0).unwrap();
    
    // 正弦位置编码
    let pos_config = PositionalConfig::new(seq_len, hidden_size);
    let sin_pos_enc = SinusoidalPositionalEncoding::<f32>::new(pos_config.clone()).unwrap();
    
    group.bench_function("sinusoidal_encoding", |b| {
        b.iter(|| {
            let result = sin_pos_enc.encode(black_box(&embeddings), None);
            black_box(result)
        });
    });
    
    // 旋转位置编码
    let rope = RotaryPositionalEncoding::<f32>::new(pos_config, 10000.0).unwrap();
    
    group.bench_function("rotary_encoding", |b| {
        b.iter(|| {
            let result = rope.apply_rotary_encoding(black_box(&query), black_box(&key), Some(0));
            black_box(result)
        });
    });
    
    group.finish();
}

/// 基准测试：KV缓存性能
fn bench_kv_cache(c: &mut Criterion) {
    let mut group = c.benchmark_group("kv_cache");
    
    let max_seq_len = 1024;
    let num_heads = 8;
    let head_dim = 64;
    let max_batch_size = 8;
    
    let cache_config = CacheConfig::new(max_seq_len, max_batch_size, num_heads, head_dim);
    let mut kv_cache = KVCache::new(cache_config).unwrap();
    
    // 存储操作
    let keys = CpuTensorFactory::randn(&Shape::new(vec![100, num_heads, head_dim]), 0.0, 1.0).unwrap();
    let values = CpuTensorFactory::randn(&Shape::new(vec![100, num_heads, head_dim]), 0.0, 1.0).unwrap();
    
    group.bench_function("store", |b| {
        b.iter(|| {
            let seq_id = format!("seq_{}", fastrand::u32(..));
            let result = kv_cache.store(&seq_id, black_box(keys.clone()), black_box(values.clone()));
            black_box(result)
        });
    });
    
    // 先存储一些数据用于检索测试
    for i in 0..10 {
        let seq_id = format!("bench_seq_{}", i);
        kv_cache.store(&seq_id, keys.clone(), values.clone()).unwrap();
    }
    
    // 检索操作
    group.bench_function("get", |b| {
        b.iter(|| {
            let seq_id = format!("bench_seq_{}", fastrand::usize(..10));
            let result = kv_cache.get(&seq_id);
            black_box(result)
        });
    });
    
    // 追加操作
    let new_keys = CpuTensorFactory::randn(&Shape::new(vec![1, num_heads, head_dim]), 0.0, 1.0).unwrap();
    let new_values = CpuTensorFactory::randn(&Shape::new(vec![1, num_heads, head_dim]), 0.0, 1.0).unwrap();
    
    group.bench_function("append", |b| {
        b.iter(|| {
            let seq_id = format!("bench_seq_{}", fastrand::usize(..10));
            let result = kv_cache.append(&seq_id, black_box(new_keys.clone()), black_box(new_values.clone()));
            black_box(result)
        });
    });
    
    group.finish();
}

/// 基准测试：完整的Transformer前向传播
fn bench_transformer_forward(c: &mut Criterion) {
    let mut group = c.benchmark_group("transformer_forward");
    
    let hidden_size = 512;
    let num_heads = 8;
    let seq_len = 128;
    let batch_size = 2;
    
    // 创建组件
    let config = AttentionConfig::new(hidden_size, num_heads).with_dropout(0.1);
    let mut mha = MultiHeadAttention::<f32>::new(config).unwrap();
    mha.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    let pos_config = PositionalConfig::new(seq_len, hidden_size);
    let pos_enc = SinusoidalPositionalEncoding::<f32>::new(pos_config).unwrap();
    
    let input = create_test_tensor(batch_size, seq_len, hidden_size);
    
    group.bench_function("complete_forward", |b| {
        b.iter(|| {
            // 位置编码
            let positioned = pos_enc.encode(black_box(&input), None).unwrap();
            
            // 注意力
            let (output, _) = mha.forward(
                black_box(&positioned),
                black_box(&positioned),
                black_box(&positioned),
                None
            ).unwrap();
            
            // 残差连接
            let result = positioned.add(&output).unwrap();
            black_box(result)
        });
    });
    
    group.finish();
}

/// 基准测试：内存使用效率
fn bench_memory_efficiency(c: &mut Criterion) {
    let mut group = c.benchmark_group("memory_efficiency");
    
    let hidden_size = 1024;
    let num_heads = 16;
    let batch_size = 1;
    
    let config = AttentionConfig::new(hidden_size, num_heads);
    let mut mha = MultiHeadAttention::<f32>::new(config).unwrap();
    mha.init_parameters(ParameterInit::XavierUniform).unwrap();
    
    // 测试不同序列长度下的内存分配模式
    for seq_len in [64, 128, 256, 512, 1024].iter() {
        let input = create_test_tensor(batch_size, *seq_len, hidden_size);
        
        group.bench_with_input(
            BenchmarkId::new("memory_pattern", seq_len),
            seq_len,
            |b, _| {
                b.iter(|| {
                    // 多次前向传播测试内存重用
                    for _ in 0..10 {
                        let result = mha.forward(
                            black_box(&input),
                            black_box(&input),
                            black_box(&input),
                            None
                        );
                        black_box(result);
                    }
                });
            },
        );
    }
    
    group.finish();
}

criterion_group!(
    benches,
    bench_multi_head_attention_seq_len,
    bench_multi_head_attention_batch_size,
    bench_attention_variants,
    bench_positional_encodings,
    bench_kv_cache,
    bench_transformer_forward,
    bench_memory_efficiency
);

criterion_main!(benches);
