//! Comprehensive Integration Tests for Qilin Inference Engine
//!
//! This test suite validates the integration and compatibility of all major components:
//! - Neural network layers (Linear, LayerNorm, Activation, etc.)
//! - Attention mechanisms (Multi-head, Causal, KV caching)
//! - Memory pool optimization
//! - SIMD optimizations
//! - Parallel operations
//! - End-to-end workflows

use qilin_inference::layers::*;
use qilin_inference::attention::*;
use qilin_inference::tensor::{
    Tensor, Shape, TensorOps, TensorFactory,
    cpu::{CpuTensor, CpuTensorFactory, PooledCpuTensorFactory},
    memory_pool::{TensorMemoryPool, MemoryPoolConfig, GlobalMemoryPool},
};

#[cfg(feature = "simd")]
use qilin_inference::tensor::simd_nn::SimdNeuralOptimized;

use qilin_inference::tensor::parallel::{<PERSON>llel<PERSON><PERSON>, ParallelConfig};
use qilin_inference::error::{TensorError, LayerError};
use std::time::Instant;

/// Test complete transformer block integration
#[test]
fn test_transformer_block_integration() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Transformer Block Integration...");
    
    let batch_size = 2;
    let seq_len = 8;
    let hidden_dim = 256;
    let num_heads = 8;
    let ff_dim = 1024;
    
    // Create input tensor
    let input_shape = Shape::new(vec![batch_size, seq_len, hidden_dim]);
    let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1)?;
    
    // 1. Multi-head self-attention
    let attn_config = AttentionConfig::new(hidden_dim, num_heads);
    let mut attention = MultiHeadAttention::new(attn_config)?;
    attention.init_parameters(ParameterInit::XavierUniform)?;
    
    let attn_output = attention.forward(input.clone())?;
    assert_eq!(attn_output.shape(), &input_shape);
    println!("  ✓ Multi-head attention: {} -> {}", 
             format_shape(input.shape()), format_shape(attn_output.shape()));
    
    // 2. Add & Norm (residual connection + layer norm)
    let residual = input.add(&attn_output)?;
    
    let norm_config = LayerNormConfig::new(vec![hidden_dim]);
    let mut layer_norm1 = LayerNorm::new(norm_config.clone())?;
    layer_norm1.init_parameters(ParameterInit::Ones)?;
    
    let norm_output1 = layer_norm1.forward(residual)?;
    assert_eq!(norm_output1.shape(), &input_shape);
    println!("  ✓ Add & Norm 1: {} -> {}", 
             format_shape(&input_shape), format_shape(norm_output1.shape()));
    
    // 3. Feed-forward network
    let ff_config = FeedForwardConfig::new(hidden_dim, ff_dim)
        .with_activation(ActivationType::GELU);
    let mut feedforward = FeedForward::new(ff_config)?;
    feedforward.init_parameters(ParameterInit::XavierUniform)?;
    
    let ff_output = feedforward.forward(norm_output1.clone())?;
    assert_eq!(ff_output.shape(), &input_shape);
    println!("  ✓ Feed-forward: {} -> {}", 
             format_shape(norm_output1.shape()), format_shape(ff_output.shape()));
    
    // 4. Add & Norm (second residual connection)
    let residual2 = norm_output1.add(&ff_output)?;
    
    let mut layer_norm2 = LayerNorm::new(norm_config)?;
    layer_norm2.init_parameters(ParameterInit::Ones)?;
    
    let final_output = layer_norm2.forward(residual2)?;
    assert_eq!(final_output.shape(), &input_shape);
    println!("  ✓ Add & Norm 2: {} -> {}", 
             format_shape(&input_shape), format_shape(final_output.shape()));
    
    // Verify output is reasonable (not NaN or infinite)
    let output_data = final_output.data();
    assert!(output_data.iter().all(|&x| x.is_finite()), "Output contains non-finite values");
    
    println!("  ✓ Complete transformer block integration successful!");
    Ok(())
}

/// Test memory pool integration with neural network layers
#[test]
fn test_memory_pool_layer_integration() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Memory Pool Integration with Layers...");
    
    let config = MemoryPoolConfig {
        enable_stats: true,
        enable_size_bucketing: true,
        preallocation_sizes: vec![256, 512, 1024],
        ..Default::default()
    };
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Test pooled tensor creation
    let shape = Shape::new(vec![4, 64]);
    let tensor1 = PooledCpuTensorFactory::<f32>::zeros(&shape)?;
    let tensor2 = PooledCpuTensorFactory::<f32>::ones(&shape)?;
    
    println!("  ✓ Created pooled tensors: {} and {}", 
             format_shape(tensor1.shape()), format_shape(tensor2.shape()));
    
    // Test layer operations with pooled tensors
    let linear_config = LinearConfig::new(64, 32);
    let mut linear = Linear::new(linear_config)?;
    linear.init_parameters(ParameterInit::XavierUniform)?;
    
    let output = linear.forward(tensor1)?;
    assert_eq!(output.shape().dims(), &[4, 32]);
    
    // Check pool statistics
    let f32_pool = GlobalMemoryPool::f32();
    let stats = f32_pool.stats();
    println!("  ✓ Pool stats: {} allocations, {:.1}% hit rate", 
             stats.total_allocations, stats.hit_rate());
    
    println!("  ✓ Memory pool integration with layers successful!");
    Ok(())
}

/// Test SIMD optimization integration with layers
#[cfg(feature = "simd")]
#[test]
fn test_simd_layer_integration() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing SIMD Integration with Layers...");
    
    let size = 1024;
    let input_data: Vec<f32> = (0..size).map(|i| (i as f32) * 0.001 - 0.5).collect();
    let shape = Shape::new(vec![1, size]);
    let input = CpuTensor::from_data(input_data.clone(), shape.clone())?;
    
    // Test SIMD activation functions
    let mut simd_output = vec![0.0; size];
    
    // ReLU
    input_data.simd_relu(&mut simd_output);
    let relu_tensor = CpuTensor::from_data(simd_output.clone(), shape.clone())?;
    println!("  ✓ SIMD ReLU: {} -> {}", format_shape(&shape), format_shape(relu_tensor.shape()));
    
    // GELU
    input_data.simd_gelu(&mut simd_output);
    let gelu_tensor = CpuTensor::from_data(simd_output.clone(), shape.clone())?;
    println!("  ✓ SIMD GELU: {} -> {}", format_shape(&shape), format_shape(gelu_tensor.shape()));
    
    // Test SIMD with layer operations
    let activation = Activation::new(ActivationType::ReLU);
    let layer_output = activation.forward(input)?;
    
    // Compare SIMD vs layer implementation (should be similar)
    let layer_data = layer_output.data();
    let max_diff = layer_data.iter().zip(relu_tensor.data().iter())
        .map(|(a, b)| (a - b).abs())
        .fold(0.0, f32::max);
    
    assert!(max_diff < 1e-5, "SIMD and layer ReLU results differ too much: {}", max_diff);
    println!("  ✓ SIMD vs Layer ReLU max difference: {:.2e}", max_diff);
    
    println!("  ✓ SIMD integration with layers successful!");
    Ok(())
}

/// Test attention mechanism with KV caching integration
#[test]
fn test_attention_kv_cache_integration() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Attention with KV Cache Integration...");
    
    let hidden_dim = 128;
    let num_heads = 4;
    let seq_len = 6;
    let batch_size = 2;
    
    // Create incremental attention with caching
    let attn_config = AttentionConfig::new(hidden_dim, num_heads);
    let mut incremental_attn = IncrementalAttention::new(attn_config)?;
    incremental_attn.init_parameters(ParameterInit::XavierUniform)?;
    
    // First step: process initial sequence
    let input_shape = Shape::new(vec![batch_size, seq_len, hidden_dim]);
    let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1)?;
    
    let output1 = incremental_attn.forward(input.clone())?;
    assert_eq!(output1.shape(), &input_shape);
    println!("  ✓ Initial attention: {} -> {}", 
             format_shape(&input_shape), format_shape(output1.shape()));
    
    // Second step: process one more token (should use cache)
    let new_token_shape = Shape::new(vec![batch_size, 1, hidden_dim]);
    let new_token = CpuTensorFactory::randn(&new_token_shape, 0.0, 0.1)?;
    
    let output2 = incremental_attn.forward(new_token.clone())?;
    assert_eq!(output2.shape(), &new_token_shape);
    println!("  ✓ Incremental attention: {} -> {}", 
             format_shape(&new_token_shape), format_shape(output2.shape()));
    
    // Verify cache statistics
    let cache_stats = incremental_attn.cache_stats();
    assert!(cache_stats.total_hits > 0, "Cache should have hits");
    println!("  ✓ Cache stats: {} hits, {} misses", 
             cache_stats.total_hits, cache_stats.total_misses);
    
    println!("  ✓ Attention with KV cache integration successful!");
    Ok(())
}

/// Test parallel operations integration
#[test]
fn test_parallel_operations_integration() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Parallel Operations Integration...");
    
    let config = ParallelConfig::new(1000, None, 256); // Low threshold for testing
    let size = 10000;
    
    // Create large tensors for parallel processing
    let a: Vec<f32> = (0..size).map(|i| i as f32 * 0.001).collect();
    let b: Vec<f32> = (0..size).map(|i| (i as f32) * 0.002).collect();
    let mut result = vec![0.0; size];
    
    // Test parallel addition
    let start = Instant::now();
    ParallelOps::add_f32_parallel(&a, &b, &mut result, &config);
    let parallel_time = start.elapsed();
    
    // Verify correctness
    for i in 0..100 { // Check first 100 elements
        let expected = a[i] + b[i];
        assert!((result[i] - expected).abs() < 1e-6, 
               "Parallel addition incorrect at index {}", i);
    }
    
    println!("  ✓ Parallel addition: {} elements in {:?}", size, parallel_time);
    
    // Test with tensor operations
    let tensor_a = CpuTensor::from_data(a, Shape::new(vec![size]))?;
    let tensor_b = CpuTensor::from_data(b, Shape::new(vec![size]))?;
    
    let start = Instant::now();
    let tensor_result = tensor_a.add(&tensor_b)?;
    let tensor_time = start.elapsed();
    
    println!("  ✓ Tensor addition: {} elements in {:?}", size, tensor_time);
    
    println!("  ✓ Parallel operations integration successful!");
    Ok(())
}

/// Test end-to-end workflow with all components
#[test]
fn test_end_to_end_workflow() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing End-to-End Workflow...");
    
    let batch_size = 1;
    let seq_len = 4;
    let vocab_size = 1000;
    let hidden_dim = 128;
    let num_heads = 4;
    
    // 1. Token embedding
    let embedding_config = EmbeddingConfig::new(vocab_size, hidden_dim);
    let mut embedding = Embedding::new(embedding_config)?;
    embedding.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.02 })?;
    
    // Input token IDs
    let token_ids = vec![10, 25, 100, 50]; // Example token sequence
    let tokens_shape = Shape::new(vec![batch_size, seq_len]);
    let tokens_tensor = CpuTensor::from_data(
        token_ids.iter().map(|&x| x as f32).collect(), 
        tokens_shape
    )?;
    
    let embedded = embedding.forward(tokens_tensor)?;
    let expected_shape = Shape::new(vec![batch_size, seq_len, hidden_dim]);
    assert_eq!(embedded.shape(), &expected_shape);
    println!("  ✓ Token embedding: {} tokens -> {}", 
             seq_len, format_shape(embedded.shape()));
    
    // 2. Positional encoding
    let pos_config = PositionalConfig::sinusoidal(hidden_dim, seq_len);
    let pos_encoding = SinusoidalPositionalEncoding::new(pos_config)?;
    
    let positioned = pos_encoding.forward(embedded)?;
    assert_eq!(positioned.shape(), &expected_shape);
    println!("  ✓ Positional encoding: {} -> {}", 
             format_shape(&expected_shape), format_shape(positioned.shape()));
    
    // 3. Self-attention
    let attn_config = AttentionConfig::new(hidden_dim, num_heads);
    let mut self_attention = SelfAttention::new(attn_config)?;
    self_attention.init_parameters(ParameterInit::XavierUniform)?;
    
    let attended = self_attention.forward(positioned.clone())?;
    assert_eq!(attended.shape(), &expected_shape);
    println!("  ✓ Self-attention: {} -> {}", 
             format_shape(positioned.shape()), format_shape(attended.shape()));
    
    // 4. Feed-forward with residual connection
    let ff_config = FeedForwardConfig::new(hidden_dim, hidden_dim * 4)
        .with_activation(ActivationType::GELU);
    let mut feedforward = FeedForward::new(ff_config)?;
    feedforward.init_parameters(ParameterInit::XavierUniform)?;
    
    let ff_output = feedforward.forward(attended.clone())?;
    let final_output = attended.add(&ff_output)?; // Residual connection
    
    assert_eq!(final_output.shape(), &expected_shape);
    println!("  ✓ Feed-forward + residual: {} -> {}", 
             format_shape(attended.shape()), format_shape(final_output.shape()));
    
    // 5. Final verification
    let output_data = final_output.data();
    assert!(output_data.iter().all(|&x| x.is_finite()), "Output contains non-finite values");
    
    let mean = output_data.iter().sum::<f32>() / output_data.len() as f32;
    let variance = output_data.iter().map(|&x| (x - mean).powi(2)).sum::<f32>() / output_data.len() as f32;
    
    println!("  ✓ Output statistics: mean={:.4}, variance={:.4}", mean, variance);
    println!("  ✓ End-to-end workflow successful!");
    
    Ok(())
}

// Helper function to format shape for display
fn format_shape(shape: &Shape) -> String {
    format!("[{}]", shape.dims().iter().map(|d| d.to_string()).collect::<Vec<_>>().join(", "))
}

/// Test error handling and recovery across components
#[test]
fn test_error_handling_integration() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Error Handling Integration...");

    // Test shape mismatch errors
    let input = CpuTensorFactory::zeros(&Shape::new(vec![2, 64]))?;
    let linear_config = LinearConfig::new(32, 16); // Wrong input size
    let mut linear = Linear::new(linear_config)?;
    linear.init_parameters(ParameterInit::XavierUniform)?;

    let result = linear.forward(input);
    assert!(result.is_err(), "Should fail with shape mismatch");

    match result {
        Err(TensorError::ShapeMismatch { expected, actual, .. }) => {
            println!("  ✓ Caught shape mismatch: expected {:?}, got {:?}", expected, actual);
        }
        _ => panic!("Expected ShapeMismatch error"),
    }

    // Test attention dimension mismatch
    let attn_config = AttentionConfig::new(128, 8);
    let mut attention = MultiHeadAttention::new(attn_config)?;
    attention.init_parameters(ParameterInit::XavierUniform)?;

    let wrong_input = CpuTensorFactory::zeros(&Shape::new(vec![2, 4, 64]))?; // Wrong hidden dim
    let attn_result = attention.forward(wrong_input);
    assert!(attn_result.is_err(), "Should fail with dimension mismatch");

    println!("  ✓ Error handling integration successful!");
    Ok(())
}

/// Test component compatibility across different data types
#[test]
fn test_multi_type_compatibility() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Multi-Type Compatibility...");

    // Test f32 components
    let f32_shape = Shape::new(vec![2, 32]);
    let f32_input = CpuTensorFactory::<f32>::randn(&f32_shape, 0.0, 1.0)?;

    let f32_linear_config = LinearConfig::new(32, 16);
    let mut f32_linear = Linear::<f32>::new(f32_linear_config)?;
    f32_linear.init_parameters(ParameterInit::XavierUniform)?;

    let f32_output = f32_linear.forward(f32_input)?;
    assert_eq!(f32_output.shape().dims(), &[2, 16]);
    println!("  ✓ f32 linear layer: [2, 32] -> [2, 16]");

    // Test f64 components
    let f64_shape = Shape::new(vec![2, 32]);
    let f64_input = CpuTensorFactory::<f64>::randn(&f64_shape, 0.0, 1.0)?;

    let f64_linear_config = LinearConfig::new(32, 16);
    let mut f64_linear = Linear::<f64>::new(f64_linear_config)?;
    f64_linear.init_parameters(ParameterInit::XavierUniform)?;

    let f64_output = f64_linear.forward(f64_input)?;
    assert_eq!(f64_output.shape().dims(), &[2, 16]);
    println!("  ✓ f64 linear layer: [2, 32] -> [2, 16]");

    // Test memory pools for different types
    let f32_pool = GlobalMemoryPool::f32();
    let f64_pool = GlobalMemoryPool::f64();

    let f32_buffer = f32_pool.allocate(100)?;
    let f64_buffer = f64_pool.allocate(100)?;

    assert_eq!(f32_buffer.len(), 100);
    assert_eq!(f64_buffer.len(), 100);

    f32_pool.deallocate(f32_buffer);
    f64_pool.deallocate(f64_buffer);

    println!("  ✓ Multi-type memory pools working correctly");
    println!("  ✓ Multi-type compatibility successful!");
    Ok(())
}

/// Test performance scaling with different batch sizes
#[test]
fn test_performance_scaling() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Performance Scaling...");

    let hidden_dim = 256;
    let num_heads = 8;
    let seq_len = 16;

    // Test different batch sizes
    let batch_sizes = vec![1, 2, 4, 8];
    let mut times = Vec::new();

    for &batch_size in &batch_sizes {
        let shape = Shape::new(vec![batch_size, seq_len, hidden_dim]);
        let input = CpuTensorFactory::randn(&shape, 0.0, 0.1)?;

        let attn_config = AttentionConfig::new(hidden_dim, num_heads);
        let mut attention = MultiHeadAttention::new(attn_config)?;
        attention.init_parameters(ParameterInit::XavierUniform)?;

        let start = Instant::now();
        let _output = attention.forward(input)?;
        let elapsed = start.elapsed();

        times.push(elapsed);
        println!("  ✓ Batch size {}: {:?}", batch_size, elapsed);
    }

    // Verify that larger batches don't scale linearly (should be more efficient)
    let single_time = times[0].as_nanos() as f64;
    let quad_time = times[2].as_nanos() as f64;
    let efficiency = single_time * 4.0 / quad_time;

    println!("  ✓ Batch efficiency (4x vs 1x): {:.2}x", efficiency);
    assert!(efficiency > 1.5, "Batching should provide some efficiency gain");

    println!("  ✓ Performance scaling test successful!");
    Ok(())
}
