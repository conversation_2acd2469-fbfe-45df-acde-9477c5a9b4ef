//! Comprehensive tests for memory pool optimization features.

use qilin_inference::tensor::memory_pool::{TensorMemoryPool, MemoryPoolConfig, GlobalMemoryPool};
use qilin_inference::tensor::cpu::PooledCpuTensorFactory;
use qilin_inference::tensor::{TensorFactory, Shape};

#[test]
fn test_enhanced_memory_pool_config() {
    let config = MemoryPoolConfig {
        max_buffers_per_bucket: 5,
        max_total_memory: 1024 * 1024, // 1MB
        enable_stats: true,
        min_pooled_size: 64,
        enable_size_bucketing: true,
        preallocation_sizes: vec![128, 256, 512],
        max_buffer_age: 300,
        enable_compaction: true,
    };
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Test preallocation
    let stats = pool.stats();
    assert!(stats.current_pooled_buffers > 0, "Should have preallocated buffers");
    assert!(stats.current_pooled_memory > 0, "Should have preallocated memory");
    assert!(stats.peak_memory_usage > 0, "Should track peak memory usage");
}

#[test]
fn test_size_bucketing() {
    let config = MemoryPoolConfig {
        enable_size_bucketing: true,
        enable_stats: true,
        ..Default::default()
    };
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Allocate buffers with sizes that should be bucketed
    let buffer1 = pool.allocate(100).unwrap();
    let buffer2 = pool.allocate(150).unwrap();
    
    // Return buffers
    pool.deallocate(buffer1);
    pool.deallocate(buffer2);
    
    // Allocate similar sizes - should get hits due to bucketing
    let _buffer3 = pool.allocate(120).unwrap();
    let _buffer4 = pool.allocate(180).unwrap();
    
    let stats = pool.stats();
    assert!(stats.pool_hits > 0, "Should have pool hits due to bucketing");
}

#[test]
fn test_batch_operations() {
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::default();
    
    // Test batch allocation
    let buffers = pool.allocate_batch(1024, 5).unwrap();
    assert_eq!(buffers.len(), 5, "Should allocate 5 buffers");
    assert!(buffers.iter().all(|b| b.len() == 1024), "All buffers should have correct size");
    
    // Test batch deallocation
    pool.deallocate_batch(buffers);
    
    let stats = pool.stats();
    assert_eq!(stats.total_allocations, 5, "Should track batch allocations");
    assert_eq!(stats.total_deallocations, 5, "Should track batch deallocations");
}

#[test]
fn test_buffer_aging_cleanup() {
    let config = MemoryPoolConfig {
        max_buffer_age: 1, // 1 second for testing
        enable_stats: true,
        enable_compaction: true,
        ..Default::default()
    };
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Allocate and return some buffers
    let buffer = pool.allocate(1024).unwrap();
    pool.deallocate(buffer);
    
    let stats_before = pool.stats();
    assert!(stats_before.current_pooled_buffers > 0, "Should have pooled buffers");
    
    // Wait for buffers to age
    std::thread::sleep(std::time::Duration::from_secs(2));
    
    // Trigger cleanup
    pool.cleanup_old_buffers();
    
    let stats_after = pool.stats();
    assert!(stats_after.age_cleanups > 0, "Should have performed age cleanups");
}

#[test]
fn test_memory_pool_statistics() {
    let config = MemoryPoolConfig {
        enable_stats: true,
        ..Default::default()
    };
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Perform some operations
    let buffer1 = pool.allocate(512).unwrap();
    let buffer2 = pool.allocate(1024).unwrap();
    
    pool.deallocate(buffer1);
    pool.deallocate(buffer2);
    
    // Allocate again to get hits
    let _buffer3 = pool.allocate(512).unwrap();
    let _buffer4 = pool.allocate(1024).unwrap();
    
    let stats = pool.stats();
    
    // Test statistics calculations
    assert!(stats.hit_rate() > 0.0, "Should have positive hit rate");
    assert!(stats.return_rate() > 0.0, "Should have positive return rate");
    assert!(stats.avg_allocation_size() > 0.0, "Should have positive average allocation size");
    assert!(!stats.summary().is_empty(), "Should have non-empty summary");
    
    // Test that all counters are reasonable
    assert!(stats.total_allocations >= 4, "Should track all allocations");
    assert!(stats.pool_hits >= 2, "Should have at least 2 hits");
    assert!(stats.pool_returns >= 2, "Should have at least 2 returns");
}

#[test]
fn test_global_memory_pools() {
    // Test f32 global pool
    let f32_pool = GlobalMemoryPool::f32();
    let buffer1 = f32_pool.allocate(256).unwrap();
    assert_eq!(buffer1.len(), 256, "f32 buffer should have correct size");
    f32_pool.deallocate(buffer1);
    
    // Test f64 global pool
    let f64_pool = GlobalMemoryPool::f64();
    let buffer2 = f64_pool.allocate(128).unwrap();
    assert_eq!(buffer2.len(), 128, "f64 buffer should have correct size");
    f64_pool.deallocate(buffer2);
    
    // Test that pools are singletons
    let f32_pool2 = GlobalMemoryPool::f32();
    assert!(std::ptr::eq(f32_pool, f32_pool2), "f32 pools should be the same instance");
}

#[test]
fn test_pooled_tensor_factory() {
    let shape = Shape::new(vec![16, 16]);
    
    // Test zeros creation
    let zeros_tensor = PooledCpuTensorFactory::<f32>::zeros(&shape).unwrap();
    assert_eq!(zeros_tensor.shape(), &shape, "Zeros tensor should have correct shape");
    
    // Test ones creation
    let ones_tensor = PooledCpuTensorFactory::<f32>::ones(&shape).unwrap();
    assert_eq!(ones_tensor.shape(), &shape, "Ones tensor should have correct shape");
    
    // Test full creation
    let full_tensor = PooledCpuTensorFactory::<f32>::full(&shape, 2.5).unwrap();
    assert_eq!(full_tensor.shape(), &shape, "Full tensor should have correct shape");
    
    // Test from_slice creation
    let data: Vec<f32> = (0..256).map(|i| i as f32).collect();
    let slice_tensor = PooledCpuTensorFactory::<f32>::from_slice(&data, &shape).unwrap();
    assert_eq!(slice_tensor.shape(), &shape, "Slice tensor should have correct shape");
    
    // Test from_vec creation
    let data: Vec<f32> = (0..256).map(|i| i as f32 * 0.5).collect();
    let vec_tensor = PooledCpuTensorFactory::<f32>::from_vec(data, &shape).unwrap();
    assert_eq!(vec_tensor.shape(), &shape, "Vec tensor should have correct shape");
}

#[test]
fn test_memory_pool_limits() {
    let config = MemoryPoolConfig {
        max_buffers_per_bucket: 2,
        max_total_memory: 1024, // Very small limit
        enable_stats: true,
        ..Default::default()
    };
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Allocate and return buffers to fill the pool
    let buffer1 = pool.allocate(64).unwrap();
    let buffer2 = pool.allocate(64).unwrap();
    let buffer3 = pool.allocate(64).unwrap();
    
    pool.deallocate(buffer1);
    pool.deallocate(buffer2);
    pool.deallocate(buffer3); // This should be discarded due to limits
    
    let stats = pool.stats();
    assert!(stats.pool_discards > 0, "Should have discarded buffers due to limits");
    assert!(stats.current_pooled_buffers <= 2, "Should respect max buffers per bucket");
}

#[test]
fn test_memory_pool_thread_safety() {
    use std::sync::Arc;
    use std::thread;
    
    let pool = Arc::new(TensorMemoryPool::<f32>::default());
    let mut handles = Vec::new();
    
    // Spawn multiple threads that use the pool
    for i in 0..4 {
        let pool_clone = Arc::clone(&pool);
        let handle = thread::spawn(move || {
            let size = 256 + i * 64;
            for _ in 0..10 {
                let buffer = pool_clone.allocate(size).unwrap();
                // Do some work with the buffer
                assert_eq!(buffer.len(), size);
                pool_clone.deallocate(buffer);
            }
        });
        handles.push(handle);
    }
    
    // Wait for all threads to complete
    for handle in handles {
        handle.join().unwrap();
    }
    
    let stats = pool.stats();
    assert_eq!(stats.total_allocations, 40, "Should track all allocations from all threads");
    assert_eq!(stats.total_deallocations, 40, "Should track all deallocations from all threads");
}

#[test]
fn test_memory_efficiency_calculation() {
    let config = MemoryPoolConfig {
        enable_stats: true,
        ..Default::default()
    };
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Allocate some memory to establish peak usage
    let buffer = pool.allocate(1024).unwrap();
    pool.deallocate(buffer);
    
    let stats = pool.stats();
    let efficiency = stats.memory_efficiency();
    
    assert!(efficiency >= 0.0 && efficiency <= 1.0, "Memory efficiency should be between 0 and 1");
    
    if stats.peak_memory_usage > 0 {
        assert!(efficiency > 0.0, "Should have positive efficiency when memory is pooled");
    }
}
