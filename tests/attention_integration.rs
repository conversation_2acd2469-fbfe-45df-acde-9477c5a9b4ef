//! Integration tests for the attention mechanism system.
//!
//! These tests verify end-to-end functionality including:
//! - Complete attention workflows with all components
//! - Multi-head attention with KV caching
//! - Positional encoding integration
//! - Performance benchmarks
//! - Memory usage validation

use qilin_inference::attention::{
    AttentionConfig, Attention,
    ScaledDotProductAttention, MultiHeadAttention,
    KVCache, CacheConfig,
    SelfAttention, CrossAttention, CausalAttention, AttentionVariant,
};
use qilin_inference::attention::positional::{
    PositionalEncoding, PositionalConfig,
    SinusoidalPositionalEncoding, LearnedPositionalEncoding,
    RelativePositionalEncoding, RotaryPositionalEncoding,
};
use qilin_inference::tensor::{Tensor, Shape};
use qilin_inference::tensor::cpu::CpuTensor;
use qilin_inference::error::AttentionError;
use qilin_inference::layers::{ParameterizedLayer, ParameterInit};
use std::time::Instant;

/// Helper function to create test tensors with specific patterns
fn create_test_tensor(batch_size: usize, seq_len: usize, hidden_size: usize, pattern: f32) -> CpuTensor<f32> {
    let total_size = batch_size * seq_len * hidden_size;
    let mut data = vec![0.0; total_size];
    for (i, val) in data.iter_mut().enumerate() {
        *val = pattern * (i as f32 + 1.0) / (total_size as f32);
    }
    CpuTensor::from_data(data, Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap()
}

/// Helper function to create realistic embeddings with sinusoidal patterns
fn create_realistic_embeddings(batch_size: usize, seq_len: usize, hidden_size: usize) -> CpuTensor<f32> {
    let mut data = vec![0.0f32; batch_size * seq_len * hidden_size];

    for b in 0..batch_size {
        for s in 0..seq_len {
            for h in 0..hidden_size {
                let idx = b * seq_len * hidden_size + s * hidden_size + h;
                // Create realistic embedding patterns
                data[idx] = (s as f32 * 0.1 + h as f32 * 0.01).sin() * 0.5 +
                           (b as f32 * 0.05 + h as f32 * 0.02).cos() * 0.3;
            }
        }
    }

    CpuTensor::from_data(data, Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap()
}

#[cfg(test)]
mod end_to_end_tests {
    use super::*;

    #[test]
    fn test_complete_transformer_attention_workflow() {
        // Configuration for a small transformer block
        let batch_size = 4;
        let seq_len = 16;
        let hidden_size = 256;
        let num_heads = 8;

        // Create multi-head attention
        let config = AttentionConfig::new(hidden_size, num_heads).with_dropout(0.1);
        let mut mha = MultiHeadAttention::<f32>::new(config).unwrap();

        // Initialize parameters
        mha.init_parameters(ParameterInit::XavierUniform).unwrap();

        // Create positional encoding
        let pos_config = PositionalConfig::new(512, hidden_size);
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(pos_config).unwrap();

        // Create realistic input embeddings
        let embeddings = create_realistic_embeddings(batch_size, seq_len, hidden_size);

        // Apply positional encoding
        let positioned_embeddings = pos_enc.encode(&embeddings, None).unwrap();

        // Apply self-attention
        let (output, attention_weights) = mha.forward(
            &positioned_embeddings,
            &positioned_embeddings,
            &positioned_embeddings,
            None
        ).unwrap();

        // Verify output shape
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
        assert_eq!(attention_weights.shape().dims(), &[batch_size, num_heads, seq_len, seq_len]);

        // Verify output is not all zeros
        let output_data = output.data();
        let non_zero_count = output_data.iter().filter(|&&x| x.abs() > 1e-6).count();
        assert!(non_zero_count > output_data.len() / 2, "Output should have significant non-zero values");

        // Verify attention weights sum to 1 (approximately)
        let weights_data = attention_weights.data();
        for b in 0..batch_size {
            for h in 0..num_heads {
                for q in 0..seq_len {
                    let mut sum = 0.0;
                    for k in 0..seq_len {
                        let idx = b * num_heads * seq_len * seq_len +
                                 h * seq_len * seq_len +
                                 q * seq_len + k;
                        sum += weights_data[idx];
                    }
                    assert!((sum - 1.0).abs() < 1e-4, "Attention weights should sum to 1, got {}", sum);
                }
            }
        }
    }
    
    #[test]
    fn test_encoder_decoder_cross_attention() {
        let batch_size = 2;
        let encoder_seq_len = 12;
        let decoder_seq_len = 8;
        let hidden_size = 128;
        let num_heads = 4;

        // Create cross-attention mechanism
        let config = AttentionConfig::new(hidden_size, num_heads);
        let cross_attn = CrossAttention::<f32>::new(config).unwrap();

        // Create encoder and decoder embeddings
        let encoder_embeddings = create_realistic_embeddings(batch_size, encoder_seq_len, hidden_size);
        let decoder_embeddings = create_realistic_embeddings(batch_size, decoder_seq_len, hidden_size);

        // Apply cross-attention: decoder queries attend to encoder keys/values
        let output = cross_attn.forward(
            &decoder_embeddings,  // queries from decoder
            Some(&encoder_embeddings),  // context from encoder
            None  // mask
        ).unwrap();

        // Verify output shape matches decoder sequence
        assert_eq!(output.shape().dims(), &[batch_size, decoder_seq_len, hidden_size]);

        // Verify output is meaningful
        let output_data = output.data();
        let non_zero_count = output_data.iter().filter(|&&x| x.abs() > 1e-6).count();
        assert!(non_zero_count > 0, "Cross-attention output should not be all zeros");
    }

    #[test]
    fn test_causal_attention_autoregressive_generation() {
        let batch_size = 1;
        let seq_len = 10;
        let hidden_size = 64;
        let num_heads = 4;

        // Create causal attention
        let config = AttentionConfig::causal(hidden_size, num_heads);
        let causal_attn = CausalAttention::<f32>::new(config).unwrap();

        // Create input sequence
        let input = create_realistic_embeddings(batch_size, seq_len, hidden_size);

        // Apply causal attention
        let output = causal_attn.forward(&input, None, None).unwrap();

        // Verify output is meaningful (causal property is enforced internally)
        let output_data = output.data();
        let non_zero_count = output_data.iter().filter(|&&x| x.abs() > 1e-6).count();
        assert!(non_zero_count > 0, "Causal attention should produce meaningful output");

        // Verify output shape
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
    }
}

#[cfg(test)]
mod kv_cache_integration_tests {
    use super::*;

    #[test]
    fn test_kv_cache_with_multi_head_attention() {
        let batch_size = 2;
        let seq_len = 8;
        let hidden_size = 128;
        let num_heads = 8;
        let head_dim = hidden_size / num_heads;

        // Create KV cache
        let cache_config = CacheConfig::new(256, batch_size, num_heads, head_dim);
        let mut cache = KVCache::new(cache_config).unwrap();

        // Create multi-head attention
        let attn_config = AttentionConfig::new(hidden_size, num_heads);
        let mut mha = MultiHeadAttention::<f32>::new(attn_config).unwrap();

        // Initialize parameters
        mha.init_parameters(ParameterInit::XavierUniform).unwrap();

        // Simulate autoregressive generation
        let mut total_output = Vec::new();

        for step in 0..3 {
            // Create input for current step
            let current_input = create_realistic_embeddings(batch_size, 1, hidden_size);

            // Apply attention (in real scenario, this would use cached K,V)
            let (step_output, _weights) = mha.forward(
                &current_input,
                &current_input,
                &current_input,
                None
            ).unwrap();

            // Store keys and values in cache (simplified - normally extracted from attention)
            let keys = create_test_tensor(1, num_heads, head_dim, 0.5 + step as f32 * 0.1);
            let values = create_test_tensor(1, num_heads, head_dim, 0.3 + step as f32 * 0.1);

            let sequence_id = format!("seq_{}", step);
            cache.store(&sequence_id, keys, values).unwrap();

            total_output.push(step_output);

            // Verify cache retrieval
            let (cached_keys, cached_values) = cache.get(&sequence_id).unwrap();
            assert_eq!(cached_keys.shape().dims(), &[1, num_heads, head_dim]);
            assert_eq!(cached_values.shape().dims(), &[1, num_heads, head_dim]);
        }

        assert_eq!(total_output.len(), 3);
    }

    #[test]
    fn test_incremental_generation_with_cache() {
        let max_seq_len = 20;
        let batch_size = 1;
        let num_heads = 4;
        let head_dim = 32;

        let cache_config = CacheConfig::new(max_seq_len, batch_size, num_heads, head_dim);
        let mut cache = KVCache::new(cache_config).unwrap();

        // Simulate incremental generation
        let sequence_id = "generation_seq";
        let mut current_seq_len = 0;

        for step in 0..5 {
            // Add one token at a time
            let keys = create_test_tensor(1, num_heads, head_dim, step as f32 * 0.2);
            let values = create_test_tensor(1, num_heads, head_dim, step as f32 * 0.3);

            if step == 0 {
                cache.store(sequence_id, keys, values).unwrap();
            } else {
                cache.append(sequence_id, keys, values).unwrap();
            }

            current_seq_len += 1;

            // Verify cache grows correctly
            let (cached_keys, cached_values) = cache.get(sequence_id).unwrap();
            assert_eq!(cached_keys.shape().dims()[0], current_seq_len);
            assert_eq!(cached_values.shape().dims()[0], current_seq_len);
        }
    }
}

#[cfg(test)]
mod positional_encoding_integration_tests {
    use super::*;

    #[test]
    fn test_positional_encoding_with_attention() {
        let batch_size = 2;
        let seq_len = 16;
        let hidden_size = 128;
        let num_heads = 8;

        // Create positional encoding
        let pos_config = PositionalConfig::new(256, hidden_size);
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(pos_config).unwrap();

        // Create multi-head attention
        let attn_config = AttentionConfig::new(hidden_size, num_heads);
        let mut mha = MultiHeadAttention::<f32>::new(attn_config).unwrap();

        // Initialize parameters
        mha.init_parameters(ParameterInit::XavierUniform).unwrap();

        // Create embeddings
        let embeddings = create_realistic_embeddings(batch_size, seq_len, hidden_size);

        // Test without positional encoding
        let (output_no_pos, _) = mha.forward(&embeddings, &embeddings, &embeddings, None).unwrap();

        // Test with positional encoding
        let positioned_embeddings = pos_enc.encode(&embeddings, None).unwrap();
        let (output_with_pos, _) = mha.forward(&positioned_embeddings, &positioned_embeddings, &positioned_embeddings, None).unwrap();

        // Verify shapes are the same
        assert_eq!(output_no_pos.shape().dims(), output_with_pos.shape().dims());

        // Verify outputs are different (positional encoding should change the result)
        let data_no_pos = output_no_pos.data();
        let data_with_pos = output_with_pos.data();

        let mut different_count = 0;
        for (a, b) in data_no_pos.iter().zip(data_with_pos.iter()) {
            if (a - b).abs() > 1e-6 {
                different_count += 1;
            }
        }

        assert!(different_count > data_no_pos.len() / 4,
               "Positional encoding should significantly change attention output");
    }

    #[test]
    fn test_rotary_positional_encoding_integration() {
        let batch_size = 1;
        let seq_len = 8;
        let hidden_size = 64;
        let num_heads = 4;

        // Create RoPE
        let rope_config = PositionalConfig::new(128, hidden_size);
        let rope = RotaryPositionalEncoding::<f32>::new(rope_config, 10000.0).unwrap();

        // Create attention
        let attn_config = AttentionConfig::new(hidden_size, num_heads);
        let mut mha = MultiHeadAttention::<f32>::new(attn_config).unwrap();

        // Initialize parameters
        mha.init_parameters(ParameterInit::XavierUniform).unwrap();

        // Create query and key
        let query = create_realistic_embeddings(batch_size, seq_len, hidden_size);
        let key = create_realistic_embeddings(batch_size, seq_len, hidden_size);
        let value = create_realistic_embeddings(batch_size, seq_len, hidden_size);

        // Apply RoPE to query and key
        let (rotated_q, rotated_k) = rope.apply_rotary_encoding(&query, &key, None).unwrap();

        // Use rotated query and key with original value
        let (output, _) = mha.forward(&rotated_q, &rotated_k, &value, None).unwrap();

        // Verify output shape
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);

        // Verify output is meaningful
        let output_data = output.data();
        let non_zero_count = output_data.iter().filter(|&&x| x.abs() > 1e-6).count();
        assert!(non_zero_count > 0, "RoPE-enhanced attention should produce meaningful output");
    }
}

#[cfg(test)]
mod error_handling_tests {
    use super::*;

    #[test]
    fn test_attention_error_handling() {
        let config = AttentionConfig::new(64, 4);

        // Test dimension mismatches
        let mut mha = MultiHeadAttention::<f32>::new(config.clone()).unwrap();

        // Initialize parameters
        mha.init_parameters(ParameterInit::XavierUniform).unwrap();

        let input1 = create_test_tensor(1, 5, 64, 1.0);
        let input2 = create_test_tensor(2, 5, 64, 1.0); // Different batch size

        let result = mha.forward(&input1, &input2, &input1, None);
        assert!(result.is_err());

        // Test KV cache overflow
        let cache_config = CacheConfig::new(5, 1, 4, 16); // Small max_seq_len
        let mut cache = KVCache::new(cache_config).unwrap();

        let keys = create_test_tensor(10, 4, 16, 1.0); // Exceeds max_seq_len
        let values = create_test_tensor(10, 4, 16, 1.0);

        let result = cache.store("overflow_seq", keys, values);
        assert!(result.is_err());

        // Test invalid positional encoding configuration
        let invalid_pos_config = PositionalConfig::new(10, 128); // max_seq_len < seq_len
        let embeddings = create_test_tensor(1, 20, 128, 1.0);
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(invalid_pos_config).unwrap();

        let result = pos_enc.encode(&embeddings, None);
        assert!(result.is_err());
    }
}

#[cfg(test)]
mod performance_tests {
    use super::*;

    #[test]
    fn test_attention_performance_scaling() {
        let config = AttentionConfig::new(256, 8);
        let mut mha = MultiHeadAttention::<f32>::new(config).unwrap();

        // Initialize parameters
        mha.init_parameters(ParameterInit::XavierUniform).unwrap();

        // Test with different sequence lengths
        let batch_size = 1;
        let hidden_size = 256;
        let mut previous_duration: Option<std::time::Duration> = None;

        for seq_len in [16, 32, 64, 128] {
            let input = create_realistic_embeddings(batch_size, seq_len, hidden_size);

            let start = Instant::now();
            let (output, weights) = mha.forward(&input, &input, &input, None).unwrap();
            let duration = start.elapsed();

            assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
            assert_eq!(weights.shape().dims(), &[batch_size, 8, seq_len, seq_len]);

            // Verify performance scaling (should be roughly quadratic in sequence length)
            if let Some(prev_dur) = previous_duration {
                let ratio = duration.as_nanos() as f64 / prev_dur.as_nanos() as f64;
                // Allow for some variance in timing, attention should scale roughly quadratically
                // but allow for significant variance due to system load and small sequence lengths
                assert!(ratio > 1.5 && ratio < 10.0,
                       "Performance scaling seems off: {}x for 2x sequence length", ratio);
            }

            previous_duration = Some(duration);
            println!("   ✓ Seq len {}: {:?}", seq_len, duration);
        }
    }

    #[test]
    fn test_kv_cache_performance_benefit() {
        let batch_size = 1;
        let seq_len = 64;
        let hidden_size = 128;
        let num_heads = 8;
        let head_dim = hidden_size / num_heads;

        // Create cache
        let cache_config = CacheConfig::new(256, batch_size, num_heads, head_dim);
        let mut cache = KVCache::new(cache_config).unwrap();

        // Measure cache operations
        let keys = create_test_tensor(seq_len, num_heads, head_dim, 1.0);
        let values = create_test_tensor(seq_len, num_heads, head_dim, 1.0);

        // Store operation
        let start = Instant::now();
        cache.store("perf_test", keys, values).unwrap();
        let store_duration = start.elapsed();

        // Retrieve operation
        let start = Instant::now();
        let (_cached_keys, _cached_values) = cache.get("perf_test").unwrap();
        let get_duration = start.elapsed();

        // Cache operations should be fast
        assert!(store_duration.as_millis() < 10, "Cache store should be fast");
        assert!(get_duration.as_millis() < 5, "Cache get should be very fast");

        println!("   ✓ Cache store: {:?}, get: {:?}", store_duration, get_duration);
    }
}
