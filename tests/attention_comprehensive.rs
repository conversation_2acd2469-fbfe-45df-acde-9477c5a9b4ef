//! Comprehensive attention mechanism tests.
//!
//! This module contains extensive tests for all attention components,
//! including correctness verification, edge cases, and numerical stability.

use qilin_inference::attention::{
    AttentionConfig, Attention,
    ScaledDotProductAttention, MultiHeadAttention,
    KVCache, CacheConfig,
};
use qilin_inference::error::AttentionError;
use qilin_inference::attention::positional::{
    PositionalEncoding, PositionalConfig,
    SinusoidalPositionalEncoding, RotaryPositionalEncoding,
};
use qilin_inference::attention::variants::{
    SelfAttention, CrossAttention, CausalAttention, AttentionVariant,
};
use qilin_inference::tensor::{Tensor, Shape};
use qilin_inference::tensor::cpu::CpuTensor;
use approx::assert_abs_diff_eq;

/// Helper function to create test tensors with specific patterns
fn create_test_tensor(batch_size: usize, seq_len: usize, hidden_size: usize, pattern: f32) -> CpuTensor<f32> {
    let mut data = vec![0.0; batch_size * seq_len * hidden_size];
    let data_len = data.len() as f32;
    for (i, val) in data.iter_mut().enumerate() {
        *val = pattern * (i as f32 + 1.0) / data_len;
    }
    CpuTensor::from_data(data, Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap()
}

/// Helper function to create attention mask
fn create_causal_mask(seq_len: usize) -> CpuTensor<f32> {
    let mut data = vec![0.0; seq_len * seq_len];
    for i in 0..seq_len {
        for j in 0..seq_len {
            if j > i {
                data[i * seq_len + j] = f32::NEG_INFINITY;
            }
        }
    }
    CpuTensor::from_data(data, Shape::new(vec![seq_len, seq_len])).unwrap()
}

#[cfg(test)]
mod scaled_dot_product_tests {
    use super::*;

    #[test]
    fn test_basic_attention_computation() {
        let attention = ScaledDotProductAttention::<f32>::new(64, 0.1, false).unwrap();
        
        let batch_size = 2;
        let seq_len = 10;
        let hidden_size = 64;
        
        let query = create_test_tensor(batch_size, seq_len, hidden_size, 1.0);
        let key = create_test_tensor(batch_size, seq_len, hidden_size, 0.8);
        let value = create_test_tensor(batch_size, seq_len, hidden_size, 0.6);
        
        let (result, _weights) = attention.compute_attention(&query, &key, &value, None).unwrap();

        assert_eq!(result.shape().dims(), &[batch_size, seq_len, hidden_size]);

        // Check that output is not all zeros
        let data = result.data();
        let non_zero_count = data.iter().filter(|&&x| x.abs() > 1e-6).count();
        assert!(non_zero_count > 0, "Attention output should not be all zeros");
    }

    #[test]
    fn test_attention_with_causal_mask() {
        let attention = ScaledDotProductAttention::<f32>::new(64, 0.0, true).unwrap(); // Causal attention
        
        let batch_size = 1;
        let seq_len = 4;
        let hidden_size = 64;
        
        let query = create_test_tensor(batch_size, seq_len, hidden_size, 1.0);
        let key = create_test_tensor(batch_size, seq_len, hidden_size, 1.0);
        let value = create_test_tensor(batch_size, seq_len, hidden_size, 1.0);
        let (result, _weights) = attention.compute_attention(&query, &key, &value, None).unwrap(); // Causal mask is built-in

        assert_eq!(result.shape().dims(), &[batch_size, seq_len, hidden_size]);
        
        // Verify causal property: later positions should not affect earlier ones
        // This is a simplified check - in practice, we'd need more sophisticated verification
        let data = result.data();
        assert!(data.iter().any(|&x| x != 0.0), "Result should not be all zeros");
    }

    #[test]
    fn test_attention_numerical_stability() {
        let attention = ScaledDotProductAttention::<f32>::new(64, 0.0, false).unwrap();
        
        let batch_size = 1;
        let seq_len = 5;
        let hidden_size = 64;
        
        // Create tensors with large values to test numerical stability
        let mut large_data = vec![0.0; batch_size * seq_len * hidden_size];
        let data_len = large_data.len();
        for (i, val) in large_data.iter_mut().enumerate() {
            *val = 100.0 * (i as f32 + 1.0) / (data_len as f32);
        }
        
        let query = CpuTensor::from_data(large_data.clone(), Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap();
        let key = CpuTensor::from_data(large_data.clone(), Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap();
        let value = CpuTensor::from_data(large_data, Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap();
        
        let (result, _weights) = attention.compute_attention(&query, &key, &value, None).unwrap();

        // Check that result contains no NaN or infinite values
        let data = result.data();
        for &val in data {
            assert!(val.is_finite(), "Attention output should be finite, got: {}", val);
        }
    }

    #[test]
    fn test_attention_dimension_mismatch() {
        let attention = ScaledDotProductAttention::<f32>::new(64, 0.1, false).unwrap();
        
        let query = create_test_tensor(2, 10, 64, 1.0);
        let key = create_test_tensor(2, 10, 32, 1.0); // Wrong hidden size
        let value = create_test_tensor(2, 10, 64, 1.0);
        
        let result = attention.compute_attention(&query, &key, &value, None);
        assert!(result.is_err(), "Should fail with dimension mismatch");

        // Just verify it's a dimension mismatch error
        match result {
            Err(AttentionError::DimensionMismatch { .. }) => {
                // Test passed - we got the expected error type
            }
            _ => panic!("Expected DimensionMismatch error"),
        }
    }

    #[test]
    fn test_attention_empty_sequence() {
        let attention = ScaledDotProductAttention::<f32>::new(64, 0.1, false).unwrap();

        let query = create_test_tensor(2, 0, 64, 1.0); // Empty sequence
        let key = create_test_tensor(2, 0, 64, 1.0);
        let value = create_test_tensor(2, 0, 64, 1.0);

        let result = attention.compute_attention(&query, &key, &value, None);
        // Empty sequences might be handled gracefully by returning empty tensors
        // Let's check if it either errors or returns empty tensors
        match result {
            Ok((output, _weights)) => {
                // If successful, output should have empty sequence dimension
                assert_eq!(output.shape().dims()[1], 0, "Empty sequence should produce empty output");
            }
            Err(_) => {
                // It's also acceptable to return an error for empty sequences
            }
        }
    }
}

#[cfg(test)]
mod multi_head_attention_tests {
    use super::*;

    #[test]
    fn test_multi_head_basic_computation() {
        let config = AttentionConfig::new(512, 8).with_dropout(0.1);
        let mha = MultiHeadAttention::<f32>::new(config).unwrap();
        
        let batch_size = 2;
        let seq_len = 10;
        let hidden_size = 512;
        
        // Create more diverse test data to avoid zero outputs
        let mut query_data = vec![0.0f32; batch_size * seq_len * hidden_size];
        let mut key_data = vec![0.0f32; batch_size * seq_len * hidden_size];
        let mut value_data = vec![0.0f32; batch_size * seq_len * hidden_size];

        for i in 0..query_data.len() {
            query_data[i] = (i as f32 * 0.1).sin();
            key_data[i] = (i as f32 * 0.1 + 1.0).cos();
            value_data[i] = (i as f32 * 0.1 + 2.0).sin();
        }

        let query = CpuTensor::from_data(query_data, Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap();
        let key = CpuTensor::from_data(key_data, Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap();
        let value = CpuTensor::from_data(value_data, Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap();

        let (result, _weights) = mha.forward(&query, &key, &value, None).unwrap();

        assert_eq!(result.shape().dims(), &[batch_size, seq_len, hidden_size]);

        // Verify output is not all zeros
        let data = result.data();
        let non_zero_count = data.iter().filter(|&&x| x.abs() > 1e-6).count();
        assert!(non_zero_count > 0, "Multi-head attention output should not be all zeros");
    }

    #[test]
    fn test_multi_head_with_different_kv() {
        let config = AttentionConfig::new(256, 4);
        let mha = MultiHeadAttention::<f32>::new(config).unwrap();
        
        let batch_size = 1;
        let q_seq_len = 8;
        let kv_seq_len = 12;
        let hidden_size = 256;
        
        let query = create_test_tensor(batch_size, q_seq_len, hidden_size, 1.0);
        let key = create_test_tensor(batch_size, kv_seq_len, hidden_size, 0.8);
        let value = create_test_tensor(batch_size, kv_seq_len, hidden_size, 0.6);
        
        let (result, _weights) = mha.forward(&query, &key, &value, None).unwrap();

        assert_eq!(result.shape().dims(), &[batch_size, q_seq_len, hidden_size]);
    }

    #[test]
    fn test_multi_head_invalid_head_count() {
        let config = AttentionConfig::new(100, 7).with_dropout(0.1); // 100 not divisible by 7
        let result = MultiHeadAttention::<f32>::new(config);
        
        assert!(result.is_err(), "Should fail with invalid head configuration");

        // Just verify it's an invalid configuration error
        match result {
            Err(AttentionError::InvalidConfiguration { .. }) => {
                // Test passed - we got the expected error type
            }
            _ => panic!("Expected InvalidConfiguration error"),
        }
    }
}

#[cfg(test)]
mod kv_cache_tests {
    use super::*;

    #[test]
    fn test_kv_cache_basic_operations() {
        let config = CacheConfig::new(1024, 2, 8, 64); // max_seq_len, max_batch_size, num_heads, head_dim
        let mut cache = KVCache::new(config).unwrap();

        let seq_len = 10;
        let num_heads = 8;
        let head_dim = 64;

        // Create tensors with correct shape: [seq_len, num_heads, head_dim]
        let key = create_test_tensor(seq_len, num_heads, head_dim, 0.8);
        let value = create_test_tensor(seq_len, num_heads, head_dim, 0.6);
        
        // Store in cache
        cache.store("seq_0", key.clone(), value.clone()).unwrap();

        // Retrieve from cache
        let (cached_key, cached_value) = cache.get("seq_0").unwrap();
        
        assert_eq!(cached_key.shape().dims(), key.shape().dims());
        assert_eq!(cached_value.shape().dims(), value.shape().dims());
        
        // Verify data integrity
        let orig_key_data = key.data();
        let cached_key_data = cached_key.data();
        for (orig, cached) in orig_key_data.iter().zip(cached_key_data.iter()) {
            assert_abs_diff_eq!(*orig, *cached, epsilon = 1e-6);
        }
    }

    #[test]
    fn test_kv_cache_incremental_update() {
        let config = CacheConfig::new(1024, 1, 8, 64);
        let mut cache = KVCache::new(config).unwrap();

        let num_heads = 8;
        let head_dim = 64;

        // Add first sequence - shape: [seq_len, num_heads, head_dim]
        let key1 = create_test_tensor(5, num_heads, head_dim, 0.8);
        let value1 = create_test_tensor(5, num_heads, head_dim, 0.6);
        cache.store("seq_0", key1, value1).unwrap();

        // Add more tokens
        let key2 = create_test_tensor(3, num_heads, head_dim, 0.9);
        let value2 = create_test_tensor(3, num_heads, head_dim, 0.7);
        cache.append("seq_0", key2, value2).unwrap();

        // Verify total length
        let (cached_key, cached_value) = cache.get("seq_0").unwrap();
        assert_eq!(cached_key.shape().dims()[0], 8); // 5 + 3 (seq_len dimension)
        assert_eq!(cached_value.shape().dims()[0], 8);
    }

    #[test]
    fn test_kv_cache_overflow() {
        let config = CacheConfig::new(10, 1, 2, 32); // Small cache: max_seq_len=10
        let mut cache = KVCache::new(config).unwrap();
        
        let batch_size = 1;
        let seq_len = 15; // Larger than cache capacity
        let hidden_size = 64; // 2 heads * 32 head_dim = 64
        
        let key = create_test_tensor(batch_size, seq_len, hidden_size, 0.8);
        let value = create_test_tensor(batch_size, seq_len, hidden_size, 0.6);
        
        let result = cache.store("seq_0", key, value);
        assert!(result.is_err(), "Should fail when exceeding cache capacity");
    }
}

#[cfg(test)]
mod positional_encoding_tests {
    use super::*;

    #[test]
    fn test_sinusoidal_encoding_properties() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(config).unwrap();

        let batch_size = 2;
        let seq_len = 10;
        let hidden_size = 64;

        let embeddings = create_test_tensor(batch_size, seq_len, hidden_size, 1.0);
        let encoded = pos_enc.encode(&embeddings, None).unwrap();

        assert_eq!(encoded.shape().dims(), &[batch_size, seq_len, hidden_size]);

        // Test that different positions have different encodings
        let positions = vec![0, 1, 5];
        let specific_encodings = pos_enc.get_encodings(&positions, hidden_size).unwrap();

        let data = specific_encodings.data();
        let pos0 = &data[0..hidden_size];
        let pos1 = &data[hidden_size..2*hidden_size];

        // Positions should have different encodings
        let mut different_count = 0;
        for (p0, p1) in pos0.iter().zip(pos1.iter()) {
            if (p0 - p1).abs() > 1e-6 {
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Different positions should have different encodings");
    }

    #[test]
    fn test_rotary_encoding_rotation_property() {
        let config = PositionalConfig::new(100, 64);
        let rope = RotaryPositionalEncoding::<f32>::new(config, 10000.0).unwrap();

        let batch_size = 1;
        let seq_len = 5;
        let hidden_size = 64;

        let query = create_test_tensor(batch_size, seq_len, hidden_size, 1.0);
        let key = create_test_tensor(batch_size, seq_len, hidden_size, 0.8);

        let (rotated_q, rotated_k) = rope.apply_rotary_encoding(&query, &key, None).unwrap();

        assert_eq!(rotated_q.shape().dims(), query.shape().dims());
        assert_eq!(rotated_k.shape().dims(), key.shape().dims());

        // Test with offset
        let (rotated_q_offset, _) = rope.apply_rotary_encoding(&query, &key, Some(3)).unwrap();

        // Results with different offsets should be different
        let data1 = rotated_q.data();
        let data2 = rotated_q_offset.data();
        let mut different_count = 0;
        for (d1, d2) in data1.iter().zip(data2.iter()) {
            if (d1 - d2).abs() > 1e-5 {
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Different offsets should produce different results");
    }

    #[test]
    fn test_positional_encoding_incremental_consistency() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(config).unwrap();

        let batch_size = 1;
        let hidden_size = 64;

        // Encode a longer sequence with zero embeddings to isolate positional encoding
        let long_embeddings = create_test_tensor(batch_size, 10, hidden_size, 0.0);
        let long_encoded = pos_enc.encode(&long_embeddings, None).unwrap();

        // Encode shorter sequences with offsets
        let short_embeddings = create_test_tensor(batch_size, 3, hidden_size, 0.0);
        let offset_encoded = pos_enc.encode(&short_embeddings, Some(5)).unwrap();

        // Compare positions 5-7 from long sequence with positions 0-2 from offset sequence
        let long_data = long_encoded.data();
        let offset_data = offset_encoded.data();

        let long_start = 5 * hidden_size;
        let long_end = 8 * hidden_size;
        let offset_end = 3 * hidden_size;

        for i in 0..(offset_end) {
            let long_val = long_data[long_start + i];
            let offset_val = offset_data[i];
            assert_abs_diff_eq!(long_val, offset_val, epsilon = 1e-5);
        }
    }
}

#[cfg(test)]
mod attention_variants_tests {
    use super::*;

    #[test]
    fn test_self_attention() {
        let config = AttentionConfig::new(256, 4).with_dropout(0.1);
        let self_attn = SelfAttention::<f32>::new(config).unwrap();

        let batch_size = 2;
        let seq_len = 8;
        let hidden_size = 256;

        let input = create_test_tensor(batch_size, seq_len, hidden_size, 1.0);
        let result = self_attn.forward(&input, None, None).unwrap();

        assert_eq!(result.shape().dims(), &[batch_size, seq_len, hidden_size]);

        // Verify output is not identical to input
        let input_data = input.data();
        let output_data = result.data();
        let mut different_count = 0;
        for (inp, out) in input_data.iter().zip(output_data.iter()) {
            if (inp - out).abs() > 1e-6 {
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Self-attention should transform the input");
    }

    #[test]
    fn test_cross_attention() {
        let config = AttentionConfig::new(256, 4).with_dropout(0.1);
        let cross_attn = CrossAttention::<f32>::new(config).unwrap();

        let batch_size = 2;
        let q_seq_len = 6;
        let kv_seq_len = 10;
        let hidden_size = 256;

        let query = create_test_tensor(batch_size, q_seq_len, hidden_size, 1.0);
        let key_value = create_test_tensor(batch_size, kv_seq_len, hidden_size, 0.8);

        let result = cross_attn.forward(&query, Some(&key_value), None).unwrap();

        assert_eq!(result.shape().dims(), &[batch_size, q_seq_len, hidden_size]);
    }

    #[test]
    fn test_causal_attention() {
        let config = AttentionConfig::new(256, 4); // No dropout for deterministic test
        let causal_attn = CausalAttention::<f32>::new(config).unwrap();

        let batch_size = 1;
        let seq_len = 5;
        let hidden_size = 256;

        let input = create_test_tensor(batch_size, seq_len, hidden_size, 1.0);
        let result = causal_attn.forward(&input, None, None).unwrap();

        assert_eq!(result.shape().dims(), &[batch_size, seq_len, hidden_size]);

        // Test causal property by checking that changing later positions
        // doesn't affect earlier positions (this is a simplified test)
        let mut modified_input = input.clone();
        let mut modified_data = modified_input.data_mut();
        // Modify the last position
        let last_pos_start = (seq_len - 1) * hidden_size;
        for i in last_pos_start..(seq_len * hidden_size) {
            modified_data[i] *= 2.0;
        }

        let modified_result = causal_attn.forward(&modified_input, None, None).unwrap();

        // First positions should be identical or very similar
        let orig_data = result.data();
        let mod_data = modified_result.data();

        // Check first position (should be unaffected by changes to last position)
        for i in 0..hidden_size {
            assert_abs_diff_eq!(orig_data[i], mod_data[i], epsilon = 1e-4);
        }
    }
}
