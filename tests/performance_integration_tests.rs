//! Performance Integration Tests
//!
//! This test suite focuses on performance characteristics and benchmarking
//! of integrated components to ensure optimal performance across the system.

use qilin_inference::layers::*;
use qilin_inference::attention::*;
use qilin_inference::tensor::{
    Tensor, <PERSON>hape, TensorOps, TensorFactory,
    cpu::{CpuTensor, CpuTensorFactory, PooledCpuTensorFactory},
    memory_pool::{TensorMemoryPool, MemoryPoolConfig, GlobalMemoryPool},
};

#[cfg(feature = "simd")]
use qilin_inference::tensor::simd_nn::{SimdNeuralOps, SimdNeuralOptimized};

use qilin_inference::tensor::parallel::{ParallelOps, ParallelConfig};
use std::time::{Instant, Duration};

/// Benchmark transformer block performance across different configurations
#[test]
fn benchmark_transformer_block_performance() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Transformer Block Performance Benchmark ===");
    
    let configurations = vec![
        ("Small", 128, 4, 8, 512),    // hidden_dim, num_heads, seq_len, ff_dim
        ("Medium", 256, 8, 16, 1024),
        ("Large", 512, 16, 32, 2048),
    ];
    
    for (name, hidden_dim, num_heads, seq_len, ff_dim) in configurations {
        println!("\n{} Configuration: {}d, {}h, {}s, {}ff", 
                 name, hidden_dim, num_heads, seq_len, ff_dim);
        
        let batch_size = 2;
        let input_shape = Shape::new(vec![batch_size, seq_len, hidden_dim]);
        let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1)?;
        
        // Setup components
        let attn_config = AttentionConfig::new(hidden_dim, num_heads);
        let mut attention = MultiHeadAttention::new(attn_config)?;
        attention.init_parameters(ParameterInit::XavierUniform)?;
        
        let norm_config = LayerNormConfig::new(vec![hidden_dim]);
        let mut layer_norm = LayerNorm::new(norm_config)?;
        layer_norm.init_parameters(ParameterInit::Ones)?;
        
        let ff_config = FeedForwardConfig::new(hidden_dim, ff_dim)
            .with_activation(ActivationType::GELU);
        let mut feedforward = FeedForward::new(ff_config)?;
        feedforward.init_parameters(ParameterInit::XavierUniform)?;
        
        // Warmup
        for _ in 0..3 {
            let _ = attention.forward(input.clone())?;
        }
        
        // Benchmark
        let iterations = 10;
        let start = Instant::now();
        
        for _ in 0..iterations {
            let attn_out = attention.forward(input.clone())?;
            let residual = input.add(&attn_out)?;
            let norm_out = layer_norm.forward(residual)?;
            let ff_out = feedforward.forward(norm_out.clone())?;
            let _final_out = norm_out.add(&ff_out)?;
        }
        
        let total_time = start.elapsed();
        let avg_time = total_time / iterations;
        let throughput = (batch_size * seq_len) as f64 / avg_time.as_secs_f64();
        
        println!("  Average time: {:?}", avg_time);
        println!("  Throughput: {:.1} tokens/sec", throughput);
        println!("  Memory usage: ~{:.1} MB", estimate_memory_usage(hidden_dim, seq_len, batch_size));
    }
    
    Ok(())
}

/// Benchmark memory pool performance vs direct allocation
#[test]
fn benchmark_memory_pool_performance() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Memory Pool Performance Benchmark ===");
    
    let sizes = vec![256, 512, 1024, 2048, 4096];
    let iterations = 1000;
    
    // Setup memory pool
    let config = MemoryPoolConfig {
        enable_stats: true,
        enable_size_bucketing: true,
        preallocation_sizes: sizes.clone(),
        ..Default::default()
    };
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    for size in sizes {
        println!("\nTesting size: {} elements", size);
        
        // Benchmark direct allocation
        let start = Instant::now();
        for _ in 0..iterations {
            let _buffer: Vec<f32> = vec![0.0; size];
        }
        let direct_time = start.elapsed();
        
        // Benchmark pooled allocation
        let start = Instant::now();
        for _ in 0..iterations {
            let buffer = pool.allocate(size)?;
            pool.deallocate(buffer);
        }
        let pooled_time = start.elapsed();
        
        let speedup = direct_time.as_nanos() as f64 / pooled_time.as_nanos() as f64;
        
        println!("  Direct allocation: {:?}", direct_time);
        println!("  Pooled allocation: {:?}", pooled_time);
        println!("  Speedup: {:.2}x", speedup);
        
        // Check pool statistics
        let stats = pool.stats();
        println!("  Hit rate: {:.1}%", stats.hit_rate());
    }
    
    Ok(())
}

/// Benchmark SIMD vs scalar operations
#[cfg(feature = "simd")]
#[test]
fn benchmark_simd_performance() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== SIMD Performance Benchmark ===");
    
    let sizes = vec![1000, 5000, 10000, 50000];
    let iterations = 100;
    
    for size in sizes {
        println!("\nTesting size: {} elements", size);
        
        let input: Vec<f32> = (0..size).map(|i| (i as f32) * 0.001 - 0.5).collect();
        let mut simd_output = vec![0.0; size];
        let mut scalar_output = vec![0.0; size];
        
        // Benchmark SIMD ReLU
        let start = Instant::now();
        for _ in 0..iterations {
            input.simd_relu(&mut simd_output);
        }
        let simd_time = start.elapsed();
        
        // Benchmark scalar ReLU
        let start = Instant::now();
        for _ in 0..iterations {
            for i in 0..size {
                scalar_output[i] = input[i].max(0.0);
            }
        }
        let scalar_time = start.elapsed();
        
        let speedup = scalar_time.as_nanos() as f64 / simd_time.as_nanos() as f64;
        
        println!("  SIMD ReLU: {:?}", simd_time);
        println!("  Scalar ReLU: {:?}", scalar_time);
        println!("  SIMD speedup: {:.2}x", speedup);
        
        // Verify correctness
        let max_diff = simd_output.iter().zip(scalar_output.iter())
            .map(|(a, b)| (a - b).abs())
            .fold(0.0, f32::max);
        println!("  Max difference: {:.2e}", max_diff);
        assert!(max_diff < 1e-6, "SIMD and scalar results should match");
    }
    
    Ok(())
}

/// Benchmark attention mechanisms with different sequence lengths
#[test]
fn benchmark_attention_scaling() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Attention Scaling Benchmark ===");
    
    let hidden_dim = 256;
    let num_heads = 8;
    let batch_size = 2;
    let seq_lengths = vec![8, 16, 32, 64, 128];
    
    for seq_len in seq_lengths {
        println!("\nSequence length: {}", seq_len);
        
        let input_shape = Shape::new(vec![batch_size, seq_len, hidden_dim]);
        let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1)?;
        
        // Standard attention
        let attn_config = AttentionConfig::new(hidden_dim, num_heads);
        let mut attention = MultiHeadAttention::new(attn_config)?;
        attention.init_parameters(ParameterInit::XavierUniform)?;
        
        // Causal attention
        let causal_config = AttentionConfig::causal(hidden_dim, num_heads);
        let mut causal_attention = CausalAttention::new(causal_config)?;
        causal_attention.init_parameters(ParameterInit::XavierUniform)?;
        
        // Benchmark standard attention
        let iterations = 5;
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = attention.forward(input.clone())?;
        }
        let standard_time = start.elapsed() / iterations;
        
        // Benchmark causal attention
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = causal_attention.forward(input.clone())?;
        }
        let causal_time = start.elapsed() / iterations;
        
        let complexity = (seq_len * seq_len) as f64;
        let standard_efficiency = complexity / standard_time.as_nanos() as f64;
        let causal_efficiency = complexity / causal_time.as_nanos() as f64;
        
        println!("  Standard attention: {:?} ({:.2e} ops/ns)", 
                 standard_time, standard_efficiency);
        println!("  Causal attention: {:?} ({:.2e} ops/ns)", 
                 causal_time, causal_efficiency);
        println!("  Theoretical complexity: O({}²) = {}", seq_len, complexity);
    }
    
    Ok(())
}

/// Benchmark parallel operations scaling
#[test]
fn benchmark_parallel_scaling() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Parallel Operations Scaling Benchmark ===");
    
    let sizes = vec![10000, 50000, 100000, 500000];
    let thread_counts = vec![1, 2, 4, 8];
    
    for size in sizes {
        println!("\nArray size: {} elements", size);
        
        let a: Vec<f32> = (0..size).map(|i| i as f32 * 0.001).collect();
        let b: Vec<f32> = (0..size).map(|i| i as f32 * 0.002).collect();
        
        for num_threads in &thread_counts {
            let config = ParallelConfig::new(1000, Some(*num_threads), size / num_threads);
            let mut result = vec![0.0; size];
            
            let iterations = 10;
            let start = Instant::now();
            
            for _ in 0..iterations {
                ParallelOps::add_f32_parallel(&a, &b, &mut result, &config);
            }
            
            let avg_time = start.elapsed() / iterations;
            let throughput = size as f64 / avg_time.as_secs_f64();
            
            println!("  {} threads: {:?} ({:.2e} ops/sec)", 
                     num_threads, avg_time, throughput);
        }
        
        // Calculate parallel efficiency
        println!("  Parallel efficiency analysis:");
        let single_thread_config = ParallelConfig::new(1000, Some(1), size);
        let multi_thread_config = ParallelConfig::new(1000, Some(4), size / 4);
        
        let mut result = vec![0.0; size];
        
        let start = Instant::now();
        ParallelOps::add_f32_parallel(&a, &b, &mut result, &single_thread_config);
        let single_time = start.elapsed();
        
        let start = Instant::now();
        ParallelOps::add_f32_parallel(&a, &b, &mut result, &multi_thread_config);
        let multi_time = start.elapsed();
        
        let speedup = single_time.as_nanos() as f64 / multi_time.as_nanos() as f64;
        let efficiency = speedup / 4.0; // 4 threads
        
        println!("    4-thread speedup: {:.2}x", speedup);
        println!("    Parallel efficiency: {:.1}%", efficiency * 100.0);
    }
    
    Ok(())
}

/// Estimate memory usage for transformer block
fn estimate_memory_usage(hidden_dim: usize, seq_len: usize, batch_size: usize) -> f64 {
    let tensor_size = batch_size * seq_len * hidden_dim;
    let attention_matrices = hidden_dim * hidden_dim * 4; // Q, K, V, O projections
    let ff_matrices = hidden_dim * (hidden_dim * 4) * 2; // Two linear layers
    
    let total_params = attention_matrices + ff_matrices;
    let total_activations = tensor_size * 8; // Rough estimate for intermediate tensors
    
    (total_params + total_activations) as f64 * 4.0 / (1024.0 * 1024.0) // Convert to MB
}

/// Test memory usage patterns across different workloads
#[test]
fn test_memory_usage_patterns() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Memory Usage Pattern Analysis ===");
    
    let pool = GlobalMemoryPool::f32();
    let initial_stats = pool.stats();
    
    println!("Initial pool state:");
    println!("  Pooled buffers: {}", initial_stats.current_pooled_buffers);
    println!("  Pooled memory: {:.2} MB", 
             initial_stats.current_pooled_memory as f64 / (1024.0 * 1024.0));
    
    // Simulate training workload
    println!("\nSimulating training workload...");
    let batch_size = 4;
    let seq_len = 32;
    let hidden_dim = 256;
    
    for epoch in 0..3 {
        println!("  Epoch {}", epoch + 1);
        
        for step in 0..10 {
            // Create tensors for forward pass
            let input_shape = Shape::new(vec![batch_size, seq_len, hidden_dim]);
            let _input = PooledCpuTensorFactory::<f32>::randn(&input_shape, 0.0, 0.1)?;
            let _gradients = PooledCpuTensorFactory::<f32>::zeros(&input_shape)?;
            
            if step % 5 == 0 {
                let stats = pool.stats();
                println!("    Step {}: {} buffers, {:.2} MB, {:.1}% hit rate", 
                         step, stats.current_pooled_buffers,
                         stats.current_pooled_memory as f64 / (1024.0 * 1024.0),
                         stats.hit_rate());
            }
        }
    }
    
    let final_stats = pool.stats();
    println!("\nFinal pool state:");
    println!("  Total allocations: {}", final_stats.total_allocations);
    println!("  Pool hits: {} ({:.1}%)", final_stats.pool_hits, final_stats.hit_rate());
    println!("  Peak memory: {:.2} MB", 
             final_stats.peak_memory_usage as f64 / (1024.0 * 1024.0));
    
    Ok(())
}
