//! Compatibility Integration Tests
//!
//! This test suite validates compatibility and interoperability between
//! different components, ensuring they work correctly together across
//! various configurations and edge cases.

use qilin_inference::layers::*;
use qilin_inference::attention::*;
use qilin_inference::tensor::{
    <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TensorFactory,
    cpu::{CpuTensor, CpuTensorFactory, PooledCpuTensorFactory},
    memory_pool::{TensorMemoryPool, MemoryPoolConfig, GlobalMemoryPool},
};

#[cfg(feature = "simd")]
use qilin_inference::tensor::simd_nn::SimdNeuralOptimized;

use qilin_inference::tensor::parallel::{ParallelOps, ParallelConfig};
use qilin_inference::error::{TensorError, LayerError, AttentionError};

/// Test compatibility between different layer types in sequence
#[test]
fn test_layer_sequence_compatibility() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Layer Sequence Compatibility...");
    
    let batch_size = 2;
    let input_dim = 128;
    let hidden_dim = 256;
    let output_dim = 64;
    
    // Create input
    let input_shape = Shape::new(vec![batch_size, input_dim]);
    let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1)?;
    
    // Layer 1: Linear transformation
    let linear1_config = LinearConfig::new(input_dim, hidden_dim);
    let mut linear1 = Linear::new(linear1_config)?;
    linear1.init_parameters(ParameterInit::XavierUniform)?;
    
    let hidden = linear1.forward(input)?;
    assert_eq!(hidden.shape().dims(), &[batch_size, hidden_dim]);
    println!("  ✓ Linear1: [2, 128] -> [2, 256]");
    
    // Layer 2: Layer normalization
    let norm_config = LayerNormConfig::new(vec![hidden_dim]);
    let mut layer_norm = LayerNorm::new(norm_config)?;
    layer_norm.init_parameters(ParameterInit::Ones)?;
    
    let normalized = layer_norm.forward(hidden)?;
    assert_eq!(normalized.shape().dims(), &[batch_size, hidden_dim]);
    println!("  ✓ LayerNorm: [2, 256] -> [2, 256]");
    
    // Layer 3: Activation
    let activation = Activation::new(ActivationType::GELU);
    let activated = activation.forward(normalized)?;
    assert_eq!(activated.shape().dims(), &[batch_size, hidden_dim]);
    println!("  ✓ GELU: [2, 256] -> [2, 256]");
    
    // Layer 4: Final linear layer
    let linear2_config = LinearConfig::new(hidden_dim, output_dim);
    let mut linear2 = Linear::new(linear2_config)?;
    linear2.init_parameters(ParameterInit::XavierUniform)?;
    
    let output = linear2.forward(activated)?;
    assert_eq!(output.shape().dims(), &[batch_size, output_dim]);
    println!("  ✓ Linear2: [2, 256] -> [2, 64]");
    
    // Verify output is reasonable
    let output_data = output.data();
    assert!(output_data.iter().all(|&x| x.is_finite()), "Output should be finite");
    
    let mean = output_data.iter().sum::<f32>() / output_data.len() as f32;
    let std = (output_data.iter().map(|&x| (x - mean).powi(2)).sum::<f32>() / output_data.len() as f32).sqrt();
    
    println!("  ✓ Output statistics: mean={:.4}, std={:.4}", mean, std);
    println!("  ✓ Layer sequence compatibility successful!");
    
    Ok(())
}

/// Test compatibility between different attention variants
#[test]
fn test_attention_variants_compatibility() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Attention Variants Compatibility...");
    
    let batch_size = 2;
    let seq_len = 8;
    let hidden_dim = 128;
    let num_heads = 4;
    
    let input_shape = Shape::new(vec![batch_size, seq_len, hidden_dim]);
    let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1)?;
    
    // Test Self-Attention
    let self_attn_config = AttentionConfig::new(hidden_dim, num_heads);
    let mut self_attention = SelfAttention::new(self_attn_config)?;
    self_attention.init_parameters(ParameterInit::XavierUniform)?;
    
    let self_output = self_attention.forward(input.clone())?;
    assert_eq!(self_output.shape(), &input_shape);
    println!("  ✓ Self-Attention: {} -> {}", 
             format_shape(&input_shape), format_shape(self_output.shape()));
    
    // Test Causal Attention
    let causal_config = AttentionConfig::causal(hidden_dim, num_heads);
    let mut causal_attention = CausalAttention::new(causal_config)?;
    causal_attention.init_parameters(ParameterInit::XavierUniform)?;
    
    let causal_output = causal_attention.forward(input.clone())?;
    assert_eq!(causal_output.shape(), &input_shape);
    println!("  ✓ Causal Attention: {} -> {}", 
             format_shape(&input_shape), format_shape(causal_output.shape()));
    
    // Test Cross-Attention (with different key/value)
    let kv_shape = Shape::new(vec![batch_size, seq_len + 2, hidden_dim]); // Different seq length
    let key_value = CpuTensorFactory::randn(&kv_shape, 0.0, 0.1)?;
    
    let cross_config = AttentionConfig::new(hidden_dim, num_heads);
    let mut cross_attention = CrossAttention::new(cross_config)?;
    cross_attention.init_parameters(ParameterInit::XavierUniform)?;
    
    let cross_output = cross_attention.forward_with_kv(input.clone(), key_value.clone(), key_value)?;
    assert_eq!(cross_output.shape(), &input_shape);
    println!("  ✓ Cross Attention: {} x {} -> {}", 
             format_shape(&input_shape), format_shape(&kv_shape), format_shape(cross_output.shape()));
    
    // Verify outputs are different (attention should produce different results)
    let self_data = self_output.data();
    let causal_data = causal_output.data();
    let cross_data = cross_output.data();
    
    let self_causal_diff = self_data.iter().zip(causal_data.iter())
        .map(|(a, b)| (a - b).abs()).fold(0.0, f32::max);
    let self_cross_diff = self_data.iter().zip(cross_data.iter())
        .map(|(a, b)| (a - b).abs()).fold(0.0, f32::max);
    
    assert!(self_causal_diff > 1e-4, "Self and causal attention should produce different results");
    assert!(self_cross_diff > 1e-4, "Self and cross attention should produce different results");
    
    println!("  ✓ Attention variants produce distinct outputs");
    println!("  ✓ Attention variants compatibility successful!");
    
    Ok(())
}

/// Test compatibility between pooled and non-pooled tensor operations
#[test]
fn test_pooled_tensor_compatibility() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Pooled Tensor Compatibility...");
    
    let shape = Shape::new(vec![4, 32]);
    
    // Create tensors using different factories
    let regular_tensor = CpuTensorFactory::randn(&shape, 0.0, 1.0)?;
    let pooled_tensor = PooledCpuTensorFactory::<f32>::randn(&shape, 0.0, 1.0)?;
    
    // Test that they can be used interchangeably in operations
    let sum_result = regular_tensor.add(&pooled_tensor)?;
    assert_eq!(sum_result.shape(), &shape);
    println!("  ✓ Regular + Pooled tensor addition");
    
    let mul_result = pooled_tensor.mul(&regular_tensor)?;
    assert_eq!(mul_result.shape(), &shape);
    println!("  ✓ Pooled * Regular tensor multiplication");
    
    // Test with layer operations
    let linear_config = LinearConfig::new(32, 16);
    let mut linear = Linear::new(linear_config)?;
    linear.init_parameters(ParameterInit::XavierUniform)?;
    
    let regular_output = linear.forward(regular_tensor)?;
    let pooled_output = linear.forward(pooled_tensor)?;
    
    assert_eq!(regular_output.shape().dims(), &[4, 16]);
    assert_eq!(pooled_output.shape().dims(), &[4, 16]);
    
    println!("  ✓ Layer operations work with both tensor types");
    
    // Test memory pool statistics
    let pool = GlobalMemoryPool::f32();
    let stats = pool.stats();
    println!("  ✓ Pool usage: {} allocations, {:.1}% hit rate", 
             stats.total_allocations, stats.hit_rate());
    
    println!("  ✓ Pooled tensor compatibility successful!");
    Ok(())
}

/// Test compatibility between different activation functions
#[test]
fn test_activation_compatibility() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Activation Function Compatibility...");
    
    let shape = Shape::new(vec![3, 64]);
    let input = CpuTensorFactory::randn(&shape, 0.0, 1.0)?;
    
    let activations = vec![
        ("ReLU", ActivationType::ReLU),
        ("GELU", ActivationType::GELU),
        ("Swish", ActivationType::Swish),
        ("Mish", ActivationType::Mish),
        ("Sigmoid", ActivationType::Sigmoid),
        ("Tanh", ActivationType::Tanh),
    ];
    
    let mut outputs = Vec::new();
    
    for (name, activation_type) in activations {
        let activation = Activation::new(activation_type);
        let output = activation.forward(input.clone())?;
        
        assert_eq!(output.shape(), &shape);
        
        // Verify output characteristics
        let output_data = output.data();
        assert!(output_data.iter().all(|&x| x.is_finite()), 
               "{} should produce finite outputs", name);
        
        let mean = output_data.iter().sum::<f32>() / output_data.len() as f32;
        let min = output_data.iter().fold(f32::INFINITY, |a, &b| a.min(b));
        let max = output_data.iter().fold(f32::NEG_INFINITY, |a, &b| a.max(b));
        
        println!("  ✓ {}: mean={:.4}, range=[{:.4}, {:.4}]", name, mean, min, max);
        
        outputs.push((name, output));
    }
    
    // Verify that different activations produce different results
    for i in 0..outputs.len() {
        for j in i+1..outputs.len() {
            let (name1, ref output1) = outputs[i];
            let (name2, ref output2) = outputs[j];
            
            let diff = output1.data().iter().zip(output2.data().iter())
                .map(|(a, b)| (a - b).abs()).fold(0.0, f32::max);
            
            assert!(diff > 1e-4, "{} and {} should produce different results", name1, name2);
        }
    }
    
    println!("  ✓ All activation functions produce distinct outputs");
    println!("  ✓ Activation compatibility successful!");
    
    Ok(())
}

/// Test compatibility with different tensor shapes and dimensions
#[test]
fn test_shape_compatibility() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Shape Compatibility...");
    
    // Test 2D tensors (batch, features)
    let shape_2d = Shape::new(vec![4, 128]);
    let tensor_2d = CpuTensorFactory::randn(&shape_2d, 0.0, 1.0)?;
    
    let linear_config = LinearConfig::new(128, 64);
    let mut linear = Linear::new(linear_config)?;
    linear.init_parameters(ParameterInit::XavierUniform)?;
    
    let output_2d = linear.forward(tensor_2d)?;
    assert_eq!(output_2d.shape().dims(), &[4, 64]);
    println!("  ✓ 2D tensor: [4, 128] -> [4, 64]");
    
    // Test 3D tensors (batch, sequence, features)
    let shape_3d = Shape::new(vec![2, 8, 128]);
    let tensor_3d = CpuTensorFactory::randn(&shape_3d, 0.0, 1.0)?;
    
    let output_3d = linear.forward(tensor_3d)?;
    assert_eq!(output_3d.shape().dims(), &[2, 8, 64]);
    println!("  ✓ 3D tensor: [2, 8, 128] -> [2, 8, 64]");
    
    // Test 4D tensors (batch, heads, sequence, features)
    let shape_4d = Shape::new(vec![2, 4, 8, 128]);
    let tensor_4d = CpuTensorFactory::randn(&shape_4d, 0.0, 1.0)?;
    
    let output_4d = linear.forward(tensor_4d)?;
    assert_eq!(output_4d.shape().dims(), &[2, 4, 8, 64]);
    println!("  ✓ 4D tensor: [2, 4, 8, 128] -> [2, 4, 8, 64]");
    
    // Test attention with different shapes
    let attn_config = AttentionConfig::new(128, 4);
    let mut attention = MultiHeadAttention::new(attn_config)?;
    attention.init_parameters(ParameterInit::XavierUniform)?;
    
    let attn_output_3d = attention.forward(CpuTensorFactory::randn(&shape_3d, 0.0, 1.0)?)?;
    assert_eq!(attn_output_3d.shape(), &shape_3d);
    println!("  ✓ Attention 3D: [2, 8, 128] -> [2, 8, 128]");
    
    // Test layer normalization with different shapes
    let norm_config = LayerNormConfig::new(vec![128]);
    let mut layer_norm = LayerNorm::new(norm_config)?;
    layer_norm.init_parameters(ParameterInit::Ones)?;
    
    let norm_output_2d = layer_norm.forward(CpuTensorFactory::randn(&shape_2d, 0.0, 1.0)?)?;
    let norm_output_3d = layer_norm.forward(CpuTensorFactory::randn(&shape_3d, 0.0, 1.0)?)?;
    
    assert_eq!(norm_output_2d.shape(), &shape_2d);
    assert_eq!(norm_output_3d.shape(), &shape_3d);
    
    println!("  ✓ LayerNorm 2D: [4, 128] -> [4, 128]");
    println!("  ✓ LayerNorm 3D: [2, 8, 128] -> [2, 8, 128]");
    
    println!("  ✓ Shape compatibility successful!");
    Ok(())
}

/// Test error propagation and handling across components
#[test]
fn test_error_propagation() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing Error Propagation...");
    
    // Test dimension mismatch in linear layer
    let input = CpuTensorFactory::zeros(&Shape::new(vec![2, 64]))?;
    let linear_config = LinearConfig::new(128, 32); // Wrong input dimension
    let mut linear = Linear::new(linear_config)?;
    linear.init_parameters(ParameterInit::XavierUniform)?;
    
    match linear.forward(input) {
        Err(TensorError::ShapeMismatch { .. }) => {
            println!("  ✓ Linear layer correctly reports shape mismatch");
        }
        _ => panic!("Expected shape mismatch error"),
    }
    
    // Test attention with wrong dimensions
    let wrong_input = CpuTensorFactory::zeros(&Shape::new(vec![2, 8, 64]))?;
    let attn_config = AttentionConfig::new(128, 4); // Expects 128-dim input
    let mut attention = MultiHeadAttention::new(attn_config)?;
    attention.init_parameters(ParameterInit::XavierUniform)?;
    
    match attention.forward(wrong_input) {
        Err(_) => {
            println!("  ✓ Attention correctly reports dimension mismatch");
        }
        Ok(_) => panic!("Expected attention error"),
    }
    
    // Test layer norm with wrong normalization dimensions
    let input = CpuTensorFactory::randn(&Shape::new(vec![2, 8, 128]), 0.0, 1.0)?;
    let wrong_norm_config = LayerNormConfig::new(vec![64]); // Wrong norm dimension
    let mut wrong_norm = LayerNorm::new(wrong_norm_config)?;
    wrong_norm.init_parameters(ParameterInit::Ones)?;
    
    match wrong_norm.forward(input) {
        Err(_) => {
            println!("  ✓ LayerNorm correctly reports dimension mismatch");
        }
        Ok(_) => panic!("Expected layer norm error"),
    }
    
    println!("  ✓ Error propagation successful!");
    Ok(())
}

// Helper function to format shape for display
fn format_shape(shape: &Shape) -> String {
    format!("[{}]", shape.dims().iter().map(|d| d.to_string()).collect::<Vec<_>>().join(", "))
}
