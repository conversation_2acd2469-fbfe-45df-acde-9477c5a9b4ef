# Qilin Attention System 使用指南

## 概述

Qilin推理引擎的注意力系统提供了完整的Transformer注意力机制实现，包括：

- **缩放点积注意力** (Scaled Dot-Product Attention)
- **多头注意力** (Multi-Head Attention)  
- **注意力变体** (Self-Attention, Cross-Attention, Causal Attention)
- **位置编码** (Sinusoidal, Learned, Relative, RoPE)
- **KV缓存** (Key-Value Caching) 用于高效生成
- **性能优化** (SIMD, 并行计算, 内存池)

## 快速开始

### 基本多头注意力

```rust
use qilin_inference::attention::{AttentionConfig, MultiHeadAttention};
use qilin_inference::tensor::cpu::CpuTensor;
use qilin_inference::layers::ParameterInit;

// 创建注意力配置
let config = AttentionConfig::new(512, 8)  // hidden_size=512, num_heads=8
    .with_dropout(0.1)
    .with_bias(true);

// 创建多头注意力层
let mut mha = MultiHeadAttention::<f32>::new(config)?;

// 初始化参数
mha.init_parameters(ParameterInit::XavierUniform)?;

// 创建输入张量 [batch_size, seq_len, hidden_size]
let input = CpuTensor::zeros(&[2, 10, 512])?;

// 前向传播
let (output, attention_weights) = mha.forward(&input, &input, &input, None)?;
```

### 因果注意力 (用于语言模型)

```rust
use qilin_inference::attention::variants::{CausalAttention, AttentionVariant};

// 创建因果注意力配置
let config = AttentionConfig::causal(512, 8);
let causal_attn = CausalAttention::<f32>::new(config)?;

// 输入序列
let input = CpuTensor::zeros(&[1, 20, 512])?;

// 应用因果注意力 (自动应用下三角掩码)
let output = causal_attn.forward(&input, None, None)?;
```

### 编码器-解码器交叉注意力

```rust
use qilin_inference::attention::variants::CrossAttention;

let config = AttentionConfig::new(512, 8);
let cross_attn = CrossAttention::<f32>::new(config)?;

// 编码器输出作为上下文
let encoder_output = CpuTensor::zeros(&[1, 15, 512])?;
// 解码器查询
let decoder_input = CpuTensor::zeros(&[1, 10, 512])?;

// 交叉注意力: 解码器查询关注编码器输出
let output = cross_attn.forward(&decoder_input, Some(&encoder_output), None)?;
```

## 位置编码

### 正弦位置编码

```rust
use qilin_inference::attention::positional::{
    SinusoidalPositionalEncoding, PositionalConfig
};

let pos_config = PositionalConfig::new(512, 512);  // max_seq_len, hidden_size
let pos_enc = SinusoidalPositionalEncoding::<f32>::new(pos_config)?;

let embeddings = CpuTensor::zeros(&[1, 100, 512])?;
let encoded = pos_enc.encode(&embeddings, None)?;
```

### 旋转位置编码 (RoPE)

```rust
use qilin_inference::attention::positional::RotaryPositionalEncoding;

let pos_config = PositionalConfig::new(512, 512);
let rope = RotaryPositionalEncoding::<f32>::new(pos_config, 10000.0)?;

let query = CpuTensor::zeros(&[1, 100, 8, 64])?;  // [batch, seq, heads, head_dim]
let key = CpuTensor::zeros(&[1, 100, 8, 64])?;

let (rotated_q, rotated_k) = rope.apply(&query, &key, 0)?;
```

## KV缓存用于高效生成

```rust
use qilin_inference::attention::{KVCache, CacheConfig};

// 创建KV缓存
let cache_config = CacheConfig::new(512, 8, 64, 1024);  // max_seq, heads, head_dim, max_batch
let mut kv_cache = KVCache::new(cache_config)?;

// 存储键值对
let keys = CpuTensor::zeros(&[10, 8, 64])?;    // [seq_len, num_heads, head_dim]
let values = CpuTensor::zeros(&[10, 8, 64])?;
kv_cache.store("sequence_1", keys, values)?;

// 检索键值对
let (cached_keys, cached_values) = kv_cache.get("sequence_1")?;

// 增量生成
let new_keys = CpuTensor::zeros(&[1, 8, 64])?;
let new_values = CpuTensor::zeros(&[1, 8, 64])?;
kv_cache.append("sequence_1", new_keys, new_values)?;
```

## 完整的Transformer块示例

```rust
use qilin_inference::attention::*;
use qilin_inference::layers::ParameterInit;

fn create_transformer_block() -> Result<(), Box<dyn std::error::Error>> {
    let hidden_size = 512;
    let num_heads = 8;
    let seq_len = 100;
    let batch_size = 2;
    
    // 1. 创建多头注意力
    let config = AttentionConfig::new(hidden_size, num_heads)
        .with_dropout(0.1);
    let mut mha = MultiHeadAttention::<f32>::new(config)?;
    mha.init_parameters(ParameterInit::XavierUniform)?;
    
    // 2. 创建位置编码
    let pos_config = PositionalConfig::new(seq_len, hidden_size);
    let pos_enc = SinusoidalPositionalEncoding::<f32>::new(pos_config)?;
    
    // 3. 创建KV缓存
    let cache_config = CacheConfig::new(seq_len, num_heads, hidden_size / num_heads, batch_size);
    let mut kv_cache = KVCache::new(cache_config)?;
    
    // 4. 处理输入
    let input_embeddings = CpuTensor::zeros(&[batch_size, seq_len, hidden_size])?;
    
    // 5. 添加位置编码
    let positioned_input = pos_enc.encode(&input_embeddings, None)?;
    
    // 6. 应用注意力
    let (attention_output, _weights) = mha.forward(
        &positioned_input, 
        &positioned_input, 
        &positioned_input, 
        None
    )?;
    
    println!("Transformer block output shape: {:?}", attention_output.shape());
    
    Ok(())
}
```

## 性能优化建议

### 1. 使用KV缓存进行增量生成

```rust
// 对于自回归生成，使用KV缓存避免重复计算
let mut cache = KVCache::new(cache_config)?;

for step in 0..max_steps {
    // 只计算新token的查询
    let new_query = get_new_token_query(step);
    
    // 使用缓存的键值对
    let (cached_k, cached_v) = cache.get("generation")?;
    let output = attention_with_cache(&new_query, &cached_k, &cached_v)?;
    
    // 更新缓存
    let (new_k, new_v) = compute_new_kv(&output);
    cache.append("generation", new_k, new_v)?;
}
```

### 2. 批处理优化

```rust
// 批量处理多个序列以提高吞吐量
let batch_inputs = vec![seq1, seq2, seq3, seq4];
let batched_input = stack_tensors(&batch_inputs)?;
let batched_output = mha.forward(&batched_input, &batched_input, &batched_input, None)?;
```

### 3. 内存管理

```rust
// 使用内存池减少分配开销
use qilin_inference::tensor::memory_pool::MemoryPool;

let pool = MemoryPool::new(1024 * 1024 * 100)?; // 100MB池
// 注意力计算会自动使用内存池
```

## 错误处理

```rust
use qilin_inference::error::AttentionError;

match mha.forward(&q, &k, &v, None) {
    Ok((output, weights)) => {
        // 处理成功结果
    },
    Err(AttentionError::DimensionMismatch { expected, actual, context }) => {
        eprintln!("维度不匹配: 期望 {:?}, 实际 {:?}", expected, actual);
    },
    Err(AttentionError::InvalidConfiguration { message, .. }) => {
        eprintln!("配置错误: {}", message);
    },
    Err(e) => {
        eprintln!("其他错误: {:?}", e);
    }
}
```

## API参考

### 核心类型

#### `AttentionConfig`
注意力层的配置结构体。

```rust
pub struct AttentionConfig {
    pub hidden_size: usize,    // 隐藏层维度
    pub num_heads: usize,      // 注意力头数
    pub dropout: f32,          // Dropout概率
    pub use_bias: bool,        // 是否使用偏置
    pub scale: Option<f32>,    // 自定义缩放因子
    pub max_seq_len: Option<usize>, // 最大序列长度
    pub causal: bool,          // 是否使用因果掩码
    pub attention_type: AttentionType, // 注意力类型
}
```

**方法:**
- `new(hidden_size, num_heads)` - 创建基本配置
- `causal(hidden_size, num_heads)` - 创建因果注意力配置
- `with_dropout(dropout)` - 设置dropout
- `with_bias(use_bias)` - 设置是否使用偏置
- `with_scale(scale)` - 设置自定义缩放因子

#### `MultiHeadAttention<T>`
多头注意力层的主要实现。

```rust
impl<T: Numeric> MultiHeadAttention<T> {
    pub fn new(config: AttentionConfig) -> Result<Self, AttentionError>;
    pub fn init_parameters(&mut self, init: ParameterInit) -> Result<(), AttentionError>;
    pub fn forward(
        &self,
        query: &CpuTensor<T>,
        key: &CpuTensor<T>,
        value: &CpuTensor<T>,
        mask: Option<&CpuTensor<T>>,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), AttentionError>;
}
```

#### `KVCache`
键值缓存用于高效的自回归生成。

```rust
impl KVCache {
    pub fn new(config: CacheConfig) -> Result<Self, CacheError>;
    pub fn store(&mut self, sequence_id: &str, keys: CpuTensor<f32>, values: CpuTensor<f32>) -> Result<(), CacheError>;
    pub fn get(&self, sequence_id: &str) -> Result<(CpuTensor<f32>, CpuTensor<f32>), CacheError>;
    pub fn append(&mut self, sequence_id: &str, keys: CpuTensor<f32>, values: CpuTensor<f32>) -> Result<(), CacheError>;
    pub fn clear(&mut self, sequence_id: &str) -> Result<(), CacheError>;
}
```

### 位置编码

#### `SinusoidalPositionalEncoding<T>`
正弦位置编码实现。

#### `RotaryPositionalEncoding<T>`
旋转位置编码(RoPE)实现。

### 注意力变体

#### `SelfAttention<T>`
自注意力机制。

#### `CrossAttention<T>`
交叉注意力机制。

#### `CausalAttention<T>`
因果注意力机制。

## 性能优化指南

### 1. 序列长度优化
- 使用合适的序列长度，避免不必要的填充
- 对于长序列，考虑使用稀疏注意力或滑动窗口

### 2. 批处理优化
- 尽可能使用较大的批大小
- 确保批内序列长度相近以减少填充

### 3. 内存优化
- 使用KV缓存进行增量生成
- 合理设置缓存大小避免内存溢出

### 4. 数值精度
- 对于推理，考虑使用f16精度
- 注意数值稳定性，特别是在长序列上

## 基准测试

运行性能基准测试：

```bash
cargo bench --bench attention_benchmarks
```

这将测试：
- 不同序列长度下的性能
- 不同批大小下的性能
- 各种注意力变体的对比
- 位置编码性能
- KV缓存效率
- 内存使用模式

## 下一步

- 查看 `examples/attention_examples.rs` 中的完整示例
- 运行 `cargo bench` 了解性能特征
- 查看测试用例了解最佳实践
- 阅读源码了解实现细节
