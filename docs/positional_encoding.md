# Positional Encoding Module

The positional encoding module provides various methods to inject position information into transformer models. This is crucial for transformer architectures since they lack inherent positional awareness.

## Overview

The module implements four main types of positional encoding:

1. **Sinusoidal Positional Encoding** - Original Transformer approach
2. **Learned Positional Encoding** - BERT-style trainable embeddings
3. **Relative Positional Encoding** - Transformer-XL/T5 approach
4. **Rotary Positional Encoding (RoPE)** - Modern approach used in LLaMA

## Common Interface

All positional encodings implement the `PositionalEncoding<T>` trait:

```rust
pub trait PositionalEncoding<T: Numeric> {
    fn encode(&self, embeddings: &CpuTensor<T>, position_offset: Option<usize>) 
        -> Result<CpuTensor<T>, AttentionError>;
    fn max_seq_len(&self) -> usize;
    fn hidden_size(&self) -> usize;
    fn encoding_type(&self) -> &'static str;
    fn supports_incremental(&self) -> bool;
    fn get_encodings(&self, positions: &[usize], hidden_size: usize) 
        -> Result<CpuTensor<T>, AttentionError>;
}
```

## Configuration

All encodings use the `PositionalConfig` struct:

```rust
let config = PositionalConfig::new(max_seq_len, hidden_size)
    .with_dropout(0.1)
    .with_embedding_scaling()
    .with_custom_scale(2.0);
```

## Encoding Types

### 1. Sinusoidal Positional Encoding

**Formula:**
- PE(pos, 2i) = sin(pos / 10000^(2i/d_model))
- PE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))

**Features:**
- Deterministic, no training required
- Supports arbitrary sequence lengths
- Good for extrapolation to longer sequences
- Supports incremental generation

**Usage:**
```rust
let config = PositionalConfig::new(1024, 512);
let pos_enc = SinusoidalPositionalEncoding::<f32>::new(config)?;
let encoded = pos_enc.encode(&embeddings, None)?;
```

### 2. Learned Positional Encoding

**Features:**
- Trainable parameters for each position
- Used in BERT and GPT models
- Better performance on specific tasks
- Requires training data
- Supports incremental generation

**Usage:**
```rust
let config = PositionalConfig::new(1024, 512);
let mut pos_enc = LearnedPositionalEncoding::<f32>::new(config)?;

// During training, update embeddings
pos_enc.update_embeddings(new_embeddings)?;

let encoded = pos_enc.encode(&embeddings, None)?;
```

### 3. Relative Positional Encoding

**Features:**
- Encodes relative distances between positions
- Better length generalization
- Used in Transformer-XL and T5
- More complex attention computation
- Does not support incremental generation easily

**Usage:**
```rust
let config = PositionalConfig::new(1024, 512);
let max_relative_distance = 64;
let pos_enc = RelativePositionalEncoding::<f32>::new(config, max_relative_distance)?;

// For attention computation
let rel_encodings = pos_enc.get_relative_encodings_for_attention(seq_len, batch_size)?;
```

### 4. Rotary Positional Encoding (RoPE)

**Features:**
- Applies rotation matrices to Q and K vectors
- Naturally incorporates relative position information
- Excellent performance in modern models (LLaMA, GPT-NeoX)
- Supports incremental generation
- No additional parameters

**Usage:**
```rust
let config = PositionalConfig::new(1024, 512);
let base_frequency = 10000.0;
let rope = RotaryPositionalEncoding::<f32>::new(config, base_frequency)?;

let (rotated_q, rotated_k) = rope.apply_rotary_encoding(&query, &key, None)?;
```

## Advanced Features

### Incremental Generation

Most encodings support incremental generation for autoregressive models:

```rust
// Generate with position offset for next token
let encoded = pos_enc.encode(&embeddings, Some(current_position))?;
```

### Embedding Scaling

Scale embeddings before adding positional information:

```rust
let config = PositionalConfig::new(1024, 512)
    .with_embedding_scaling(); // Uses sqrt(hidden_size)
```

### Dropout

Apply dropout to positional encodings during training:

```rust
let config = PositionalConfig::new(1024, 512)
    .with_dropout(0.1);
```

## Performance Considerations

### Memory Usage
- **Sinusoidal**: O(max_seq_len × hidden_size) for precomputed values
- **Learned**: O(max_seq_len × hidden_size) for parameters
- **Relative**: O(max_relative_distance × hidden_size) for parameters
- **RoPE**: O(max_seq_len × hidden_size/2) for precomputed rotation matrices

### Computational Complexity
- **Sinusoidal**: O(seq_len × hidden_size) for addition
- **Learned**: O(seq_len × hidden_size) for addition
- **Relative**: O(seq_len² × hidden_size) for attention computation
- **RoPE**: O(seq_len × hidden_size) for rotation

## Best Practices

### Choosing an Encoding Type

1. **Sinusoidal**: Good default choice, works well for most tasks
2. **Learned**: Use when you have sufficient training data and fixed sequence lengths
3. **Relative**: Use for tasks requiring strong positional relationships
4. **RoPE**: Use for modern transformer architectures, especially for generation tasks

### Configuration Tips

1. Set `max_seq_len` to the maximum sequence you expect to handle
2. Use embedding scaling for better training stability
3. Apply dropout during training to prevent overfitting
4. For RoPE, use base frequency of 10000.0 as a good default

### Integration with Attention

```rust
// Example: Using RoPE with attention
let rope = RotaryPositionalEncoding::<f32>::new(config, 10000.0)?;
let (rotated_q, rotated_k) = rope.apply_rotary_encoding(&query, &key, None)?;

// Use rotated_q and rotated_k in attention computation
let attention_output = attention.forward(&rotated_q, &rotated_k, &value)?;
```

## Error Handling

The module provides comprehensive error handling:

- **InvalidConfiguration**: Invalid parameters or mismatched dimensions
- **DimensionMismatch**: Tensor shape mismatches
- **Tensor**: Underlying tensor operation errors

## Testing

Run the positional encoding tests:

```bash
cargo test positional --lib
```

Run the demonstration example:

```bash
cargo run --example positional_encoding_demo
```

## Future Enhancements

Potential improvements for future versions:

1. **ALiBi (Attention with Linear Biases)**: Alternative to positional encoding
2. **Sandwich positional encoding**: Hybrid approaches
3. **Dynamic positional encoding**: Adaptive position representations
4. **Hardware-optimized implementations**: SIMD and GPU acceleration
5. **Quantized positional encodings**: Memory-efficient representations
