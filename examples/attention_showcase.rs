//! Comprehensive showcase of the attention system.
//!
//! This example demonstrates all the major features of the attention system:
//! - Basic scaled dot-product attention
//! - Multi-head attention
//! - Attention variants (self, cross, causal)
//! - KV caching for efficient generation
//! - Incremental attention for autoregressive models

use qilin_inference::attention::*;
use qilin_inference::tensor::{Tensor, Shape};
use qilin_inference::tensor::cpu::{CpuTensor, CpuTensorFactory};
use qilin_inference::tensor::TensorFactory;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Qilin Inference - Attention System Showcase");
    println!("================================================\n");

    // Configuration
    let hidden_size = 256;
    let num_heads = 8;
    let head_dim = hidden_size / num_heads;
    let batch_size = 2;
    let seq_len = 12;

    println!("📋 Configuration:");
    println!("   Hidden size: {}", hidden_size);
    println!("   Number of heads: {}", num_heads);
    println!("   Head dimension: {}", head_dim);
    println!("   Batch size: {}", batch_size);
    println!("   Sequence length: {}\n", seq_len);

    // Create test data
    let input = CpuTensorFactory::randn(
        &Shape::new(vec![batch_size, seq_len, hidden_size]),
        0.0,
        0.1,
    )?;

    println!("🔧 Created input tensor with shape: {:?}\n", input.shape().dims());

    // 1. Basic Scaled Dot-Product Attention
    println!("1️⃣  Basic Scaled Dot-Product Attention");
    println!("   =====================================");
    
    let sdp_attention = ScaledDotProductAttention::<f32>::new(head_dim, 0.1, false)?;
    
    // Create Q, K, V for single head
    let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, head_dim]), 0.0, 0.1)?;
    let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, head_dim]), 0.0, 0.1)?;
    let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, head_dim]), 0.0, 0.1)?;
    
    let (sdp_output, sdp_weights) = sdp_attention.compute_attention(&query, &key, &value, None)?;
    
    println!("   ✓ Output shape: {:?}", sdp_output.shape().dims());
    println!("   ✓ Attention weights shape: {:?}", sdp_weights.shape().dims());
    println!("   ✓ Scale factor: {:.6}\n", sdp_attention.scale_factor());

    // 2. Multi-Head Attention
    println!("2️⃣  Multi-Head Attention");
    println!("   =====================");
    
    let config = AttentionConfig::new(hidden_size, num_heads)
        .with_dropout(0.1)
        .with_bias(true);
    
    println!("   Config: {}", config.summary());
    
    let mha = MultiHeadAttention::<f32>::new(config.clone())?;
    let (mha_output, mha_weights) = mha.compute_attention(&input, &input, &input, None)?;
    
    println!("   ✓ Output shape: {:?}", mha_output.shape().dims());
    println!("   ✓ Attention weights shape: {:?}", mha_weights.shape().dims());
    println!("   ✓ Number of heads: {}\n", mha.num_heads());

    // 3. Self-Attention Variant
    println!("3️⃣  Self-Attention Variant");
    println!("   ========================");
    
    let self_attn = SelfAttention::<f32>::new(config.clone())?;
    let self_output = self_attn.forward(&input, None, None)?;
    
    println!("   ✓ Attention type: {}", self_attn.attention_type());
    println!("   ✓ Supports causal: {}", self_attn.supports_causal());
    println!("   ✓ Supports cross-attention: {}", self_attn.supports_cross_attention());
    println!("   ✓ Output shape: {:?}\n", self_output.shape().dims());

    // 4. Cross-Attention Variant
    println!("4️⃣  Cross-Attention Variant");
    println!("   =========================");
    
    let cross_attn = CrossAttention::<f32>::new(config.clone())?;
    let encoder_output = CpuTensorFactory::randn(
        &Shape::new(vec![batch_size, seq_len + 4, hidden_size]),
        0.0,
        0.1,
    )?;
    
    let cross_output = cross_attn.forward(&input, Some(&encoder_output), None)?;
    
    println!("   ✓ Attention type: {}", cross_attn.attention_type());
    println!("   ✓ Encoder sequence length: {}", seq_len + 4);
    println!("   ✓ Decoder sequence length: {}", seq_len);
    println!("   ✓ Output shape: {:?}\n", cross_output.shape().dims());

    // 5. Causal Attention Variant
    println!("5️⃣  Causal Attention Variant");
    println!("   ==========================");
    
    let causal_config = AttentionConfig::causal(hidden_size, num_heads);
    let causal_attn = CausalAttention::<f32>::new(causal_config)?;
    
    let (causal_output, causal_weights) = causal_attn.forward_with_weights(&input, None)?;
    
    println!("   ✓ Attention type: {}", causal_attn.attention_type());
    println!("   ✓ Supports causal: {}", causal_attn.supports_causal());
    println!("   ✓ Output shape: {:?}", causal_output.shape().dims());
    
    // Validate causal pattern
    let is_causal = causal_attn.validate_causal_pattern(&causal_weights, 1e-6)?;
    println!("   ✓ Causal pattern validated: {}\n", is_causal);

    // 6. KV Cache System
    println!("6️⃣  KV Cache System");
    println!("   ================");
    
    let cache_config = CacheConfig::new(1024, 16, num_heads, head_dim);
    let mut cache = KVCache::<f32>::new(cache_config)?;
    
    // Store some key-value pairs
    let keys = CpuTensorFactory::randn(&Shape::new(vec![seq_len, num_heads, head_dim]), 0.0, 0.1)?;
    let values = CpuTensorFactory::randn(&Shape::new(vec![seq_len, num_heads, head_dim]), 0.0, 0.1)?;
    
    cache.store("sequence_1", keys, values)?;
    
    let stats = cache.stats();
    println!("   ✓ Cached sequences: {}", stats.num_sequences);
    println!("   ✓ Memory usage: {} bytes", stats.memory_usage);
    println!("   ✓ Cache utilization: {:.1}%\n", stats.utilization);

    // 7. Incremental Attention
    println!("7️⃣  Incremental Attention");
    println!("   ======================");
    
    let inc_config = AttentionConfig::new(hidden_size, num_heads)
        .with_max_seq_len(1024);
    
    let mut inc_attn = IncrementalAttention::<f32>::new(inc_config)?;
    
    // Initial forward pass
    let initial_input = CpuTensorFactory::randn(
        &Shape::new(vec![1, 5, hidden_size]),
        0.0,
        0.1,
    )?;
    
    let (initial_output, _) = inc_attn.forward_initial("gen_seq", &initial_input)?;
    println!("   ✓ Initial sequence length: 5");
    println!("   ✓ Initial output shape: {:?}", initial_output.shape().dims());
    
    // Incremental forward pass
    let new_token = CpuTensorFactory::randn(
        &Shape::new(vec![1, 1, hidden_size]),
        0.0,
        0.1,
    )?;
    
    let (inc_output, _) = inc_attn.forward_incremental("gen_seq", &new_token)?;
    println!("   ✓ Added 1 new token");
    println!("   ✓ Incremental output shape: {:?}", inc_output.shape().dims());
    println!("   ✓ Total sequence length: {:?}", inc_attn.get_sequence_length("gen_seq"));
    
    let cache_stats = inc_attn.cache_stats();
    println!("   ✓ Cache sequences: {}\n", cache_stats.num_sequences);

    // 8. Performance Comparison
    println!("8️⃣  Performance Comparison");
    println!("   ========================");
    
    let test_input = CpuTensorFactory::randn(
        &Shape::new(vec![1, 100, hidden_size]),
        0.0,
        0.1,
    )?;
    
    // Standard multi-head attention
    let start = std::time::Instant::now();
    let _ = mha.compute_attention(&test_input, &test_input, &test_input, None)?;
    let mha_time = start.elapsed();
    
    // Self-attention variant
    let start = std::time::Instant::now();
    let _ = self_attn.forward(&test_input, None, None)?;
    let self_time = start.elapsed();
    
    println!("   ✓ Multi-head attention: {:?}", mha_time);
    println!("   ✓ Self-attention variant: {:?}", self_time);
    println!("   ✓ Speedup ratio: {:.2}x\n", mha_time.as_nanos() as f64 / self_time.as_nanos() as f64);

    println!("🎉 Attention System Showcase Complete!");
    println!("   All components working correctly!");
    println!("   Ready for production use in transformer models.");

    Ok(())
}
