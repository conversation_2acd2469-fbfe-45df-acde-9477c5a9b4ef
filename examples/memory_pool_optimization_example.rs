//! Memory Pool Optimization Example
//!
//! This example demonstrates the enhanced memory pool features including:
//! - Size bucketing for better memory reuse
//! - Buffer aging and automatic cleanup
//! - Memory compaction
//! - Batch allocation/deallocation
//! - Performance monitoring and statistics

use qilin_inference::tensor::memory_pool::{TensorMemoryPool, MemoryPoolConfig, GlobalMemoryPool};
use qilin_inference::tensor::cpu::{CpuTensor, PooledCpuTensorFactory};
use qilin_inference::tensor::{TensorFactory, Shape, Tensor};
use std::time::Instant;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Memory Pool Optimization Example ===\n");
    
    // Test 1: Enhanced Configuration
    test_enhanced_configuration()?;
    
    // Test 2: Size Bucketing
    test_size_bucketing()?;
    
    // Test 3: Batch Operations
    test_batch_operations()?;
    
    // Test 4: Buffer Aging and Cleanup
    test_buffer_aging()?;
    
    // Test 5: Performance Comparison
    test_performance_comparison()?;
    
    // Test 6: Global Pool Usage
    test_global_pool()?;
    
    println!("\n=== All Memory Pool Tests Completed Successfully! ===");
    Ok(())
}

fn test_enhanced_configuration() -> Result<(), Box<dyn std::error::Error>> {
    println!("1. Testing Enhanced Configuration");
    
    let config = MemoryPoolConfig {
        max_buffers_per_bucket: 10,
        max_total_memory: 1024 * 1024, // 1MB
        enable_stats: true,
        min_pooled_size: 64,
        enable_size_bucketing: true,
        preallocation_sizes: vec![128, 256, 512, 1024],
        max_buffer_age: 300, // 5 minutes
        enable_compaction: true,
    };
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Check initial stats after preallocation
    let stats = pool.stats();
    println!("  Initial pooled buffers: {}", stats.current_pooled_buffers);
    println!("  Initial pooled memory: {} bytes", stats.current_pooled_memory);
    println!("  Peak memory usage: {} bytes", stats.peak_memory_usage);
    
    // Allocate some buffers
    let buffer1 = pool.allocate(128)?;
    let buffer2 = pool.allocate(256)?;
    let buffer3 = pool.allocate(512)?;
    
    let stats = pool.stats();
    println!("  After allocation - Hit rate: {:.1}%", stats.hit_rate());
    println!("  Pool hits: {}, Pool misses: {}", stats.pool_hits, stats.pool_misses);
    
    // Return buffers
    pool.deallocate(buffer1);
    pool.deallocate(buffer2);
    pool.deallocate(buffer3);
    
    let stats = pool.stats();
    println!("  After deallocation - Return rate: {:.1}%", stats.return_rate());
    println!("  Average allocation size: {:.1} bytes", stats.avg_allocation_size());
    println!("  Memory efficiency: {:.1}%", stats.memory_efficiency() * 100.0);
    println!("  Summary: {}", stats.summary());
    
    println!("  ✓ Enhanced configuration test passed\n");
    Ok(())
}

fn test_size_bucketing() -> Result<(), Box<dyn std::error::Error>> {
    println!("2. Testing Size Bucketing");
    
    let config = MemoryPoolConfig {
        enable_size_bucketing: true,
        enable_stats: true,
        ..Default::default()
    };
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Allocate buffers with sizes that should be bucketed
    let sizes = vec![100, 150, 200, 300]; // Should be bucketed to powers of 2
    let mut buffers = Vec::new();
    
    for size in &sizes {
        let buffer = pool.allocate(*size)?;
        println!("  Allocated buffer of size {} (actual capacity: {})", size, buffer.capacity());
        buffers.push(buffer);
    }
    
    // Return all buffers
    for buffer in buffers {
        pool.deallocate(buffer);
    }
    
    // Now allocate similar sizes - should get hits due to bucketing
    for size in &sizes {
        let _buffer = pool.allocate(*size)?;
    }
    
    let stats = pool.stats();
    println!("  Hit rate with bucketing: {:.1}%", stats.hit_rate());
    println!("  ✓ Size bucketing test passed\n");
    Ok(())
}

fn test_batch_operations() -> Result<(), Box<dyn std::error::Error>> {
    println!("3. Testing Batch Operations");
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::default();
    
    // Test batch allocation
    let start = Instant::now();
    let buffers = pool.allocate_batch(1024, 10)?;
    let batch_time = start.elapsed();
    
    println!("  Batch allocated 10 buffers of size 1024 in {:?}", batch_time);
    println!("  All buffers have correct size: {}", 
             buffers.iter().all(|b| b.len() == 1024));
    
    // Test batch deallocation
    let start = Instant::now();
    pool.deallocate_batch(buffers);
    let batch_dealloc_time = start.elapsed();
    
    println!("  Batch deallocated 10 buffers in {:?}", batch_dealloc_time);
    
    // Compare with individual operations
    let start = Instant::now();
    let mut individual_buffers = Vec::new();
    for _ in 0..10 {
        individual_buffers.push(pool.allocate(1024)?);
    }
    let individual_time = start.elapsed();
    
    println!("  Individual allocation of 10 buffers took {:?}", individual_time);
    println!("  Batch allocation speedup: {:.2}x", 
             individual_time.as_nanos() as f64 / batch_time.as_nanos() as f64);
    
    // Clean up
    for buffer in individual_buffers {
        pool.deallocate(buffer);
    }
    
    println!("  ✓ Batch operations test passed\n");
    Ok(())
}

fn test_buffer_aging() -> Result<(), Box<dyn std::error::Error>> {
    println!("4. Testing Buffer Aging and Cleanup");
    
    let config = MemoryPoolConfig {
        max_buffer_age: 1, // 1 second for testing
        enable_stats: true,
        enable_compaction: true,
        ..Default::default()
    };
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Allocate and return some buffers
    let buffer1 = pool.allocate(1024)?;
    let buffer2 = pool.allocate(2048)?;
    pool.deallocate(buffer1);
    pool.deallocate(buffer2);
    
    let stats_before = pool.stats();
    println!("  Buffers before cleanup: {}", stats_before.current_pooled_buffers);
    println!("  Memory before cleanup: {} bytes", stats_before.current_pooled_memory);
    
    // Wait for buffers to age
    std::thread::sleep(std::time::Duration::from_secs(2));
    
    // Trigger cleanup
    pool.cleanup_old_buffers();
    
    let stats_after = pool.stats();
    println!("  Buffers after cleanup: {}", stats_after.current_pooled_buffers);
    println!("  Memory after cleanup: {} bytes", stats_after.current_pooled_memory);
    println!("  Age cleanups performed: {}", stats_after.age_cleanups);
    
    println!("  ✓ Buffer aging test passed\n");
    Ok(())
}

fn test_performance_comparison() -> Result<(), Box<dyn std::error::Error>> {
    println!("5. Testing Performance Comparison");
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::default();
    let iterations = 1000;
    let buffer_size = 1024;
    
    // Test pooled allocation performance
    let start = Instant::now();
    for _ in 0..iterations {
        let buffer = pool.allocate(buffer_size)?;
        pool.deallocate(buffer);
    }
    let pooled_time = start.elapsed();
    
    // Test direct allocation performance
    let start = Instant::now();
    for _ in 0..iterations {
        let _buffer: Vec<f32> = vec![0.0; buffer_size];
        // Buffer is automatically dropped
    }
    let direct_time = start.elapsed();
    
    println!("  Pooled allocation time: {:?}", pooled_time);
    println!("  Direct allocation time: {:?}", direct_time);
    
    let stats = pool.stats();
    println!("  Pool hit rate: {:.1}%", stats.hit_rate());
    println!("  Pool return rate: {:.1}%", stats.return_rate());
    
    if pooled_time < direct_time {
        let speedup = direct_time.as_nanos() as f64 / pooled_time.as_nanos() as f64;
        println!("  Pooled allocation is {:.2}x faster!", speedup);
    } else {
        println!("  Direct allocation is faster (expected for small buffers)");
    }
    
    println!("  ✓ Performance comparison test passed\n");
    Ok(())
}

fn test_global_pool() -> Result<(), Box<dyn std::error::Error>> {
    println!("6. Testing Global Pool Usage");
    
    // Test global f32 pool
    let f32_pool = GlobalMemoryPool::f32();
    let buffer1 = f32_pool.allocate(512)?;
    println!("  Allocated f32 buffer of size: {}", buffer1.len());
    f32_pool.deallocate(buffer1);
    
    // Test global f64 pool
    let f64_pool = GlobalMemoryPool::f64();
    let buffer2 = f64_pool.allocate(256)?;
    println!("  Allocated f64 buffer of size: {}", buffer2.len());
    f64_pool.deallocate(buffer2);
    
    // Test pooled tensor factory
    let shape = Shape::new(vec![32, 32]);
    let tensor1 = PooledCpuTensorFactory::<f32>::zeros(&shape)?;
    println!("  Created pooled tensor with shape: {:?}", tensor1.shape());
    
    let tensor2 = PooledCpuTensorFactory::<f32>::ones(&shape)?;
    println!("  Created pooled ones tensor with shape: {:?}", tensor2.shape());
    
    println!("  ✓ Global pool test passed\n");
    Ok(())
}
