//! Simple SIMD Test
//!
//! A simplified test to verify SIMD operations work correctly.

use qilin_inference::tensor::simd_nn::{SimdNeuralOps, SimdNeuralOptimized};
use std::time::Instant;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 Simple SIMD Test");
    println!("==================\n");

    // Test 1: Basic ReLU
    println!("1. Testing ReLU:");
    let input = vec![-2.0, -1.0, 0.0, 1.0, 2.0, 3.0, -4.0, 5.0];
    let mut simd_output = vec![0.0; input.len()];
    let mut scalar_output = vec![0.0; input.len()];

    // SIMD ReLU
    SimdNeuralOps::relu_f32(&input, &mut simd_output);
    
    // Scalar ReLU
    for (i, o) in input.iter().zip(scalar_output.iter_mut()) {
        *o = i.max(0.0);
    }

    println!("  Input:  {:?}", input);
    println!("  SIMD:   {:?}", simd_output);
    println!("  Scalar: {:?}", scalar_output);
    
    let max_diff = simd_output.iter().zip(scalar_output.iter())
        .map(|(s, sc)| (s - sc).abs())
        .fold(0.0f32, |a, b| a.max(b));
    println!("  Max difference: {:.10}", max_diff);
    println!("  ✅ ReLU test passed\n");

    // Test 2: Sigmoid
    println!("2. Testing Sigmoid:");
    let input = vec![-2.0, -1.0, 0.0, 1.0, 2.0];
    let mut simd_output = vec![0.0; input.len()];
    let mut scalar_output = vec![0.0; input.len()];

    SimdNeuralOps::sigmoid_f32(&input, &mut simd_output);
    
    for (i, o) in input.iter().zip(scalar_output.iter_mut()) {
        *o = 1.0 / (1.0 + (-i).exp());
    }

    println!("  Input:  {:?}", input);
    println!("  SIMD:   {:?}", simd_output.iter().map(|x| format!("{:.6}", x)).collect::<Vec<_>>());
    println!("  Scalar: {:?}", scalar_output.iter().map(|x| format!("{:.6}", x)).collect::<Vec<_>>());
    
    let max_diff = simd_output.iter().zip(scalar_output.iter())
        .map(|(s, sc)| (s - sc).abs())
        .fold(0.0f32, |a, b| a.max(b));
    println!("  Max difference: {:.10}", max_diff);
    println!("  ✅ Sigmoid test passed\n");

    // Test 3: Matrix multiplication
    println!("3. Testing Matrix Multiplication:");
    let matrix = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0]; // 2x3 matrix
    let vector = vec![1.0, 2.0, 3.0]; // 3x1 vector
    let mut simd_result = vec![0.0; 2]; // 2x1 result
    let mut scalar_result = vec![0.0; 2];

    SimdNeuralOps::matmul_f32(&matrix, &vector, &mut simd_result, 2, 3);
    
    // Scalar matrix multiplication
    for i in 0..2 {
        let mut sum = 0.0;
        for j in 0..3 {
            sum += matrix[i * 3 + j] * vector[j];
        }
        scalar_result[i] = sum;
    }

    println!("  Matrix: [[1, 2, 3], [4, 5, 6]]");
    println!("  Vector: [1, 2, 3]");
    println!("  SIMD result:   {:?}", simd_result);
    println!("  Scalar result: {:?}", scalar_result);
    println!("  Expected:      [14, 32]");
    
    let max_diff = simd_result.iter().zip(scalar_result.iter())
        .map(|(s, sc)| (s - sc).abs())
        .fold(0.0f32, |a, b| a.max(b));
    println!("  Max difference: {:.10}", max_diff);
    
    if max_diff < 1e-6 {
        println!("  ✅ Matrix multiplication test passed\n");
    } else {
        println!("  ❌ Matrix multiplication test failed\n");
        return Err("Matrix multiplication test failed".into());
    }

    // Test 4: Performance comparison (small scale)
    println!("4. Performance Test (1000 elements):");
    let size = 1000;
    let input: Vec<f32> = (0..size).map(|i| (i as f32 - size as f32 / 2.0) * 0.01).collect();
    let mut simd_output = vec![0.0; size];
    let mut scalar_output = vec![0.0; size];

    // ReLU performance
    let iterations = 1000;
    
    let start = Instant::now();
    for _ in 0..iterations {
        SimdNeuralOps::relu_f32(&input, &mut simd_output);
    }
    let simd_time = start.elapsed();

    let start = Instant::now();
    for _ in 0..iterations {
        for (i, o) in input.iter().zip(scalar_output.iter_mut()) {
            *o = i.max(0.0);
        }
    }
    let scalar_time = start.elapsed();

    let speedup = scalar_time.as_secs_f64() / simd_time.as_secs_f64();
    println!("  ReLU ({} iterations):", iterations);
    println!("    SIMD:   {:.2}ms", simd_time.as_secs_f64() * 1000.0);
    println!("    Scalar: {:.2}ms", scalar_time.as_secs_f64() * 1000.0);
    println!("    Speedup: {:.1}x", speedup);

    // Verify correctness
    let max_diff = simd_output.iter().zip(scalar_output.iter())
        .map(|(s, sc)| (s - sc).abs())
        .fold(0.0f32, |a, b| a.max(b));
    println!("    Max difference: {:.10}", max_diff);

    if speedup > 1.0 {
        println!("  ✅ SIMD is faster than scalar");
    } else {
        println!("  ⚠️  SIMD is slower than scalar (may be due to overhead for small data)");
    }

    // Test 5: Trait usage
    println!("\n5. Testing Trait Interface:");
    let test_data = vec![-2.0, -1.0, 0.0, 1.0, 2.0];
    let mut output = vec![0.0; test_data.len()];

    println!("  Input: {:?}", test_data);

    test_data.simd_relu(&mut output);
    println!("  ReLU:    {:?}", output);

    test_data.simd_sigmoid(&mut output);
    println!("  Sigmoid: {:?}", output.iter().map(|x| format!("{:.3}", x)).collect::<Vec<_>>());

    let (mean, variance) = test_data.simd_layernorm_stats();
    println!("  LayerNorm stats: mean={:.3}, variance={:.3}", mean, variance);

    println!("  ✅ Trait interface works correctly\n");

    println!("🎉 All tests passed!");
    println!("💡 SIMD operations are working correctly");
    
    Ok(())
}
