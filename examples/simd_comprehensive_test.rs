//! Comprehensive SIMD Neural Network Operations Test
//!
//! This example demonstrates and validates all SIMD-accelerated neural network
//! operations with comprehensive correctness checks and performance measurements.

use qilin_inference::tensor::simd_nn::{SimdNeuralOps, SimdNeuralOptimized};
use std::time::Instant;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 Comprehensive SIMD Neural Network Operations Test");
    println!("===================================================\n");

    // Test all activation functions
    test_activation_functions()?;
    
    // Test normalization operations
    test_normalization_operations()?;
    
    // Test matrix operations
    test_matrix_operations()?;
    
    // Test performance scaling
    test_performance_scaling()?;
    
    // Test trait interface
    test_trait_interface()?;

    println!("🎉 All SIMD tests passed successfully!");
    println!("✅ SIMD neural network operations are working correctly");
    println!("⚡ Ready for production use with --features simd");
    
    Ok(())
}

fn test_activation_functions() -> Result<(), Box<dyn std::error::Error>> {
    println!("1. Testing Activation Functions");
    println!("-------------------------------");

    let test_inputs = vec![
        vec![-3.0, -2.0, -1.0, 0.0, 1.0, 2.0, 3.0, 4.0],
        vec![-10.0, -5.0, -1.0, -0.1, 0.0, 0.1, 1.0, 5.0, 10.0],
        (0..100).map(|i| (i as f32 - 50.0) * 0.1).collect(),
    ];

    for (test_idx, input) in test_inputs.iter().enumerate() {
        println!("  Test case {} ({} elements):", test_idx + 1, input.len());
        
        let mut simd_output = vec![0.0; input.len()];
        let mut scalar_output = vec![0.0; input.len()];

        // Test ReLU
        SimdNeuralOps::relu_f32(input, &mut simd_output);
        for (i, o) in input.iter().zip(scalar_output.iter_mut()) {
            *o = i.max(0.0);
        }
        let relu_diff = max_difference(&simd_output, &scalar_output);
        println!("    ReLU max diff: {:.2e}", relu_diff);
        assert!(relu_diff < 1e-6, "ReLU difference too large: {}", relu_diff);

        // Test Sigmoid
        SimdNeuralOps::sigmoid_f32(input, &mut simd_output);
        for (i, o) in input.iter().zip(scalar_output.iter_mut()) {
            *o = 1.0 / (1.0 + (-i).exp());
        }
        let sigmoid_diff = max_difference(&simd_output, &scalar_output);
        println!("    Sigmoid max diff: {:.2e}", sigmoid_diff);
        assert!(sigmoid_diff < 1e-4, "Sigmoid difference too large: {}", sigmoid_diff);

        // Test Tanh
        SimdNeuralOps::tanh_f32(input, &mut simd_output);
        for (i, o) in input.iter().zip(scalar_output.iter_mut()) {
            *o = i.tanh();
        }
        let tanh_diff = max_difference(&simd_output, &scalar_output);
        println!("    Tanh max diff: {:.2e}", tanh_diff);
        assert!(tanh_diff < 1e-4, "Tanh difference too large: {}", tanh_diff);

        // Test GELU
        SimdNeuralOps::gelu_f32(input, &mut simd_output);
        for (i, o) in input.iter().zip(scalar_output.iter_mut()) {
            let x = *i;
            let inner = 0.7978845608 * (x + 0.044715 * x * x * x);
            *o = 0.5 * x * (1.0 + inner.tanh());
        }
        let gelu_diff = max_difference(&simd_output, &scalar_output);
        println!("    GELU max diff: {:.2e}", gelu_diff);
        assert!(gelu_diff < 1e-3, "GELU difference too large: {}", gelu_diff);

        // Test Swish
        SimdNeuralOps::swish_f32(input, &mut simd_output);
        for (i, o) in input.iter().zip(scalar_output.iter_mut()) {
            let x = *i;
            let sigmoid = 1.0 / (1.0 + (-x).exp());
            *o = x * sigmoid;
        }
        let swish_diff = max_difference(&simd_output, &scalar_output);
        println!("    Swish max diff: {:.2e}", swish_diff);
        assert!(swish_diff < 1e-4, "Swish difference too large: {}", swish_diff);

        // Test Leaky ReLU
        let alpha = 0.01;
        SimdNeuralOps::leaky_relu_f32(input, &mut simd_output, alpha);
        for (i, o) in input.iter().zip(scalar_output.iter_mut()) {
            *o = i.max(alpha * i);
        }
        let leaky_relu_diff = max_difference(&simd_output, &scalar_output);
        println!("    Leaky ReLU max diff: {:.2e}", leaky_relu_diff);
        assert!(leaky_relu_diff < 1e-6, "Leaky ReLU difference too large: {}", leaky_relu_diff);
    }

    println!("  ✅ All activation functions passed\n");
    Ok(())
}

fn test_normalization_operations() -> Result<(), Box<dyn std::error::Error>> {
    println!("2. Testing Normalization Operations");
    println!("-----------------------------------");

    let test_cases = vec![
        vec![1.0, 2.0, 3.0, 4.0, 5.0],
        vec![-2.0, -1.0, 0.0, 1.0, 2.0],
        (0..64).map(|i| (i as f32) * 0.1 - 3.2).collect(),
        (0..128).map(|i| ((i as f32).sin() * 10.0)).collect(),
    ];

    for (test_idx, input) in test_cases.iter().enumerate() {
        println!("  Test case {} ({} elements):", test_idx + 1, input.len());

        // Test LayerNorm stats
        let (simd_mean, simd_var) = SimdNeuralOps::layernorm_stats_f32(input);
        let scalar_mean = input.iter().sum::<f32>() / input.len() as f32;
        let scalar_var = input.iter()
            .map(|x| (x - scalar_mean).powi(2))
            .sum::<f32>() / input.len() as f32;

        let mean_diff = (simd_mean - scalar_mean).abs();
        let var_diff = (simd_var - scalar_var).abs();
        println!("    LayerNorm stats - Mean diff: {:.2e}, Var diff: {:.2e}", mean_diff, var_diff);
        assert!(mean_diff < 1e-5, "Mean difference too large: {}", mean_diff);
        assert!(var_diff < 1e-5, "Variance difference too large: {}", var_diff);

        // Test LayerNorm
        let scale = vec![1.0; input.len()];
        let bias = vec![0.0; input.len()];
        let mut simd_output = vec![0.0; input.len()];
        let mut scalar_output = vec![0.0; input.len()];
        let eps = 1e-5;

        SimdNeuralOps::layernorm_f32(input, &mut simd_output, &scale, &bias, eps);
        
        let inv_std = 1.0 / (scalar_var + eps).sqrt();
        for (((&inp, &sc), &bi), out) in input.iter()
            .zip(scale.iter())
            .zip(bias.iter())
            .zip(scalar_output.iter_mut()) {
            let normalized = (inp - scalar_mean) * inv_std;
            *out = normalized * sc + bi;
        }

        let layernorm_diff = max_difference(&simd_output, &scalar_output);
        println!("    LayerNorm max diff: {:.2e}", layernorm_diff);
        assert!(layernorm_diff < 1e-4, "LayerNorm difference too large: {}", layernorm_diff);

        // Test Softmax
        let mut simd_softmax = vec![0.0; input.len()];
        let mut scalar_softmax = vec![0.0; input.len()];

        SimdNeuralOps::softmax_f32(input, &mut simd_softmax);
        
        let max_val = input.iter().fold(f32::NEG_INFINITY, |a, &b| a.max(b));
        let mut sum = 0.0;
        for (inp, out) in input.iter().zip(scalar_softmax.iter_mut()) {
            *out = (inp - max_val).exp();
            sum += *out;
        }
        let inv_sum = 1.0 / sum;
        for out in scalar_softmax.iter_mut() {
            *out *= inv_sum;
        }

        let softmax_diff = max_difference(&simd_softmax, &scalar_softmax);
        println!("    Softmax max diff: {:.2e}", softmax_diff);
        assert!(softmax_diff < 1e-4, "Softmax difference too large: {}", softmax_diff);

        // Verify softmax sums to 1
        let simd_sum: f32 = simd_softmax.iter().sum();
        let scalar_sum: f32 = scalar_softmax.iter().sum();
        println!("    Softmax sums - SIMD: {:.6}, Scalar: {:.6}", simd_sum, scalar_sum);
        assert!((simd_sum - 1.0).abs() < 1e-5, "SIMD softmax doesn't sum to 1: {}", simd_sum);
        assert!((scalar_sum - 1.0).abs() < 1e-5, "Scalar softmax doesn't sum to 1: {}", scalar_sum);
    }

    println!("  ✅ All normalization operations passed\n");
    Ok(())
}

fn test_matrix_operations() -> Result<(), Box<dyn std::error::Error>> {
    println!("3. Testing Matrix Operations");
    println!("----------------------------");

    let test_cases = vec![
        (2, 3),   // Small matrix
        (4, 4),   // Square matrix
        (10, 8),  // Medium matrix
        (50, 32), // Larger matrix
    ];

    for &(rows, cols) in &test_cases {
        println!("  Testing {}x{} matrix-vector multiplication:", rows, cols);

        let matrix: Vec<f32> = (0..rows * cols).map(|i| (i as f32) * 0.01).collect();
        let vector: Vec<f32> = (0..cols).map(|i| 1.0 + (i as f32) * 0.01).collect();
        let mut simd_result = vec![0.0; rows];
        let mut scalar_result = vec![0.0; rows];

        SimdNeuralOps::matmul_f32(&matrix, &vector, &mut simd_result, rows, cols);

        for i in 0..rows {
            let mut sum = 0.0;
            for j in 0..cols {
                sum += matrix[i * cols + j] * vector[j];
            }
            scalar_result[i] = sum;
        }

        let matmul_diff = max_difference(&simd_result, &scalar_result);
        println!("    Max difference: {:.2e}", matmul_diff);
        assert!(matmul_diff < 1e-4, "Matrix multiplication difference too large: {}", matmul_diff);
    }

    println!("  ✅ All matrix operations passed\n");
    Ok(())
}

fn test_performance_scaling() -> Result<(), Box<dyn std::error::Error>> {
    println!("4. Testing Performance Scaling");
    println!("------------------------------");

    let sizes = vec![100, 1000, 10000];
    let iterations = 100;

    for &size in &sizes {
        println!("  Testing with {} elements ({} iterations):", size, iterations);
        
        let input: Vec<f32> = (0..size).map(|i| (i as f32 - size as f32 / 2.0) * 0.001).collect();
        let mut simd_output = vec![0.0; size];
        let mut scalar_output = vec![0.0; size];

        // Test ReLU performance
        let start = Instant::now();
        for _ in 0..iterations {
            SimdNeuralOps::relu_f32(&input, &mut simd_output);
        }
        let simd_time = start.elapsed();

        let start = Instant::now();
        for _ in 0..iterations {
            for (i, o) in input.iter().zip(scalar_output.iter_mut()) {
                *o = i.max(0.0);
            }
        }
        let scalar_time = start.elapsed();

        let speedup = scalar_time.as_secs_f64() / simd_time.as_secs_f64();
        println!("    ReLU - SIMD: {:.2}ms, Scalar: {:.2}ms, Speedup: {:.1}x",
                 simd_time.as_secs_f64() * 1000.0,
                 scalar_time.as_secs_f64() * 1000.0,
                 speedup);

        // Verify correctness
        let diff = max_difference(&simd_output, &scalar_output);
        assert!(diff < 1e-6, "Performance test correctness failed: {}", diff);
    }

    println!("  ✅ Performance scaling tests completed\n");
    Ok(())
}

fn test_trait_interface() -> Result<(), Box<dyn std::error::Error>> {
    println!("5. Testing Trait Interface");
    println!("--------------------------");

    let input = vec![-2.0, -1.0, 0.0, 1.0, 2.0, 3.0, 4.0, 5.0];
    let mut output = vec![0.0; input.len()];

    println!("  Input: {:?}", input);

    // Test all trait methods
    input.simd_relu(&mut output);
    println!("  ReLU:    {:?}", output);

    input.simd_leaky_relu(&mut output, 0.1);
    println!("  Leaky ReLU: {:?}", output.iter().map(|x| format!("{:.2}", x)).collect::<Vec<_>>());

    input.simd_sigmoid(&mut output);
    println!("  Sigmoid: {:?}", output.iter().map(|x| format!("{:.3}", x)).collect::<Vec<_>>());

    input.simd_tanh(&mut output);
    println!("  Tanh:    {:?}", output.iter().map(|x| format!("{:.3}", x)).collect::<Vec<_>>());

    input.simd_gelu(&mut output);
    println!("  GELU:    {:?}", output.iter().map(|x| format!("{:.3}", x)).collect::<Vec<_>>());

    input.simd_swish(&mut output);
    println!("  Swish:   {:?}", output.iter().map(|x| format!("{:.3}", x)).collect::<Vec<_>>());

    input.simd_softmax(&mut output);
    println!("  Softmax: {:?}", output.iter().map(|x| format!("{:.4}", x)).collect::<Vec<_>>());
    let sum: f32 = output.iter().sum();
    println!("  Softmax sum: {:.6}", sum);

    let (mean, variance) = input.simd_layernorm_stats();
    println!("  LayerNorm stats: mean={:.3}, variance={:.3}", mean, variance);

    let scale = vec![1.0; input.len()];
    let bias = vec![0.0; input.len()];
    input.simd_layernorm(&mut output, &scale, &bias, 1e-5);
    println!("  LayerNorm: {:?}", output.iter().map(|x| format!("{:.3}", x)).collect::<Vec<_>>());

    println!("  ✅ Trait interface works correctly\n");
    Ok(())
}

fn max_difference(a: &[f32], b: &[f32]) -> f32 {
    a.iter().zip(b.iter())
        .map(|(x, y)| (x - y).abs())
        .fold(0.0f32, |acc, diff| acc.max(diff))
}
