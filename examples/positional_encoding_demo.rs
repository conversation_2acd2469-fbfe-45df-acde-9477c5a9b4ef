//! Positional encoding demonstration.
//!
//! This example shows how to use different types of positional encodings
//! in the Qilin inference engine.

use qilin_inference::attention::positional::{
    PositionalEncoding, PositionalConfig,
    SinusoidalPositionalEncoding, LearnedPositionalEncoding,
    RelativePositionalEncoding, RotaryPositionalEncoding,
};
use qilin_inference::tensor::{Tensor, Shape};
use qilin_inference::tensor::cpu::{CpuTensor, CpuTensorFactory};
use qilin_inference::tensor::TensorFactory;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Positional Encoding Demo ===\n");
    
    // Configuration
    let max_seq_len = 128;
    let hidden_size = 64;
    let batch_size = 2;
    let seq_len = 10;
    
    let config = PositionalConfig::new(max_seq_len, hidden_size)
        .with_dropout(0.1)
        .with_embedding_scaling();
    
    println!("Configuration: {}", config.summary());
    println!();
    
    // Create sample embeddings
    let embeddings = CpuTensorFactory::ones(&Shape::new(vec![batch_size, seq_len, hidden_size]))?;
    println!("Input embeddings shape: {:?}", embeddings.shape().dims());
    println!();
    
    // 1. Sinusoidal Positional Encoding
    println!("1. Sinusoidal Positional Encoding");
    println!("----------------------------------");
    
    let sinusoidal = SinusoidalPositionalEncoding::<f32>::new(config.clone())?;
    println!("Type: {}", sinusoidal.encoding_type());
    println!("Max sequence length: {}", sinusoidal.max_seq_len());
    println!("Supports incremental: {}", sinusoidal.supports_incremental());
    
    let encoded_sin = sinusoidal.encode(&embeddings, None)?;
    println!("Encoded shape: {:?}", encoded_sin.shape().dims());
    
    // Show some values
    let sin_data = encoded_sin.data();
    println!("First few values: [{:.4}, {:.4}, {:.4}, {:.4}]", 
             sin_data[0], sin_data[1], sin_data[2], sin_data[3]);
    println!();
    
    // 2. Learned Positional Encoding
    println!("2. Learned Positional Encoding");
    println!("------------------------------");
    
    let mut learned = LearnedPositionalEncoding::<f32>::zeros(config.clone())?;
    
    // Initialize with some pattern for demo
    let mut pos_data = vec![0.0; max_seq_len * hidden_size];
    for i in 0..pos_data.len() {
        pos_data[i] = (i % 10) as f32 * 0.01;
    }
    let pos_embeddings = CpuTensor::from_data(pos_data, Shape::new(vec![max_seq_len, hidden_size]))?;
    learned.update_embeddings(pos_embeddings)?;
    
    println!("Type: {}", learned.encoding_type());
    println!("Max sequence length: {}", learned.max_seq_len());
    println!("Supports incremental: {}", learned.supports_incremental());
    
    let encoded_learned = learned.encode(&embeddings, None)?;
    println!("Encoded shape: {:?}", encoded_learned.shape().dims());
    
    let learned_data = encoded_learned.data();
    println!("First few values: [{:.4}, {:.4}, {:.4}, {:.4}]", 
             learned_data[0], learned_data[1], learned_data[2], learned_data[3]);
    println!();
    
    // 3. Relative Positional Encoding
    println!("3. Relative Positional Encoding");
    println!("-------------------------------");
    
    let relative = RelativePositionalEncoding::<f32>::new(config.clone(), 32)?;
    println!("Type: {}", relative.encoding_type());
    println!("Max sequence length: {}", relative.max_seq_len());
    println!("Max relative distance: {}", relative.max_relative_distance());
    println!("Supports incremental: {}", relative.supports_incremental());
    
    let encoded_relative = relative.encode(&embeddings, None)?;
    println!("Encoded shape: {:?}", encoded_relative.shape().dims());
    
    // Get relative encodings for attention
    let rel_attention = relative.get_relative_encodings_for_attention(seq_len, batch_size)?;
    println!("Relative attention encodings shape: {:?}", rel_attention.shape().dims());
    println!();
    
    // 4. Rotary Positional Encoding (RoPE)
    println!("4. Rotary Positional Encoding (RoPE)");
    println!("------------------------------------");
    
    let rope = RotaryPositionalEncoding::<f32>::new(config.clone(), 10000.0)?;
    println!("Type: {}", rope.encoding_type());
    println!("Max sequence length: {}", rope.max_seq_len());
    println!("Base frequency: {}", rope.base());
    println!("Supports incremental: {}", rope.supports_incremental());
    
    // Create query and key tensors
    let query = CpuTensorFactory::ones(&Shape::new(vec![batch_size, seq_len, hidden_size]))?;
    let key = CpuTensorFactory::ones(&Shape::new(vec![batch_size, seq_len, hidden_size]))?;
    
    let (rotated_q, rotated_k) = rope.apply_rotary_encoding(&query, &key, None)?;
    println!("Rotated query shape: {:?}", rotated_q.shape().dims());
    println!("Rotated key shape: {:?}", rotated_k.shape().dims());
    
    let rope_q_data = rotated_q.data();
    println!("Rotated query first few values: [{:.4}, {:.4}, {:.4}, {:.4}]", 
             rope_q_data[0], rope_q_data[1], rope_q_data[2], rope_q_data[3]);
    println!();
    
    // 5. Comparison of different encodings
    println!("5. Encoding Comparison");
    println!("---------------------");
    
    println!("Original embeddings sum: {:.4}", embeddings.data().iter().sum::<f32>());
    println!("Sinusoidal encoded sum: {:.4}", encoded_sin.data().iter().sum::<f32>());
    println!("Learned encoded sum: {:.4}", encoded_learned.data().iter().sum::<f32>());
    println!("Relative encoded sum: {:.4}", encoded_relative.data().iter().sum::<f32>());
    println!();
    
    // 6. Incremental generation example
    println!("6. Incremental Generation Example");
    println!("---------------------------------");
    
    let short_embeddings = CpuTensorFactory::ones(&Shape::new(vec![1, 3, hidden_size]))?;
    
    // Encode with different offsets
    let offset_0 = sinusoidal.encode(&short_embeddings, Some(0))?;
    let offset_5 = sinusoidal.encode(&short_embeddings, Some(5))?;
    
    println!("Encoding with offset 0 - first value: {:.4}", offset_0.data()[0]);
    println!("Encoding with offset 5 - first value: {:.4}", offset_5.data()[0]);
    println!();
    
    // 7. Get specific position encodings
    println!("7. Specific Position Encodings");
    println!("------------------------------");
    
    let positions = vec![0, 1, 5, 10];
    let specific_encodings = sinusoidal.get_encodings(&positions, hidden_size)?;
    println!("Encodings for positions {:?}: shape {:?}", 
             positions, specific_encodings.shape().dims());
    
    let spec_data = specific_encodings.data();
    println!("Position 0 first value: {:.4}", spec_data[0]);
    println!("Position 1 first value: {:.4}", spec_data[hidden_size]);
    println!("Position 5 first value: {:.4}", spec_data[2 * hidden_size]);
    println!("Position 10 first value: {:.4}", spec_data[3 * hidden_size]);
    
    println!("\n=== Demo completed successfully! ===");
    
    Ok(())
}
