//! BatchNorm and GroupNorm Example
//!
//! This example demonstrates the usage of BatchNorm and GroupNorm layers
//! in the Qilin inference engine. These normalization techniques are commonly
//! used in computer vision models and provide different trade-offs.
//!
//! Run with: cargo run --example batch_group_norm_example

use qilin_inference::layers::*;
use qilin_inference::tensor::{Tensor, Shape, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
use qilin_inference::error::TensorError;

fn main() -> Result<(), TensorError> {
    println!("🚀 BatchNorm and GroupNorm Example");
    println!("===================================\n");

    // Configuration for image-like data (NCHW format)
    let batch_size = 8;
    let channels = 32;
    let height = 16;
    let width = 16;

    println!("Configuration:");
    println!("  • Batch size: {}", batch_size);
    println!("  • Channels: {}", channels);
    println!("  • Height: {}", height);
    println!("  • Width: {}", width);
    println!("  • Input format: NCHW (batch, channels, height, width)");
    println!();

    // Create input tensor
    let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, channels, height, width]), 0.0, 1.0)?;
    println!("Input tensor shape: {:?}", input.shape().dims());
    println!();

    // 1. BatchNorm Example
    println!("1. BatchNorm Example");
    println!("-------------------");
    
    let batch_config = BatchNormConfig::new(channels)
        .with_eps(1e-5)
        .with_momentum(0.1)
        .with_affine(true)
        .with_track_running_stats(true);
    
    let mut batch_norm = BatchNorm::<f32>::new(batch_config)?;
    batch_norm.init_parameters(ParameterInit::Ones)?;
    
    println!("  BatchNorm configuration:");
    println!("    • Channels: {}", batch_norm.num_features());
    println!("    • Epsilon: {}", batch_norm.eps());
    println!("    • Momentum: {}", batch_norm.momentum());
    println!("    • Affine: {}", batch_norm.affine());
    println!("    • Track running stats: {}", batch_norm.track_running_stats());
    println!("    • Parameters: {}", batch_norm.parameter_count());
    
    // Training mode forward pass
    batch_norm.set_training(true);
    let batch_output_train = batch_norm.forward(input.clone())?;
    println!("    • Training output shape: {:?}", batch_output_train.shape().dims());
    
    // Inference mode forward pass
    batch_norm.set_training(false);
    let batch_output_infer = batch_norm.forward(input.clone())?;
    println!("    • Inference output shape: {:?}", batch_output_infer.shape().dims());
    println!("    ✓ BatchNorm forward pass successful");
    println!();

    // 2. GroupNorm Example
    println!("2. GroupNorm Example");
    println!("-------------------");
    
    let num_groups = 8; // 32 channels / 8 groups = 4 channels per group
    let group_config = GroupNormConfig::new(num_groups, channels)?
        .with_eps(1e-5)
        .with_affine(true);
    
    let mut group_norm = GroupNorm::<f32>::new(group_config)?;
    group_norm.init_parameters(ParameterInit::Ones)?;
    
    println!("  GroupNorm configuration:");
    println!("    • Groups: {}", group_norm.num_groups());
    println!("    • Channels: {}", group_norm.num_channels());
    println!("    • Channels per group: {}", group_norm.channels_per_group());
    println!("    • Epsilon: {}", group_norm.eps());
    println!("    • Affine: {}", group_norm.affine());
    println!("    • Parameters: {}", group_norm.parameter_count());
    
    let group_output = group_norm.forward(input.clone())?;
    println!("    • Output shape: {:?}", group_output.shape().dims());
    println!("    ✓ GroupNorm forward pass successful");
    println!();

    // 3. Comparison of Different Normalization Methods
    println!("3. Normalization Methods Comparison");
    println!("----------------------------------");
    
    // Create smaller test input for detailed analysis
    let test_input = CpuTensorFactory::randn(&Shape::new(vec![2, 4, 2, 2]), 0.0, 1.0)?;
    println!("  Test input shape: {:?}", test_input.shape().dims());
    
    // For comparison, we'll focus on BatchNorm and GroupNorm
    // since they are specifically designed for multi-dimensional inputs
    
    // BatchNorm
    let test_batch_config = BatchNormConfig::new(4);
    let mut test_batch_norm = BatchNorm::<f32>::new(test_batch_config)?;
    test_batch_norm.init_parameters(ParameterInit::Ones)?;
    let test_batch_output = test_batch_norm.forward(test_input.clone())?;
    
    // GroupNorm
    let test_group_config = GroupNormConfig::new(2, 4)?; // 2 groups, 4 channels
    let mut test_group_norm = GroupNorm::<f32>::new(test_group_config)?;
    test_group_norm.init_parameters(ParameterInit::Ones)?;
    let test_group_output = test_group_norm.forward(test_input.clone())?;
    
    println!("  Normalization results:");
    println!("    • BatchNorm parameters: {}", test_batch_norm.parameter_count());
    println!("    • GroupNorm parameters: {}", test_group_norm.parameter_count());
    println!("    ✓ Both normalization methods work correctly");
    println!();

    // 4. Performance Comparison
    println!("4. Performance Comparison");
    println!("------------------------");
    
    let perf_input = CpuTensorFactory::randn(&Shape::new(vec![16, 64, 32, 32]), 0.0, 1.0)?;
    let iterations = 50;
    
    // BatchNorm performance
    let perf_batch_config = BatchNormConfig::new(64);
    let mut perf_batch_norm = BatchNorm::<f32>::new(perf_batch_config)?;
    perf_batch_norm.init_parameters(ParameterInit::Ones)?;
    
    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = perf_batch_norm.forward(perf_input.clone())?;
    }
    let batch_duration = start.elapsed();
    
    // GroupNorm performance
    let perf_group_config = GroupNormConfig::new(8, 64)?; // 8 groups
    let mut perf_group_norm = GroupNorm::<f32>::new(perf_group_config)?;
    perf_group_norm.init_parameters(ParameterInit::Ones)?;
    
    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = perf_group_norm.forward(perf_input.clone())?;
    }
    let group_duration = start.elapsed();
    
    println!("  Input shape: [16, 64, 32, 32]");
    println!("  Iterations: {}", iterations);
    println!("  BatchNorm time: {:.2}ms ({:.2}ms/iter)", 
             batch_duration.as_secs_f64() * 1000.0,
             batch_duration.as_secs_f64() * 1000.0 / iterations as f64);
    println!("  GroupNorm time: {:.2}ms ({:.2}ms/iter)", 
             group_duration.as_secs_f64() * 1000.0,
             group_duration.as_secs_f64() * 1000.0 / iterations as f64);
    
    let speedup = group_duration.as_secs_f64() / batch_duration.as_secs_f64();
    if speedup > 1.0 {
        println!("  BatchNorm is {:.2}x faster than GroupNorm", speedup);
    } else {
        println!("  GroupNorm is {:.2}x faster than BatchNorm", 1.0 / speedup);
    }
    println!();

    // 5. Different Group Configurations
    println!("5. Different Group Configurations");
    println!("---------------------------------");
    
    let test_channels = 16;
    let group_test_input = CpuTensorFactory::randn(&Shape::new(vec![4, test_channels, 8, 8]), 0.0, 1.0)?;
    
    let group_configs = vec![
        (1, "Instance Norm equivalent"),
        (2, "8 channels per group"),
        (4, "4 channels per group"),
        (8, "2 channels per group"),
        (16, "Layer Norm equivalent"),
    ];
    
    for (num_groups, description) in group_configs {
        let config = GroupNormConfig::new(num_groups, test_channels)?;
        let mut layer = GroupNorm::<f32>::new(config)?;
        layer.init_parameters(ParameterInit::Ones)?;
        
        let output = layer.forward(group_test_input.clone())?;
        
        println!("  {} groups ({}): ✓", num_groups, description);
        println!("    • Channels per group: {}", layer.channels_per_group());
        println!("    • Output shape: {:?}", output.shape().dims());
    }
    println!();

    println!("📊 Summary:");
    println!("  • BatchNorm: Normalizes across batch dimension, good for large batches");
    println!("  • GroupNorm: Normalizes within channel groups, batch-size independent");
    println!("  • BatchNorm typically faster but requires sufficient batch size");
    println!("  • GroupNorm more stable for small batches and inference");
    println!("  • Both support affine transformations and configurable epsilon");
    println!("  • GroupNorm with 1 group ≈ Instance Norm");
    println!("  • GroupNorm with channels groups ≈ Layer Norm");
    println!();

    println!("✅ BatchNorm and GroupNorm example completed successfully!");
    println!("🎯 Both normalization methods are ready for use in computer vision models!");
    
    Ok(())
}
