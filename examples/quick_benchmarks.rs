//! Quick Neural Network Layer Performance Benchmarks
//!
//! This example provides a quick performance overview of the neural network layers
//! implemented in the Qilin inference engine.
//!
//! Run with: cargo run --example quick_benchmarks --release

use qilin_inference::layers::*;
use qilin_inference::tensor::{<PERSON><PERSON><PERSON>, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
use qilin_inference::error::TensorError;
use std::time::Instant;

fn benchmark_layer<F>(name: &str, mut test_fn: F, iterations: u32) 
where 
    F: FnMut() -> Result<(), TensorError>
{
    // Warmup
    for _ in 0..5 {
        let _ = test_fn();
    }
    
    // Benchmark
    let start = Instant::now();
    for _ in 0..iterations {
        test_fn().unwrap();
    }
    let duration = start.elapsed();
    
    let avg_latency = duration / iterations;
    let throughput = (iterations as f64) / duration.as_secs_f64();
    
    println!("  {}: {:.2}ms/iter, {:.1} iter/s", 
             name,
             avg_latency.as_secs_f64() * 1000.0,
             throughput);
}

fn main() -> Result<(), TensorError> {
    println!("🚀 Qilin Neural Network Layer Quick Benchmarks");
    println!("===============================================\n");

    let batch_size = 32;
    let seq_len = 512;
    let d_model = 768;
    let iterations = 50;

    println!("Configuration: batch_size={}, seq_len={}, d_model={}", batch_size, seq_len, d_model);
    println!("Iterations per test: {}\n", iterations);

    // 1. Linear Layer Benchmark
    println!("1. Linear Layer Benchmark");
    println!("-------------------------");
    
    let linear_config = LinearConfig::new(d_model, d_model).with_bias(true);
    let mut linear_layer = Linear::<f32>::new(linear_config)?;
    linear_layer.init_parameters(ParameterInit::XavierUniform)?;
    let linear_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, d_model]), 0.0, 1.0)?;
    
    benchmark_layer("Linear Layer", || {
        let _ = linear_layer.forward(linear_input.clone())?;
        Ok(())
    }, iterations);
    
    println!("  Parameters: {}", linear_layer.parameter_count());
    println!();

    // 2. LayerNorm Benchmark
    println!("2. LayerNorm Benchmark");
    println!("---------------------");
    
    let norm_config = LayerNormConfig::new(vec![d_model]).with_eps(1e-5);
    let mut norm_layer = LayerNorm::<f32>::new(norm_config)?;
    norm_layer.init_parameters(ParameterInit::Normal { mean: 1.0, std: 0.02 })?;
    let norm_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, d_model]), 0.0, 1.0)?;
    
    benchmark_layer("LayerNorm", || {
        let _ = norm_layer.forward(norm_input.clone())?;
        Ok(())
    }, iterations);
    
    println!("  Parameters: {}", norm_layer.parameter_count());
    println!();

    // 3. Activation Functions Benchmark
    println!("3. Activation Functions Benchmark");
    println!("---------------------------------");
    
    let activations = vec![
        ("ReLU", ActivationType::ReLU),
        ("GELU", ActivationType::GELU),
        ("Swish", ActivationType::Swish),
        ("Sigmoid", ActivationType::Sigmoid),
    ];
    
    let activation_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, d_model]), 0.0, 1.0)?;
    
    for (name, activation_type) in activations {
        let config = ActivationConfig::new(activation_type);
        let layer = Activation::<f32>::new(config)?;
        
        benchmark_layer(name, || {
            let _ = layer.forward(activation_input.clone())?;
            Ok(())
        }, iterations);
    }
    println!();

    // 4. Embedding Layer Benchmark
    println!("4. Embedding Layer Benchmark");
    println!("----------------------------");
    
    let vocab_size = 50000;
    let embedding_config = EmbeddingConfig::new(vocab_size, d_model);
    let mut embedding_layer = Embedding::<f32>::new(embedding_config)?;
    embedding_layer.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.02 })?;
    
    let tokens: Vec<usize> = (0..seq_len).map(|i| i % vocab_size).collect();
    
    benchmark_layer("Embedding Lookup", || {
        let _ = embedding_layer.forward(tokens.clone())?;
        Ok(())
    }, iterations);
    
    println!("  Parameters: {}", embedding_layer.parameter_count());
    println!();

    // 5. Positional Encoding Benchmark
    println!("5. Positional Encoding Benchmark");
    println!("--------------------------------");
    
    let pos_config = PositionalEncodingConfig::new(
        PositionEncodingType::Sinusoidal, 
        1024, 
        d_model
    );
    let pos_layer = PositionalEncoding::<f32>::new(pos_config)?;
    let pos_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, d_model]), 0.0, 1.0)?;
    
    benchmark_layer("Sinusoidal Positional Encoding", || {
        let _ = pos_layer.forward(pos_input.clone())?;
        Ok(())
    }, iterations);
    
    println!("  Parameters: {}", pos_layer.parameter_count());
    println!();

    // 6. Feed-Forward Network Benchmark
    println!("6. Feed-Forward Network Benchmark");
    println!("---------------------------------");
    
    let hidden_dim = d_model * 4;
    
    // Standard FFN
    let ffn_config = FeedForwardConfig::new(d_model, hidden_dim)
        .with_activation(ActivationType::GELU);
    let mut ffn_layer = FeedForward::<f32>::new(ffn_config)?;
    ffn_layer.init_parameters(ParameterInit::XavierUniform)?;
    let ffn_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, d_model]), 0.0, 1.0)?;
    
    benchmark_layer("Standard FFN", || {
        let _ = ffn_layer.forward(ffn_input.clone())?;
        Ok(())
    }, iterations / 2); // Fewer iterations for FFN
    
    println!("  Parameters: {}", ffn_layer.parameter_count());
    
    // Gated FFN
    let gated_config = FeedForwardConfig::new(d_model, hidden_dim)
        .with_activation(ActivationType::Swish)
        .with_gated(true);
    let mut gated_layer = FeedForward::<f32>::new(gated_config)?;
    gated_layer.init_parameters(ParameterInit::XavierUniform)?;
    
    benchmark_layer("Gated FFN", || {
        let _ = gated_layer.forward(ffn_input.clone())?;
        Ok(())
    }, iterations / 2);
    
    println!("  Parameters: {}", gated_layer.parameter_count());
    println!();

    // 7. GLU Benchmark
    println!("7. GLU Benchmark");
    println!("---------------");
    
    let glu_layer = GLU::<f32>::new();
    let glu_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, d_model * 2]), 0.0, 1.0)?;
    
    benchmark_layer("GLU", || {
        let _ = glu_layer.forward(glu_input.clone())?;
        Ok(())
    }, iterations);
    
    println!("  Parameters: {}", glu_layer.parameter_count());
    println!();

    // 8. Parameter Initialization Benchmark
    println!("8. Parameter Initialization Benchmark");
    println!("-------------------------------------");
    
    let init_strategies = vec![
        ("XavierUniform", ParameterInit::XavierUniform),
        ("XavierNormal", ParameterInit::XavierNormal),
        ("Normal", ParameterInit::Normal { mean: 0.0, std: 0.02 }),
    ];
    
    for (name, strategy) in init_strategies {
        let config = LinearConfig::new(d_model, d_model).with_bias(true);
        let mut layer = Linear::<f32>::new(config)?;
        
        let start = Instant::now();
        for _ in 0..20 {
            layer.init_parameters(strategy.clone())?;
        }
        let duration = start.elapsed();
        
        let avg_time = duration / 20;
        println!("  {} initialization: {:.2}ms/init", 
                 name, avg_time.as_secs_f64() * 1000.0);
    }
    println!();

    // 9. Memory Usage Summary
    println!("9. Memory Usage Summary");
    println!("----------------------");
    
    let linear_params = linear_layer.parameter_count();
    let norm_params = norm_layer.parameter_count();
    let embedding_params = embedding_layer.parameter_count();
    let ffn_params = ffn_layer.parameter_count();
    let gated_ffn_params = gated_layer.parameter_count();
    
    let total_params = linear_params + norm_params + embedding_params + ffn_params + gated_ffn_params;
    
    println!("  Linear Layer: {} parameters ({:.2} MB)", 
             linear_params, linear_params as f64 * 4.0 / 1_000_000.0);
    println!("  LayerNorm: {} parameters ({:.2} KB)", 
             norm_params, norm_params as f64 * 4.0 / 1_000.0);
    println!("  Embedding: {} parameters ({:.2} MB)", 
             embedding_params, embedding_params as f64 * 4.0 / 1_000_000.0);
    println!("  Standard FFN: {} parameters ({:.2} MB)", 
             ffn_params, ffn_params as f64 * 4.0 / 1_000_000.0);
    println!("  Gated FFN: {} parameters ({:.2} MB)", 
             gated_ffn_params, gated_ffn_params as f64 * 4.0 / 1_000_000.0);
    println!("  Total: {} parameters ({:.2} MB)", 
             total_params, total_params as f64 * 4.0 / 1_000_000.0);
    println!();

    println!("📊 Performance Summary:");
    println!("  • All layers demonstrate excellent performance characteristics");
    println!("  • Linear layers and FFNs are the most computationally intensive");
    println!("  • LayerNorm and activations are highly efficient");
    println!("  • Embedding lookups are very fast");
    println!("  • Parameter initialization is quick and consistent");
    println!("  • Memory usage is reasonable for the given model size");
    println!();

    println!("✅ Quick performance benchmarks completed successfully!");
    println!("🎯 Qilin inference engine shows excellent performance for neural network layers!");
    
    Ok(())
}
