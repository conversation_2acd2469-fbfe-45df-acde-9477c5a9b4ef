//! Neural Network Layers Example
//!
//! This example demonstrates the usage of various neural network layers
//! implemented in the Qilin inference engine, including:
//! - Linear layers with different configurations
//! - Layer normalization
//! - Activation functions (ReLU, GELU, Swish, etc.)
//! - Embedding layers with positional encoding
//! - Feed-forward networks (standard and gated)
//!
//! The example shows how to create, configure, and use these layers
//! to build components commonly found in transformer architectures.

use qilin_inference::layers::*;
use qilin_inference::tensor::{Tensor, Shape, TensorOps, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
use qilin_inference::error::TensorError;

fn main() -> Result<(), TensorError> {
    println!("🚀 Qilin Neural Network Layers Example");
    println!("=====================================\n");

    // 1. Linear Layer Example
    println!("1. Linear Layer Example");
    println!("-----------------------");
    
    let linear_config = LinearConfig::new(512, 256).with_bias(true);
    let mut linear_layer = Linear::<f32>::new(linear_config)?;
    linear_layer.init_parameters(ParameterInit::XavierUniform)?;
    
    let input = CpuTensorFactory::randn(&Shape::new(vec![32, 128, 512]), 0.0, 1.0)?;
    let linear_output = linear_layer.forward(input)?;
    
    println!("  Input shape: {:?}", [32, 128, 512]);
    println!("  Output shape: {:?}", linear_output.shape().dims());
    println!("  Parameters: {}", linear_layer.parameter_count());
    println!();

    // 2. Layer Normalization Example
    println!("2. Layer Normalization Example");
    println!("------------------------------");
    
    let norm_config = LayerNormConfig::new(vec![256])
        .with_eps(1e-5)
        .with_elementwise_affine(true);
    let mut norm_layer = LayerNorm::<f32>::new(norm_config)?;
    norm_layer.init_parameters(ParameterInit::Normal { mean: 1.0, std: 0.02 })?;
    
    let norm_output = norm_layer.forward(linear_output.clone())?;
    
    println!("  Normalized dimensions: {:?}", [256]);
    println!("  Output shape: {:?}", norm_output.shape().dims());
    println!("  Parameters: {}", norm_layer.parameter_count());
    println!();

    // 3. Activation Functions Example
    println!("3. Activation Functions Example");
    println!("-------------------------------");
    
    // Test different activation functions
    let activations = vec![
        ("ReLU", ActivationType::ReLU),
        ("GELU", ActivationType::GELU),
        ("Swish", ActivationType::Swish),
        ("Sigmoid", ActivationType::Sigmoid),
    ];
    
    for (name, activation_type) in activations {
        let activation_config = ActivationConfig::new(activation_type);
        let activation_layer = Activation::<f32>::new(activation_config)?;
        
        let test_input = CpuTensor::from_data(vec![-2.0, -1.0, 0.0, 1.0, 2.0], Shape::new(vec![5]))?;
        let activation_output = activation_layer.forward(test_input)?;
        
        println!("  {}: {:?}", name, activation_output.to_vec());
    }
    println!();

    // 4. Embedding Layer Example
    println!("4. Embedding Layer Example");
    println!("--------------------------");
    
    let embedding_config = EmbeddingConfig::new(10000, 512)
        .with_padding_idx(Some(0));
    let mut embedding_layer = Embedding::<f32>::new(embedding_config)?;
    embedding_layer.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.02 })?;
    
    // Example token sequence
    let tokens = vec![1, 5, 10, 25, 100];
    let embeddings = embedding_layer.forward(tokens)?;
    
    println!("  Vocabulary size: {}", embedding_layer.vocab_size());
    println!("  Embedding dimension: {}", embedding_layer.embedding_dim());
    println!("  Token embeddings shape: {:?}", embeddings.shape().dims());
    println!("  Parameters: {}", embedding_layer.parameter_count());
    println!();

    // 5. Positional Encoding Example
    println!("5. Positional Encoding Example");
    println!("------------------------------");
    
    // Sinusoidal positional encoding
    let pos_config = PositionalEncodingConfig::new(
        PositionEncodingType::Sinusoidal, 
        512, 
        512
    );
    let pos_layer = PositionalEncoding::<f32>::new(pos_config)?;
    
    // Add positional encoding to embeddings
    let batch_embeddings = CpuTensorFactory::randn(&Shape::new(vec![2, 5, 512]), 0.0, 1.0)?;
    let pos_encoded = pos_layer.forward(batch_embeddings.clone())?;
    
    println!("  Encoding type: {:?}", pos_layer.encoding_type());
    println!("  Max sequence length: {}", pos_layer.max_length());
    println!("  Encoded shape: {:?}", pos_encoded.shape().dims());
    println!("  Parameters: {}", pos_layer.parameter_count());
    println!();

    // 6. Feed-Forward Network Example
    println!("6. Feed-Forward Network Example");
    println!("-------------------------------");
    
    // Standard FFN
    let ffn_config = FeedForwardConfig::new(512, 2048)
        .with_activation(ActivationType::GELU)
        .with_bias(true);
    let mut ffn_layer = FeedForward::<f32>::new(ffn_config)?;
    ffn_layer.init_parameters(ParameterInit::XavierUniform)?;
    
    let ffn_input = CpuTensorFactory::randn(&Shape::new(vec![2, 128, 512]), 0.0, 1.0)?;
    let ffn_output = ffn_layer.forward(ffn_input.clone())?;
    
    println!("  Standard FFN:");
    println!("    Input dim: {}", ffn_layer.input_dim());
    println!("    Hidden dim: {}", ffn_layer.hidden_dim());
    println!("    Output dim: {}", ffn_layer.output_dim());
    println!("    Output shape: {:?}", ffn_output.shape().dims());
    println!("    Parameters: {}", ffn_layer.parameter_count());
    println!();

    // Gated FFN
    let gated_ffn_config = FeedForwardConfig::new(512, 2048)
        .with_activation(ActivationType::Swish)
        .with_gated(true);
    let mut gated_ffn_layer = FeedForward::<f32>::new(gated_ffn_config)?;
    gated_ffn_layer.init_parameters(ParameterInit::XavierUniform)?;
    
    let gated_ffn_input = CpuTensorFactory::randn(&Shape::new(vec![2, 128, 512]), 0.0, 1.0)?;
    let gated_ffn_output = gated_ffn_layer.forward(gated_ffn_input.clone())?;
    
    println!("  Gated FFN:");
    println!("    Is gated: {}", gated_ffn_layer.is_gated());
    println!("    Output shape: {:?}", gated_ffn_output.shape().dims());
    println!("    Parameters: {}", gated_ffn_layer.parameter_count());
    println!();

    // 7. GLU (Gated Linear Unit) Example
    println!("7. GLU (Gated Linear Unit) Example");
    println!("----------------------------------");
    
    let glu_layer = GLU::<f32>::new();
    
    // GLU expects input with even last dimension
    let glu_input = CpuTensorFactory::randn(&Shape::new(vec![2, 128, 1024]), 0.0, 1.0)?;
    let glu_output = glu_layer.forward(glu_input.clone())?;
    
    println!("  Input shape: {:?}", [2, 128, 1024]);
    println!("  Output shape: {:?}", glu_output.shape().dims());
    println!("  Parameters: {}", glu_layer.parameter_count());
    println!();

    // 8. Complete Mini-Transformer Block Example
    println!("8. Mini-Transformer Block Example");
    println!("---------------------------------");
    
    // Simulate a simple transformer block component
    let d_model = 512;
    let seq_len = 128;
    let batch_size = 2;
    
    // Input embeddings
    let mut input_embedding = Embedding::<f32>::new(
        EmbeddingConfig::new(1000, d_model)
    )?;
    input_embedding.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.02 })?;
    
    // Positional encoding
    let pos_encoding = PositionalEncoding::<f32>::new(
        PositionalEncodingConfig::new(PositionEncodingType::Sinusoidal, 512, d_model)
    )?;
    
    // Layer normalization
    let mut layer_norm1 = LayerNorm::<f32>::new(
        LayerNormConfig::new(vec![d_model]).with_eps(1e-5)
    )?;
    layer_norm1.init_parameters(ParameterInit::Normal { mean: 1.0, std: 0.02 })?;
    
    // Feed-forward network
    let mut ffn = FeedForward::<f32>::new(
        FeedForwardConfig::new(d_model, d_model * 4)
            .with_activation(ActivationType::GELU)
    )?;
    ffn.init_parameters(ParameterInit::XavierUniform)?;
    
    // Another layer norm
    let mut layer_norm2 = LayerNorm::<f32>::new(
        LayerNormConfig::new(vec![d_model]).with_eps(1e-5)
    )?;
    layer_norm2.init_parameters(ParameterInit::Normal { mean: 1.0, std: 0.02 })?;
    
    // Forward pass through mini-transformer block
    let tokens = vec![1, 5, 10, 25, 100]; // Example tokens
    let _token_embeddings = input_embedding.forward(tokens)?;
    
    // Reshape for batch processing
    let batch_input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, d_model]), 0.0, 1.0)?;

    // Add positional encoding
    let pos_encoded = pos_encoding.forward(batch_input.clone())?;
    
    // First layer norm
    let normed1 = layer_norm1.forward(pos_encoded.clone())?;
    
    // Feed-forward network
    let ffn_out = ffn.forward(normed1)?;
    
    // Residual connection (simplified)
    let residual1 = pos_encoded.add(&ffn_out)?;
    
    // Second layer norm
    let final_output = layer_norm2.forward(residual1)?;
    
    println!("  Mini-Transformer Block:");
    println!("    Model dimension: {}", d_model);
    println!("    Sequence length: {}", seq_len);
    println!("    Batch size: {}", batch_size);
    println!("    Final output shape: {:?}", final_output.shape().dims());
    
    let total_params = input_embedding.parameter_count() + 
                      pos_encoding.parameter_count() +
                      layer_norm1.parameter_count() +
                      ffn.parameter_count() +
                      layer_norm2.parameter_count();
    println!("    Total parameters: {}", total_params);
    println!();

    // 9. Validation and Summary
    println!("9. Validation and Summary");
    println!("-------------------------");

    // Validate shapes and parameter counts
    println!("  ✓ Linear layer: {} → {} (params: {})",
             512, 256, linear_layer.parameter_count());
    println!("  ✓ LayerNorm: normalized dims {:?} (params: {})",
             [256], norm_layer.parameter_count());
    println!("  ✓ Embedding: vocab {} → dim {} (params: {})",
             embedding_layer.vocab_size(), embedding_layer.embedding_dim(),
             embedding_layer.parameter_count());
    println!("  ✓ Standard FFN: {} → {} → {} (params: {})",
             ffn_layer.input_dim(), ffn_layer.hidden_dim(), ffn_layer.output_dim(),
             ffn_layer.parameter_count());
    println!("  ✓ Gated FFN: {} → {} → {} (params: {})",
             gated_ffn_layer.input_dim(), gated_ffn_layer.hidden_dim(),
             gated_ffn_layer.output_dim(), gated_ffn_layer.parameter_count());

    // Verify output shapes are correct
    assert_eq!(linear_output.shape().dims(), &[32, 128, 256]);
    assert_eq!(norm_output.shape().dims(), &[32, 128, 256]);
    assert_eq!(embeddings.shape().dims(), &[5, 512]);
    assert_eq!(pos_encoded.shape().dims(), &[2, 128, 512]);
    assert_eq!(ffn_output.shape().dims(), &[2, 128, 512]);
    assert_eq!(gated_ffn_output.shape().dims(), &[2, 128, 512]);
    assert_eq!(glu_output.shape().dims(), &[2, 128, 512]);
    assert_eq!(final_output.shape().dims(), &[2, 128, 512]);

    println!("  ✓ All output shapes validated successfully!");
    println!();

    println!("📊 Performance Summary:");
    println!("  • Total layers implemented: 8 types");
    println!("  • Activation functions: 8 variants");
    println!("  • Position encodings: 3 types (Sinusoidal, Learnable, RoPE)");
    println!("  • All layers support parameter initialization");
    println!("  • All layers implement the Layer trait");
    println!("  • Memory-safe tensor operations with proper error handling");
    println!();

    println!("✅ All neural network layer examples completed successfully!");
    println!("🎯 Qilin inference engine neural network layer system is ready for use!");

    Ok(())
}
