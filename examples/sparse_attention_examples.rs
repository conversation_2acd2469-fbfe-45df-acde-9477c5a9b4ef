//! 稀疏注意力机制示例
//!
//! 本示例展示了如何使用稀疏注意力机制来减少计算复杂度和内存使用。
//! 稀疏注意力特别适用于长序列处理，可以显著提升性能。

use qilin_inference::attention::{
    AttentionConfig, SparseAttention, SparseAttentionConfig, SparsePattern
};
use qilin_inference::tensor::cpu::{CpuTensor, CpuTensorFactory};
use qilin_inference::tensor::{Shape, TensorFactory, Tensor};
use qilin_inference::error::AttentionError;

/// 示例1: 局部注意力 (Local Attention)
fn example_local_attention() -> Result<(), AttentionError> {
    println!("=== 示例1: 局部注意力 ===");
    
    let hidden_size = 512;
    let num_heads = 8;
    let seq_len = 128;
    let batch_size = 2;
    let window_size = 16; // 每个位置只关注前后16个位置
    
    // 创建基础配置
    let base_config = AttentionConfig::new(hidden_size, num_heads);
    
    // 创建稀疏注意力配置
    let sparse_config = SparseAttentionConfig::new(
        base_config,
        SparsePattern::Local { window_size }
    );
    
    // 创建稀疏注意力层
    let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;
    
    // 创建输入张量
    let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    
    // 执行稀疏注意力
    let (output, attention_weights) = sparse_attention.forward(&query, &key, &value, None)?;
    
    // 获取稀疏性统计
    let stats = sparse_attention.sparsity_stats(seq_len)?;
    
    println!("输入形状: {:?}", query.shape());
    println!("输出形状: {:?}", output.shape());
    println!("注意力权重形状: {:?}", attention_weights.shape());
    println!("稀疏性统计: {}", stats);
    println!("局部注意力窗口大小: {}", window_size);
    
    Ok(())
}

/// 示例2: 步长注意力 (Strided Attention)
fn example_strided_attention() -> Result<(), AttentionError> {
    println!("\n=== 示例2: 步长注意力 ===");
    
    let hidden_size = 256;
    let num_heads = 4;
    let seq_len = 64;
    let batch_size = 1;
    let stride = 4; // 每4个位置关注一次
    
    let base_config = AttentionConfig::new(hidden_size, num_heads);
    let sparse_config = SparseAttentionConfig::new(
        base_config,
        SparsePattern::Strided { stride }
    );
    
    let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;
    
    let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    
    let (output, _) = sparse_attention.forward(&query, &key, &value, None)?;
    let stats = sparse_attention.sparsity_stats(seq_len)?;
    
    println!("步长注意力输出形状: {:?}", output.shape());
    println!("稀疏性统计: {}", stats);
    println!("注意力步长: {}", stride);
    
    Ok(())
}

/// 示例3: 随机稀疏注意力 (Random Sparse Attention)
fn example_random_sparse_attention() -> Result<(), AttentionError> {
    println!("\n=== 示例3: 随机稀疏注意力 ===");
    
    let hidden_size = 384;
    let num_heads = 6;
    let seq_len = 96;
    let batch_size = 1;
    let sparsity = 0.7; // 70%的连接被剪枝
    
    let base_config = AttentionConfig::new(hidden_size, num_heads);
    let sparse_config = SparseAttentionConfig::new(
        base_config,
        SparsePattern::Random { sparsity }
    ).with_approximation(true); // 启用近似计算
    
    let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;
    
    let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    
    let (output, _) = sparse_attention.forward(&query, &key, &value, None)?;
    let stats = sparse_attention.sparsity_stats(seq_len)?;
    
    println!("随机稀疏注意力输出形状: {:?}", output.shape());
    println!("稀疏性统计: {}", stats);
    println!("目标稀疏度: {:.1}%", sparsity * 100.0);
    
    Ok(())
}

/// 示例4: 块稀疏注意力 (Block Sparse Attention)
fn example_block_sparse_attention() -> Result<(), AttentionError> {
    println!("\n=== 示例4: 块稀疏注意力 ===");
    
    let hidden_size = 512;
    let num_heads = 8;
    let seq_len = 128;
    let batch_size = 1;
    let block_size = 16; // 16x16的块
    
    let base_config = AttentionConfig::new(hidden_size, num_heads);
    let sparse_config = SparseAttentionConfig::new(
        base_config,
        SparsePattern::Block { block_size }
    ).with_memory_level(2); // 中等内存优化
    
    let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;
    
    let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
    
    let (output, _) = sparse_attention.forward(&query, &key, &value, None)?;
    let stats = sparse_attention.sparsity_stats(seq_len)?;
    
    println!("块稀疏注意力输出形状: {:?}", output.shape());
    println!("稀疏性统计: {}", stats);
    println!("块大小: {}x{}", block_size, block_size);
    
    Ok(())
}

/// 示例5: 性能对比
fn example_performance_comparison() -> Result<(), AttentionError> {
    println!("\n=== 示例5: 性能对比 ===");
    
    let hidden_size = 512;
    let num_heads = 8;
    let seq_len = 256;
    let batch_size = 1;
    
    let base_config = AttentionConfig::new(hidden_size, num_heads);
    
    // 不同稀疏模式的配置
    let patterns = vec![
        ("局部注意力 (窗口=32)", SparsePattern::Local { window_size: 32 }),
        ("步长注意力 (步长=8)", SparsePattern::Strided { stride: 8 }),
        ("随机稀疏 (稀疏度=60%)", SparsePattern::Random { sparsity: 0.6 }),
        ("块稀疏 (块大小=32)", SparsePattern::Block { block_size: 32 }),
    ];
    
    println!("序列长度: {}, 隐藏维度: {}, 注意力头数: {}", seq_len, hidden_size, num_heads);
    println!("完整注意力复杂度: O(n²) = O({}) = {}", seq_len, seq_len * seq_len);
    println!();
    
    for (name, pattern) in patterns {
        let sparse_config = SparseAttentionConfig::new(base_config.clone(), pattern);
        let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;
        let stats = sparse_attention.sparsity_stats(seq_len)?;
        
        println!("{}: {}", name, stats);
    }
    
    Ok(())
}

/// 示例6: 缓存管理
fn example_cache_management() -> Result<(), AttentionError> {
    println!("\n=== 示例6: 缓存管理 ===");
    
    let hidden_size = 256;
    let num_heads = 4;
    let base_config = AttentionConfig::new(hidden_size, num_heads);
    
    let sparse_config = SparseAttentionConfig::new(
        base_config,
        SparsePattern::Local { window_size: 16 }
    );
    
    let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;
    
    // 测试不同序列长度的缓存
    let seq_lengths = vec![32, 64, 128, 256];
    
    for &seq_len in &seq_lengths {
        let _ = sparse_attention.sparsity_stats(seq_len)?;
    }
    
    println!("已为 {} 个不同序列长度生成并缓存稀疏模式", seq_lengths.len());
    
    // 清理缓存
    sparse_attention.clear_cache();
    println!("缓存已清理");
    
    Ok(())
}

/// 主函数：运行所有示例
fn main() -> Result<(), AttentionError> {
    println!("🚀 稀疏注意力机制示例");
    println!("========================");
    
    example_local_attention()?;
    example_strided_attention()?;
    example_random_sparse_attention()?;
    example_block_sparse_attention()?;
    example_performance_comparison()?;
    example_cache_management()?;
    
    println!("\n✅ 所有稀疏注意力示例运行完成！");
    println!();
    println!("💡 稀疏注意力优势:");
    println!("- 显著减少计算复杂度");
    println!("- 降低内存使用");
    println!("- 支持更长的序列");
    println!("- 保持良好的性能");
    println!();
    println!("🔧 使用建议:");
    println!("- 局部注意力适用于序列建模任务");
    println!("- 步长注意力适用于长距离依赖较少的任务");
    println!("- 随机稀疏适用于需要全局信息但可以容忍近似的任务");
    println!("- 块稀疏适用于结构化数据处理");
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_all_sparse_patterns() -> Result<(), AttentionError> {
        let hidden_size = 128;
        let num_heads = 4;
        let seq_len = 32;
        let batch_size = 1;
        
        let base_config = AttentionConfig::new(hidden_size, num_heads);
        let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        
        let patterns = vec![
            SparsePattern::Local { window_size: 8 },
            SparsePattern::Strided { stride: 4 },
            SparsePattern::Random { sparsity: 0.5 },
            SparsePattern::Block { block_size: 8 },
        ];
        
        for pattern in patterns {
            let sparse_config = SparseAttentionConfig::new(base_config.clone(), pattern);
            let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;
            
            let (output, _) = sparse_attention.forward(&query, &key, &value, None)?;
            assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
            
            let stats = sparse_attention.sparsity_stats(seq_len)?;
            assert!(stats.sparsity >= 0.0 && stats.sparsity <= 1.0);
        }
        
        Ok(())
    }
}
