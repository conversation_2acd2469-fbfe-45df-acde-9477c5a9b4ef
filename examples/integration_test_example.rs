//! Integration Test Example
//!
//! This example demonstrates the integration of various components
//! in the Qilin inference engine, including layers, attention, and memory pools.

use qilin_inference::layers::*;
use qilin_inference::tensor::{
    <PERSON><PERSON>, <PERSON><PERSON><PERSON>, TensorOps, TensorFactory,
    cpu::{CpuTensor, CpuTensorFactory, PooledCpuTensorFactory},
    memory_pool::{TensorMemoryPool, MemoryPoolConfig, GlobalMemoryPool},
};

#[cfg(feature = "simd")]
use qilin_inference::tensor::simd_nn::SimdNeuralOptimized;

use qilin_inference::tensor::parallel::{ParallelOps, ParallelConfig};
use std::time::Instant;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Qilin Integration Test Example ===\n");
    
    // Test 1: Basic layer integration
    test_basic_layer_integration()?;
    
    // Test 2: Memory pool integration
    test_memory_pool_integration()?;
    
    // Test 3: SIMD integration (if available)
    #[cfg(feature = "simd")]
    test_simd_integration()?;
    
    // Test 4: Parallel operations
    test_parallel_integration()?;
    
    // Test 5: End-to-end mini transformer
    test_mini_transformer()?;
    
    println!("\n=== All Integration Tests Passed! ===");
    Ok(())
}

fn test_basic_layer_integration() -> Result<(), Box<dyn std::error::Error>> {
    println!("1. Testing Basic Layer Integration");
    
    let batch_size = 2;
    let input_dim = 64;
    let hidden_dim = 128;
    let output_dim = 32;
    
    // Create input
    let input_shape = Shape::new(vec![batch_size, input_dim]);
    let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1)?;
    
    // Linear layer
    let linear_config = LinearConfig::new(input_dim, hidden_dim);
    let mut linear: Linear<f32> = Linear::new(linear_config)?;
    linear.init_parameters(ParameterInit::XavierUniform)?;
    
    let hidden = linear.forward(input)?;
    println!("  ✓ Linear: [2, 64] -> [2, 128]");
    
    // Layer normalization
    let norm_config = LayerNormConfig::new(vec![hidden_dim]);
    let mut layer_norm: LayerNorm<f32> = LayerNorm::new(norm_config)?;
    layer_norm.init_parameters(ParameterInit::Ones)?;
    
    let normalized = layer_norm.forward(hidden)?;
    println!("  ✓ LayerNorm: [2, 128] -> [2, 128]");
    
    // Activation
    let activation_config = ActivationConfig::new(ActivationType::GELU);
    let activation: Activation<f32> = Activation::new(activation_config)?;
    let activated = activation.forward(normalized)?;
    println!("  ✓ GELU: [2, 128] -> [2, 128]");
    
    // Final linear layer
    let output_linear_config = LinearConfig::new(hidden_dim, output_dim);
    let mut output_linear: Linear<f32> = Linear::new(output_linear_config)?;
    output_linear.init_parameters(ParameterInit::XavierUniform)?;
    
    let output = output_linear.forward(activated)?;
    assert_eq!(output.shape().dims(), &[batch_size, output_dim]);
    println!("  ✓ Output Linear: [2, 128] -> [2, 32]");
    
    // Verify output is reasonable
    let output_data = output.data();
    assert!(output_data.iter().all(|&x| x.is_finite()), "Output should be finite");
    
    let mean = output_data.iter().sum::<f32>() / output_data.len() as f32;
    println!("  ✓ Output mean: {:.4}", mean);
    
    Ok(())
}

fn test_memory_pool_integration() -> Result<(), Box<dyn std::error::Error>> {
    println!("2. Testing Memory Pool Integration");
    
    let config = MemoryPoolConfig {
        enable_stats: true,
        enable_size_bucketing: true,
        preallocation_sizes: vec![128, 256, 512],
        ..Default::default()
    };

    let _pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Test pooled tensor creation
    let shape = Shape::new(vec![4, 32]);
    let tensor1 = PooledCpuTensorFactory::<f32>::zeros(&shape)?;
    let tensor2 = PooledCpuTensorFactory::<f32>::ones(&shape)?;
    
    println!("  ✓ Created pooled tensors: [4, 32]");
    
    // Test operations with pooled tensors
    let result = tensor1.add(&tensor2)?;
    assert_eq!(result.shape(), &shape);
    println!("  ✓ Pooled tensor operations work");
    
    // Test with layers
    let linear_config = LinearConfig::new(32, 16);
    let mut linear: Linear<f32> = Linear::new(linear_config)?;
    linear.init_parameters(ParameterInit::XavierUniform)?;
    
    let output = linear.forward(tensor1)?;
    assert_eq!(output.shape().dims(), &[4, 16]);
    println!("  ✓ Layers work with pooled tensors");
    
    // Check pool statistics
    let f32_pool = GlobalMemoryPool::f32();
    let stats = f32_pool.stats();
    println!("  ✓ Pool stats: {} allocations, {:.1}% hit rate", 
             stats.total_allocations, stats.hit_rate());
    
    Ok(())
}

#[cfg(feature = "simd")]
fn test_simd_integration() -> Result<(), Box<dyn std::error::Error>> {
    println!("3. Testing SIMD Integration");
    
    let size = 1000;
    let input_data: Vec<f32> = (0..size).map(|i| (i as f32) * 0.001 - 0.5).collect();
    
    // Test SIMD ReLU
    let mut simd_output = vec![0.0; size];
    input_data.simd_relu(&mut simd_output);
    
    // Compare with regular activation layer
    let shape = Shape::new(vec![1, size]);
    let input_tensor = CpuTensor::from_data(input_data.clone(), shape.clone())?;
    let activation_config = ActivationConfig::new(ActivationType::ReLU);
    let activation: Activation<f32> = Activation::new(activation_config)?;
    let layer_output = activation.forward(input_tensor)?;
    
    // Check that results are similar
    let layer_data = layer_output.data();
    let max_diff = layer_data.iter().zip(simd_output.iter())
        .map(|(a, b)| (a - b).abs())
        .fold(0.0, f32::max);
    
    assert!(max_diff < 1e-5, "SIMD and layer results should be similar");
    println!("  ✓ SIMD ReLU matches layer ReLU (max diff: {:.2e})", max_diff);
    
    // Test SIMD GELU
    let mut gelu_output = vec![0.0; size];
    input_data.simd_gelu(&mut gelu_output);
    
    let gelu_activation_config = ActivationConfig::new(ActivationType::GELU);
    let gelu_activation: Activation<f32> = Activation::new(gelu_activation_config)?;
    let gelu_layer_output = gelu_activation.forward(CpuTensor::from_data(input_data, shape)?)?;
    
    let gelu_layer_data = gelu_layer_output.data();
    let gelu_max_diff = gelu_layer_data.iter().zip(gelu_output.iter())
        .map(|(a, b)| (a - b).abs())
        .fold(0.0, f32::max);
    
    println!("  ✓ SIMD GELU matches layer GELU (max diff: {:.2e})", gelu_max_diff);
    
    Ok(())
}

fn test_parallel_integration() -> Result<(), Box<dyn std::error::Error>> {
    println!("4. Testing Parallel Integration");
    
    let size = 10000;
    let config = ParallelConfig::new(1000, None, 1000);
    
    let a: Vec<f32> = (0..size).map(|i| i as f32 * 0.001).collect();
    let b: Vec<f32> = (0..size).map(|i| i as f32 * 0.002).collect();
    let mut result = vec![0.0; size];
    
    // Test parallel addition
    let start = Instant::now();
    ParallelOps::add_f32_parallel(&a, &b, &mut result, &config);
    let parallel_time = start.elapsed();
    
    // Verify correctness
    for i in 0..100 {
        let expected = a[i] + b[i];
        assert!((result[i] - expected).abs() < 1e-6, 
               "Parallel addition incorrect at index {}", i);
    }
    
    println!("  ✓ Parallel addition: {} elements in {:?}", size, parallel_time);
    
    // Test with tensor operations
    let tensor_a = CpuTensor::from_data(a, Shape::new(vec![size]))?;
    let tensor_b = CpuTensor::from_data(b, Shape::new(vec![size]))?;
    
    let start = Instant::now();
    let _tensor_result = tensor_a.add(&tensor_b)?;
    let tensor_time = start.elapsed();
    
    println!("  ✓ Tensor addition: {} elements in {:?}", size, tensor_time);
    
    Ok(())
}

fn test_mini_transformer() -> Result<(), Box<dyn std::error::Error>> {
    println!("5. Testing Mini Transformer");
    
    let batch_size = 1;
    let seq_len = 4;
    let vocab_size = 100;
    let hidden_dim = 64;
    
    // 1. Token embedding
    let embedding_config = EmbeddingConfig::new(vocab_size, hidden_dim);
    let mut embedding: Embedding<f32> = Embedding::new(embedding_config)?;
    embedding.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.02 })?;
    
    // Input token IDs
    let token_ids = vec![10, 25, 50, 75];

    let embedded = embedding.forward(token_ids)?;
    // Embedding output shape is [seq_len, hidden_dim], we need to add batch dimension
    let expected_shape = Shape::new(vec![seq_len, hidden_dim]);
    assert_eq!(embedded.shape(), &expected_shape);
    println!("  ✓ Token embedding: {} tokens -> [4, 64]", seq_len);
    
    // 2. Layer normalization
    let norm_config = LayerNormConfig::new(vec![hidden_dim]);
    let mut layer_norm: LayerNorm<f32> = LayerNorm::new(norm_config)?;
    layer_norm.init_parameters(ParameterInit::Ones)?;
    
    let normalized = layer_norm.forward(embedded)?;
    assert_eq!(normalized.shape(), &expected_shape);
    println!("  ✓ Layer normalization: [4, 64] -> [4, 64]");
    
    // 3. Feed-forward network
    let ff_config = FeedForwardConfig::new(hidden_dim, hidden_dim * 2)
        .with_activation(ActivationType::GELU);
    let mut feedforward: FeedForward<f32> = FeedForward::new(ff_config)?;
    feedforward.init_parameters(ParameterInit::XavierUniform)?;
    
    let ff_output = feedforward.forward(normalized.clone())?;
    assert_eq!(ff_output.shape(), &expected_shape);
    println!("  ✓ Feed-forward: [4, 64] -> [4, 64]");
    
    // 4. Residual connection
    let final_output = normalized.add(&ff_output)?;
    assert_eq!(final_output.shape(), &expected_shape);
    println!("  ✓ Residual connection: [4, 64] -> [4, 64]");
    
    // 5. Final verification
    let output_data = final_output.data();
    assert!(output_data.iter().all(|&x| x.is_finite()), "Output should be finite");
    
    let mean = output_data.iter().sum::<f32>() / output_data.len() as f32;
    let variance = output_data.iter().map(|&x| (x - mean).powi(2)).sum::<f32>() / output_data.len() as f32;
    
    println!("  ✓ Output statistics: mean={:.4}, variance={:.4}", mean, variance);
    println!("  ✓ Mini transformer completed successfully!");
    
    Ok(())
}
