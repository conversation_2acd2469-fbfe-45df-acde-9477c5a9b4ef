//! GLU Variants Example
//!
//! This example demonstrates the usage of different GLU (Gated Linear Unit) variants
//! in the Qilin inference engine. GLU variants are commonly used in modern transformer
//! architectures for their superior gating mechanisms.
//!
//! Run with: cargo run --example glu_variants_example

use qilin_inference::layers::*;
use qilin_inference::tensor::{Tensor, Shape, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
use qilin_inference::error::TensorError;

fn main() -> Result<(), TensorError> {
    println!("🚀 GLU Variants Example");
    println!("=======================\n");

    // Configuration for transformer-like data
    let batch_size = 4;
    let seq_len = 8;
    let hidden_dim = 16; // Must be even for GLU variants

    println!("Configuration:");
    println!("  • Batch size: {}", batch_size);
    println!("  • Sequence length: {}", seq_len);
    println!("  • Hidden dimension: {}", hidden_dim);
    println!("  • Input format: [batch, seq_len, hidden_dim]");
    println!();

    // Create input tensor
    let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_dim]), 0.0, 1.0)?;
    println!("Input tensor shape: {:?}", input.shape().dims());
    println!();

    // 1. Standard GLU Example
    println!("1. Standard GLU (Sigmoid Gate)");
    println!("------------------------------");
    
    let glu_layer = GLU::<f32>::new();
    let glu_output = glu_layer.forward(input.clone())?;
    
    println!("  GLU configuration:");
    println!("    • Gate activation: Sigmoid");
    println!("    • Parameters: {}", glu_layer.parameter_count());
    println!("    • Input shape: {:?}", input.shape().dims());
    println!("    • Output shape: {:?}", glu_output.shape().dims());
    println!("    • Formula: GLU(x) = x[:, :d] ⊙ σ(x[:, d:])");
    println!("    ✓ Standard GLU forward pass successful");
    println!();

    // 2. SwiGLU Example
    println!("2. SwiGLU (Swish Gate)");
    println!("----------------------");
    
    let swiglu_layer = SwiGLU::<f32>::new();
    let swiglu_output = swiglu_layer.forward(input.clone())?;
    
    println!("  SwiGLU configuration:");
    println!("    • Gate activation: Swish (x * sigmoid(x))");
    println!("    • Parameters: {}", swiglu_layer.parameter_count());
    println!("    • Input shape: {:?}", input.shape().dims());
    println!("    • Output shape: {:?}", swiglu_output.shape().dims());
    println!("    • Formula: SwiGLU(x) = x[:, :d] ⊙ Swish(x[:, d:])");
    println!("    • Used in: PaLM, LLaMA, and other modern transformers");
    println!("    ✓ SwiGLU forward pass successful");
    println!();

    // 3. GeGLU Example
    println!("3. GeGLU (GELU Gate)");
    println!("-------------------");
    
    let geglu_layer = GeGLU::<f32>::new();
    let geglu_output = geglu_layer.forward(input.clone())?;
    
    println!("  GeGLU configuration:");
    println!("    • Gate activation: GELU (Gaussian Error Linear Unit)");
    println!("    • Parameters: {}", geglu_layer.parameter_count());
    println!("    • Input shape: {:?}", input.shape().dims());
    println!("    • Output shape: {:?}", geglu_output.shape().dims());
    println!("    • Formula: GeGLU(x) = x[:, :d] ⊙ GELU(x[:, d:])");
    println!("    • Used in: Various transformer architectures for smooth gating");
    println!("    ✓ GeGLU forward pass successful");
    println!();

    // 4. Performance Comparison
    println!("4. Performance Comparison");
    println!("------------------------");
    
    let perf_input = CpuTensorFactory::randn(&Shape::new(vec![32, 128, 512]), 0.0, 1.0)?;
    let iterations = 100;
    
    println!("  Performance test configuration:");
    println!("    • Input shape: [32, 128, 512]");
    println!("    • Iterations: {}", iterations);
    println!();
    
    // GLU performance
    let glu_perf = GLU::<f32>::new();
    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = glu_perf.forward(perf_input.clone())?;
    }
    let glu_duration = start.elapsed();
    
    // SwiGLU performance
    let swiglu_perf = SwiGLU::<f32>::new();
    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = swiglu_perf.forward(perf_input.clone())?;
    }
    let swiglu_duration = start.elapsed();
    
    // GeGLU performance
    let geglu_perf = GeGLU::<f32>::new();
    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = geglu_perf.forward(perf_input.clone())?;
    }
    let geglu_duration = start.elapsed();
    
    println!("  Performance results:");
    println!("    • GLU time: {:.2}ms ({:.3}ms/iter)", 
             glu_duration.as_secs_f64() * 1000.0,
             glu_duration.as_secs_f64() * 1000.0 / iterations as f64);
    println!("    • SwiGLU time: {:.2}ms ({:.3}ms/iter)", 
             swiglu_duration.as_secs_f64() * 1000.0,
             swiglu_duration.as_secs_f64() * 1000.0 / iterations as f64);
    println!("    • GeGLU time: {:.2}ms ({:.3}ms/iter)", 
             geglu_duration.as_secs_f64() * 1000.0,
             geglu_duration.as_secs_f64() * 1000.0 / iterations as f64);
    
    let swiglu_vs_glu = swiglu_duration.as_secs_f64() / glu_duration.as_secs_f64();
    let geglu_vs_glu = geglu_duration.as_secs_f64() / glu_duration.as_secs_f64();
    
    println!("    • SwiGLU vs GLU: {:.2}x slower", swiglu_vs_glu);
    println!("    • GeGLU vs GLU: {:.2}x slower", geglu_vs_glu);
    println!();

    // 5. Output Analysis
    println!("5. Output Analysis");
    println!("-----------------");
    
    let test_input = CpuTensorFactory::randn(&Shape::new(vec![2, 4, 8]), 0.0, 1.0)?;
    
    let glu_test_output = GLU::<f32>::new().forward(test_input.clone())?;
    let swiglu_test_output = SwiGLU::<f32>::new().forward(test_input.clone())?;
    let geglu_test_output = GeGLU::<f32>::new().forward(test_input.clone())?;
    
    let glu_data = glu_test_output.to_vec();
    let swiglu_data = swiglu_test_output.to_vec();
    let geglu_data = geglu_test_output.to_vec();
    
    // Calculate statistics
    let glu_mean = glu_data.iter().sum::<f32>() / glu_data.len() as f32;
    let swiglu_mean = swiglu_data.iter().sum::<f32>() / swiglu_data.len() as f32;
    let geglu_mean = geglu_data.iter().sum::<f32>() / geglu_data.len() as f32;
    
    let glu_std = (glu_data.iter().map(|x| (x - glu_mean).powi(2)).sum::<f32>() / glu_data.len() as f32).sqrt();
    let swiglu_std = (swiglu_data.iter().map(|x| (x - swiglu_mean).powi(2)).sum::<f32>() / swiglu_data.len() as f32).sqrt();
    let geglu_std = (geglu_data.iter().map(|x| (x - geglu_mean).powi(2)).sum::<f32>() / geglu_data.len() as f32).sqrt();
    
    println!("  Output statistics (test input [2, 4, 8] -> [2, 4, 4]):");
    println!("    • GLU:    mean={:.4}, std={:.4}", glu_mean, glu_std);
    println!("    • SwiGLU: mean={:.4}, std={:.4}", swiglu_mean, swiglu_std);
    println!("    • GeGLU:  mean={:.4}, std={:.4}", geglu_mean, geglu_std);
    println!();

    // 6. Different Input Sizes
    println!("6. Different Input Sizes");
    println!("-----------------------");
    
    let test_sizes = vec![
        (1, 1, 2),   // Minimal
        (2, 4, 8),   // Small
        (4, 16, 32), // Medium
        (8, 64, 128), // Large
    ];
    
    for (batch, seq, hidden) in test_sizes {
        let test_input = CpuTensorFactory::randn(&Shape::new(vec![batch, seq, hidden]), 0.0, 1.0)?;
        
        let glu_output = GLU::<f32>::new().forward(test_input.clone())?;
        let swiglu_output = SwiGLU::<f32>::new().forward(test_input.clone())?;
        let geglu_output = GeGLU::<f32>::new().forward(test_input.clone())?;
        
        println!("  Input [{}, {}, {}] -> Output [{}, {}, {}]:", 
                 batch, seq, hidden, batch, seq, hidden/2);
        println!("    • GLU: ✓    SwiGLU: ✓    GeGLU: ✓");
    }
    println!();

    // 7. Error Handling
    println!("7. Error Handling");
    println!("----------------");
    
    // Test with odd dimension (should fail)
    let odd_input = CpuTensorFactory::randn(&Shape::new(vec![2, 4, 7]), 0.0, 1.0)?; // 7 is odd
    
    let glu_result = GLU::<f32>::new().forward(odd_input.clone());
    let swiglu_result = SwiGLU::<f32>::new().forward(odd_input.clone());
    let geglu_result = GeGLU::<f32>::new().forward(odd_input.clone());
    
    println!("  Testing with odd last dimension (7):");
    println!("    • GLU error: {}", glu_result.is_err());
    println!("    • SwiGLU error: {}", swiglu_result.is_err());
    println!("    • GeGLU error: {}", geglu_result.is_err());
    println!("    ✓ All variants correctly reject odd dimensions");
    println!();

    println!("📊 Summary:");
    println!("  • GLU: Fast, simple sigmoid gating");
    println!("  • SwiGLU: Smooth Swish gating, used in modern LLMs");
    println!("  • GeGLU: GELU gating, good for smooth activation");
    println!("  • All variants reduce last dimension by half");
    println!("  • SwiGLU and GeGLU are slightly slower but provide better gating");
    println!("  • Input dimension must be even for proper splitting");
    println!();

    println!("✅ GLU variants example completed successfully!");
    println!("🎯 SwiGLU and GeGLU are ready for use in modern transformer architectures!");
    
    Ok(())
}
