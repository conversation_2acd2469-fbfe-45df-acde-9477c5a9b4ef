//! Sequential Layer Example
//!
//! This example demonstrates the usage of the Sequential layer for composing
//! multiple layers into a single sequential unit. The Sequential layer applies
//! each layer in order, passing the output of one layer as input to the next.
//!
//! Run with: cargo run --example sequential_layer_example

use qilin_inference::layers::*;
use qilin_inference::tensor::{<PERSON><PERSON>, <PERSON><PERSON><PERSON>, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
use qilin_inference::error::TensorError;

fn main() -> Result<(), TensorError> {
    println!("🔗 Sequential Layer Example");
    println!("===========================\n");

    // Create a layer factory for convenience
    let factory = LayerFactory::<f32>::new();

    println!("1. Creating Individual Layers");
    println!("-----------------------------");

    // Create individual layers
    let linear1 = factory.create_layer(LayerConfig::Linear {
        input_size: 784,
        output_size: 256,
        bias: true,
    })?;
    println!("  ✓ Linear1 created: {} parameters", linear1.parameter_count());

    let activation1 = factory.create_layer(LayerConfig::Activation {
        activation_type: ActivationType::ReLU,
    })?;
    println!("  ✓ ReLU activation created: {} parameters", activation1.parameter_count());

    let linear2 = factory.create_layer(LayerConfig::Linear {
        input_size: 256,
        output_size: 128,
        bias: true,
    })?;
    println!("  ✓ Linear2 created: {} parameters", linear2.parameter_count());

    let layernorm = factory.create_layer(LayerConfig::LayerNorm {
        normalized_shape: vec![128],
        eps: 1e-5,
        elementwise_affine: true,
    })?;
    println!("  ✓ LayerNorm created: {} parameters", layernorm.parameter_count());

    let activation2 = factory.create_layer(LayerConfig::Activation {
        activation_type: ActivationType::GELU,
    })?;
    println!("  ✓ GELU activation created: {} parameters", activation2.parameter_count());

    let linear3 = factory.create_layer(LayerConfig::Linear {
        input_size: 128,
        output_size: 10,
        bias: true,
    })?;
    println!("  ✓ Linear3 created: {} parameters", linear3.parameter_count());

    println!();

    // 2. Creating Sequential Network
    println!("2. Creating Sequential Network");
    println!("------------------------------");

    let mut network = Sequential::new()
        .add_layer(linear1)
        .add_layer(activation1)
        .add_layer(linear2)
        .add_layer(layernorm)
        .add_layer(activation2)
        .add_layer(linear3);

    println!("  ✓ Sequential network created with {} layers", network.len());
    println!("  ✓ Total parameters: {}", network.parameter_count());
    println!("  ✓ Training mode: {}", network.training());

    println!();

    // 3. Testing Forward Pass
    println!("3. Testing Forward Pass");
    println!("-----------------------");

    // Create test input (batch_size=32, features=784, like MNIST)
    let input = CpuTensorFactory::randn(&Shape::new(vec![32, 784]), 0.0, 1.0)?;
    println!("  Input shape: {:?}", input.shape().dims());

    let output = network.forward(input)?;
    println!("  ✓ Forward pass successful");
    println!("  Output shape: {:?}", output.shape().dims());

    // Check output statistics
    let output_data = output.data();
    let mean = output_data.iter().sum::<f32>() / output_data.len() as f32;
    let variance = output_data.iter().map(|x| (x - mean).powi(2)).sum::<f32>() / output_data.len() as f32;
    println!("  Output statistics:");
    println!("    • Mean: {:.6}", mean);
    println!("    • Variance: {:.6}", variance);
    println!("    • Min: {:.6}", output_data.iter().fold(f32::INFINITY, |a, &b| a.min(b)));
    println!("    • Max: {:.6}", output_data.iter().fold(f32::NEG_INFINITY, |a, &b| a.max(b)));

    println!();

    // 4. Layer Management
    println!("4. Layer Management");
    println!("------------------");

    println!("  Layer summary:");
    let summary = network.layer_summary();
    for (index, name, params) in summary {
        println!("    [{:2}] {}: {} parameters", index, name, params);
    }

    // Test layer access
    if let Some(layer) = network.get_layer(0) {
        println!("  ✓ First layer accessible: {} parameters", layer.parameter_count());
    }

    println!();

    // 5. Training Mode Control
    println!("5. Training Mode Control");
    println!("-----------------------");

    println!("  Current training mode: {}", network.training());
    
    // Switch to evaluation mode
    network.set_training(false);
    println!("  ✓ Switched to evaluation mode: {}", network.training());

    // Test forward pass in eval mode
    let eval_input = CpuTensorFactory::randn(&Shape::new(vec![16, 784]), 0.0, 1.0)?;
    let eval_output = network.forward(eval_input)?;
    println!("  ✓ Forward pass in eval mode successful");
    println!("  Eval output shape: {:?}", eval_output.shape().dims());

    // Switch back to training mode
    network.set_training(true);
    println!("  ✓ Switched back to training mode: {}", network.training());

    println!();

    // 6. Building Different Network Architectures
    println!("6. Building Different Network Architectures");
    println!("-------------------------------------------");

    // Simple classifier
    let mut classifier = Sequential::new()
        .add_layer(factory.create_layer(LayerConfig::Linear { input_size: 784, output_size: 512, bias: true })?)
        .add_layer(factory.create_layer(LayerConfig::Activation { activation_type: ActivationType::ReLU })?)
        .add_layer(factory.create_layer(LayerConfig::Linear { input_size: 512, output_size: 256, bias: true })?)
        .add_layer(factory.create_layer(LayerConfig::Activation { activation_type: ActivationType::ReLU })?)
        .add_layer(factory.create_layer(LayerConfig::Linear { input_size: 256, output_size: 10, bias: true })?);

    println!("  ✓ Classifier created: {} layers, {} parameters", classifier.len(), classifier.parameter_count());

    // Deep network with normalization
    let mut deep_network = Sequential::new()
        .add_layer(factory.create_layer(LayerConfig::Linear { input_size: 512, output_size: 512, bias: true })?)
        .add_layer(factory.create_layer(LayerConfig::LayerNorm { normalized_shape: vec![512], eps: 1e-5, elementwise_affine: true })?)
        .add_layer(factory.create_layer(LayerConfig::Activation { activation_type: ActivationType::GELU })?)
        .add_layer(factory.create_layer(LayerConfig::Linear { input_size: 512, output_size: 512, bias: true })?)
        .add_layer(factory.create_layer(LayerConfig::LayerNorm { normalized_shape: vec![512], eps: 1e-5, elementwise_affine: true })?)
        .add_layer(factory.create_layer(LayerConfig::Activation { activation_type: ActivationType::GELU })?)
        .add_layer(factory.create_layer(LayerConfig::Linear { input_size: 512, output_size: 256, bias: true })?);

    println!("  ✓ Deep network created: {} layers, {} parameters", deep_network.len(), deep_network.parameter_count());

    // Test both networks
    let test_input_classifier = CpuTensorFactory::randn(&Shape::new(vec![8, 784]), 0.0, 1.0)?;
    let test_input_deep = CpuTensorFactory::randn(&Shape::new(vec![8, 512]), 0.0, 1.0)?;

    let classifier_output = classifier.forward(test_input_classifier)?;
    let deep_output = deep_network.forward(test_input_deep)?;

    println!("  ✓ Classifier output shape: {:?}", classifier_output.shape().dims());
    println!("  ✓ Deep network output shape: {:?}", deep_output.shape().dims());

    println!();

    // 7. Performance Benchmark
    println!("7. Performance Benchmark");
    println!("-----------------------");

    let bench_input = CpuTensorFactory::randn(&Shape::new(vec![64, 784]), 0.0, 1.0)?;
    let iterations = 100;

    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = network.forward(bench_input.clone())?;
    }
    let duration = start.elapsed();

    println!("  ✓ Performance benchmark completed");
    println!("    • Network: 784 -> 256 -> 128 -> 10 (6 layers)");
    println!("    • Input shape: [64, 784]");
    println!("    • Iterations: {}", iterations);
    println!("    • Total time: {:.2}ms", duration.as_secs_f64() * 1000.0);
    println!("    • Time per iteration: {:.3}ms", duration.as_secs_f64() * 1000.0 / iterations as f64);
    println!("    • Throughput: {:.1} samples/sec", (64 * iterations) as f64 / duration.as_secs_f64());

    println!();

    // 8. Error Handling
    println!("8. Error Handling");
    println!("----------------");

    // Test empty sequential
    let empty_sequential = Sequential::<f32>::new();
    match empty_sequential.forward(CpuTensorFactory::zeros(&Shape::new(vec![1, 10]))?) {
        Ok(_) => println!("  ❌ Empty sequential should have failed"),
        Err(_) => println!("  ✓ Empty sequential correctly returns error"),
    }

    // Test layer access bounds
    if network.get_layer(100).is_none() {
        println!("  ✓ Out-of-bounds layer access correctly returns None");
    }

    println!();

    // 9. Summary
    println!("9. Summary");
    println!("---------");
    println!("  ✅ Sequential layer successfully created and tested");
    println!("  ✅ Forward propagation works correctly through all layers");
    println!("  ✅ Parameter counting aggregates correctly across layers");
    println!("  ✅ Training mode propagates to all contained layers");
    println!("  ✅ Layer management functions work properly");
    println!("  ✅ Different network architectures can be easily built");
    println!("  ✅ Performance is reasonable for inference workloads");
    println!("  ✅ Error handling works as expected");
    println!();

    println!("🎯 Sequential layer provides a powerful way to compose neural networks!");
    println!("📦 Key features:");
    println!("   • Easy layer composition with builder pattern");
    println!("   • Automatic forward propagation chaining");
    println!("   • Aggregate parameter management");
    println!("   • Training mode synchronization");
    println!("   • Layer access and management utilities");
    println!();

    println!("✨ Next step: Implement Parallel layer for branching architectures!");

    Ok(())
}
