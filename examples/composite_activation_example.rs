use qilin_inference::{
    layers::{
        Layer,
        activation::{
            ActivationType, CompositeActivation, CompositeActivationConfig,
            CombinationStrategy, CustomActivation
        },
    },
    tensor::{cpu::{<PERSON><PERSON>Tensor, CpuTensorFactory}, <PERSON><PERSON><PERSON>, TensorFactory},
    error::TensorError,
};

fn main() -> Result<(), TensorError> {
    println!("🚀 Qilin Inference Engine - Composite Activation Functions Example");
    println!("================================================================================");

    // Create test input
    let input_data = vec![-2.0, -1.0, -0.5, 0.0, 0.5, 1.0, 2.0, 3.0];
    let input = CpuTensorFactory::from_vec(input_data.clone(), &Shape::new(vec![8]))?;
    
    println!("\n📊 Input tensor: {:?}", input_data);
    println!("================================================================================");

    // 1. Sequential Composite Activation (ReLU → Sigmoid)
    println!("\n🔗 1. Sequential Composite Activation (ReLU → Sigmoid)");
    println!("------------------------------------------------------------------------");
    
    let sequential_config = CompositeActivationConfig::new_sequential(vec![
        ActivationType::ReLU,
        ActivationType::Sigmoid,
    ]);
    let sequential_layer = CompositeActivation::<f32>::new(sequential_config);
    let sequential_output = sequential_layer.forward(input.clone())?;
    let sequential_data = sequential_output.to_vec();
    
    println!("Sequential (ReLU → Sigmoid): {:?}", sequential_data);
    println!("Parameter count: {}", sequential_layer.parameter_count());

    // 2. Sum Composite Activation (ReLU + GELU)
    println!("\n➕ 2. Sum Composite Activation (ReLU + GELU)");
    println!("------------------------------------------------------------------------");
    
    let sum_config = CompositeActivationConfig::new_sum(vec![
        ActivationType::ReLU,
        ActivationType::GELU,
    ]);
    let sum_layer = CompositeActivation::<f32>::new(sum_config);
    let sum_output = sum_layer.forward(input.clone())?;
    let sum_data = sum_output.to_vec();
    
    println!("Sum (ReLU + GELU): {:?}", sum_data);

    // 3. Weighted Composite Activation
    println!("\n⚖️ 3. Weighted Composite Activation (0.7×Swish + 0.3×Mish)");
    println!("------------------------------------------------------------------------");
    
    let weighted_config = CompositeActivationConfig::new_weighted(
        vec![ActivationType::Swish, ActivationType::Mish],
        vec![0.7, 0.3]
    )?;
    let weighted_layer = CompositeActivation::<f32>::new(weighted_config);
    let weighted_output = weighted_layer.forward(input.clone())?;
    let weighted_data = weighted_output.to_vec();
    
    println!("Weighted (0.7×Swish + 0.3×Mish): {:?}", weighted_data);

    // 4. Max Composite Activation
    println!("\n📈 4. Max Composite Activation (max(ReLU, Sigmoid))");
    println!("------------------------------------------------------------------------");
    
    let max_config = CompositeActivationConfig {
        activations: vec![ActivationType::ReLU, ActivationType::Sigmoid],
        combination_strategy: CombinationStrategy::Max,
    };
    let max_layer = CompositeActivation::<f32>::new(max_config);
    let max_output = max_layer.forward(input.clone())?;
    let max_data = max_output.to_vec();
    
    println!("Max (max(ReLU, Sigmoid)): {:?}", max_data);

    // 5. Product Composite Activation
    println!("\n✖️ 5. Product Composite Activation (Sigmoid × Tanh)");
    println!("------------------------------------------------------------------------");
    
    let product_config = CompositeActivationConfig {
        activations: vec![ActivationType::Sigmoid, ActivationType::Tanh],
        combination_strategy: CombinationStrategy::Product,
    };
    let product_layer = CompositeActivation::<f32>::new(product_config);
    let product_output = product_layer.forward(input.clone())?;
    let product_data = product_output.to_vec();
    
    println!("Product (Sigmoid × Tanh): {:?}", product_data);

    // 6. Predefined Composite Activations
    println!("\n🎯 6. Predefined Composite Activations");
    println!("------------------------------------------------------------------------");
    
    // GELU-Tanh composite
    let gelu_tanh_config = CompositeActivationConfig::gelu_tanh();
    let gelu_tanh_layer = CompositeActivation::<f32>::new(gelu_tanh_config);
    let gelu_tanh_output = gelu_tanh_layer.forward(input.clone())?;
    let gelu_tanh_data = gelu_tanh_output.to_vec();
    
    println!("GELU-Tanh: {:?}", gelu_tanh_data);
    
    // Ensemble activation
    let ensemble_config = CompositeActivationConfig::ensemble(vec![
        ActivationType::ReLU,
        ActivationType::GELU,
        ActivationType::Swish,
    ])?;
    let ensemble_layer = CompositeActivation::<f32>::new(ensemble_config);
    let ensemble_output = ensemble_layer.forward(input.clone())?;
    let ensemble_data = ensemble_output.to_vec();
    
    println!("Ensemble (ReLU + GELU + Swish)/3: {:?}", ensemble_data);

    // 7. Custom Activation Functions
    println!("\n🛠️ 7. Custom Activation Functions");
    println!("------------------------------------------------------------------------");
    
    // Parametric ReLU
    let prelu_layer = CustomActivation::parametric_relu(0.2);
    let prelu_output = prelu_layer.forward(input.clone())?;
    let prelu_data = prelu_output.to_vec();
    
    println!("Parametric ReLU (α=0.2): {:?}", prelu_data);
    
    // Scaled ReLU
    let scaled_relu_layer = CustomActivation::scaled(ActivationType::ReLU, 1.5)?;
    let scaled_relu_output = scaled_relu_layer.forward(input.clone())?;
    let scaled_relu_data = scaled_relu_output.to_vec();
    
    println!("Scaled ReLU (scale=1.5): {:?}", scaled_relu_data);

    // 8. Performance Comparison
    println!("\n⚡ 8. Performance Comparison");
    println!("------------------------------------------------------------------------");
    
    let iterations = 1000;
    let large_input = CpuTensorFactory::zeros(&Shape::new(vec![1000]))?;
    
    // Time sequential activation
    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = sequential_layer.forward(large_input.clone())?;
    }
    let sequential_time = start.elapsed();
    
    // Time weighted activation
    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = weighted_layer.forward(large_input.clone())?;
    }
    let weighted_time = start.elapsed();
    
    // Time custom activation
    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = prelu_layer.forward(large_input.clone())?;
    }
    let custom_time = start.elapsed();
    
    println!("Sequential activation: {:.2}ms ({:.1} ops/sec)", 
             sequential_time.as_millis(), 
             iterations as f64 / sequential_time.as_secs_f64());
    println!("Weighted activation: {:.2}ms ({:.1} ops/sec)", 
             weighted_time.as_millis(), 
             iterations as f64 / weighted_time.as_secs_f64());
    println!("Custom activation: {:.2}ms ({:.1} ops/sec)", 
             custom_time.as_millis(), 
             iterations as f64 / custom_time.as_secs_f64());

    // 9. Training Mode Demonstration
    println!("\n🎓 9. Training Mode Demonstration");
    println!("------------------------------------------------------------------------");
    
    let mut demo_layer = CompositeActivation::<f32>::new(
        CompositeActivationConfig::new_sequential(vec![
            ActivationType::ReLU,
            ActivationType::GELU,
        ])
    );
    
    println!("Initial training mode: {}", demo_layer.training());
    demo_layer.set_training(false);
    println!("After set_training(false): {}", demo_layer.training());
    demo_layer.set_training(true);
    println!("After set_training(true): {}", demo_layer.training());

    // 10. Error Handling Demonstration
    println!("\n❌ 10. Error Handling Demonstration");
    println!("------------------------------------------------------------------------");
    
    // Try to create weighted activation with mismatched lengths
    match CompositeActivationConfig::new_weighted(
        vec![ActivationType::ReLU, ActivationType::GELU],
        vec![0.5] // Wrong length
    ) {
        Ok(_) => println!("Unexpected success"),
        Err(e) => println!("Expected error for mismatched weights: {:?}", e),
    }
    
    // Try to create empty ensemble
    match CompositeActivationConfig::ensemble(vec![]) {
        Ok(_) => println!("Unexpected success"),
        Err(e) => println!("Expected error for empty ensemble: {:?}", e),
    }

    println!("\n🎉 Composite Activation Functions Example completed successfully!");
    println!("================================================================================");

    Ok(())
}
