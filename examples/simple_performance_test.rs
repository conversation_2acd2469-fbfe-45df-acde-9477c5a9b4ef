//! Simple performance test to validate the performance analysis system
//! This example demonstrates basic performance measurement capabilities.

use qilin_inference::performance::{
    PerformanceMeasurement, PerformanceReport,
    profiler::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    reporter::PerformanceReporter,
    analyzer::PerformanceAnalyzer,
};
use qilin_inference::layers::{
    linear::{Linear, LinearConfig},
    norm::{LayerNorm, LayerNormConfig},
    Layer,
};
use qilin_inference::layers::activation::{ActivationLayer, ActivationConfig, ActivationType};
use qilin_inference::tensor::{
    Shape, TensorFactory,
    cpu::{CpuTensor, CpuTensorFactory},
};
use qilin_inference::error::TensorError;
use std::time::{Duration, Instant};

fn main() -> Result<(), TensorError> {
    println!("🚀 Qilin Inference Engine - Simple Performance Test");
    println!("================================================================================");

    // 创建简单的性能测量
    let mut measurements = Vec::new();

    // 测试线性层性能
    println!("\n📊 Testing Linear Layer Performance...");
    let input_shape = Shape::new(vec![32, 128]);
    let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1)?;
    
    let linear_config = LinearConfig::new(128, 256);
    let mut linear_layer = Linear::<f32>::new(linear_config)?;
    
    // 预热
    for _ in 0..5 {
        let _ = linear_layer.forward(input.clone())?;
    }

    // 性能测试
    let iterations = 100usize;
    let start = Instant::now();

    for _ in 0..iterations {
        let _ = linear_layer.forward(input.clone())?;
    }
    
    let total_duration = start.elapsed();
    let avg_duration = total_duration / iterations as u32;
    
    let measurement = PerformanceMeasurement {
        operation_name: "Linear Layer".to_string(),
        configuration: "128->256 with bias".to_string(),
        iterations,
        total_duration,
        avg_duration,
        min_duration: avg_duration, // 简化
        max_duration: avg_duration, // 简化
        std_deviation: Duration::from_nanos(0), // 简化
        throughput: iterations as f64 / total_duration.as_secs_f64(),
        memory_usage: Some(input_shape.size() * std::mem::size_of::<f32>()),
        additional_metrics: std::collections::HashMap::new(),
    };
    
    measurements.push(measurement);
    
    println!("✅ Linear layer test completed:");
    println!("   - Iterations: {}", iterations);
    println!("   - Total time: {:.2}ms", total_duration.as_millis());
    println!("   - Average time: {:.2}μs", avg_duration.as_micros());
    println!("   - Throughput: {:.2} ops/sec", iterations as f64 / total_duration.as_secs_f64());

    // 测试LayerNorm性能
    println!("\n📊 Testing LayerNorm Performance...");
    let norm_config = LayerNormConfig::new(vec![128]);
    let norm_layer = LayerNorm::<f32>::new(norm_config)?;
    
    // 预热
    for _ in 0..5 {
        let _ = norm_layer.forward(input.clone())?;
    }

    let start = Instant::now();
    for _ in 0..iterations {
        let _ = norm_layer.forward(input.clone())?;
    }
    let total_duration = start.elapsed();
    let avg_duration = total_duration / iterations as u32;
    
    let measurement = PerformanceMeasurement {
        operation_name: "LayerNorm".to_string(),
        configuration: "normalized_shape=[128]".to_string(),
        iterations,
        total_duration,
        avg_duration,
        min_duration: avg_duration,
        max_duration: avg_duration,
        std_deviation: Duration::from_nanos(0),
        throughput: iterations as f64 / total_duration.as_secs_f64(),
        memory_usage: Some(input_shape.size() * std::mem::size_of::<f32>()),
        additional_metrics: std::collections::HashMap::new(),
    };
    
    measurements.push(measurement);
    
    println!("✅ LayerNorm test completed:");
    println!("   - Iterations: {}", iterations);
    println!("   - Total time: {:.2}ms", total_duration.as_millis());
    println!("   - Average time: {:.2}μs", avg_duration.as_micros());
    println!("   - Throughput: {:.2} ops/sec", iterations as f64 / total_duration.as_secs_f64());

    // 测试激活函数性能
    println!("\n📊 Testing ReLU Activation Performance...");
    let activation_config = ActivationConfig::new(ActivationType::ReLU);
    let activation_layer = ActivationLayer::new(activation_config);
    
    // 预热
    for _ in 0..5 {
        let _ = activation_layer.forward(input.clone())?;
    }

    let start = Instant::now();
    for _ in 0..iterations {
        let _ = activation_layer.forward(input.clone())?;
    }
    let total_duration = start.elapsed();
    let avg_duration = total_duration / iterations as u32;
    
    let measurement = PerformanceMeasurement {
        operation_name: "ReLU Activation".to_string(),
        configuration: "element-wise".to_string(),
        iterations,
        total_duration,
        avg_duration,
        min_duration: avg_duration,
        max_duration: avg_duration,
        std_deviation: Duration::from_nanos(0),
        throughput: iterations as f64 / total_duration.as_secs_f64(),
        memory_usage: Some(input_shape.size() * std::mem::size_of::<f32>()),
        additional_metrics: std::collections::HashMap::new(),
    };
    
    measurements.push(measurement);
    
    println!("✅ ReLU activation test completed:");
    println!("   - Iterations: {}", iterations);
    println!("   - Total time: {:.2}ms", total_duration.as_millis());
    println!("   - Average time: {:.2}μs", avg_duration.as_micros());
    println!("   - Throughput: {:.2} ops/sec", iterations as f64 / total_duration.as_secs_f64());

    // 生成性能报告
    println!("\n📈 Generating Performance Report...");
    let mut report = PerformanceReport::new();
    for measurement in measurements.iter() {
        report.add_measurement(measurement.clone());
    }
    let console_report = PerformanceReporter::generate_console_report(&report);
    println!("{}", console_report);

    // 统计分析
    println!("\n📊 Statistical Analysis...");
    if let Some(stats) = PerformanceAnalyzer::calculate_statistics(&measurements) {
        println!("✅ Performance Statistics:");
        println!("   - Count: {}", stats.count);
        println!("   - Mean duration: {:.2}μs", stats.mean.as_micros());
        println!("   - Median duration: {:.2}μs", stats.median.as_micros());
        println!("   - Min duration: {:.2}μs", stats.min.as_micros());
        println!("   - Max duration: {:.2}μs", stats.max.as_micros());
        println!("   - Standard deviation: {:.2}μs", stats.std_dev.as_micros());
        println!("   - Coefficient of variation: {:.2}%", stats.coefficient_of_variation * 100.0);
    }

    println!("\n🎉 Simple performance test completed successfully!");
    println!("================================================================================");

    Ok(())
}
