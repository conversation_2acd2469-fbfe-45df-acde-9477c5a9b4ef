//! Parallel Layer Example
//!
//! This example demonstrates the usage of the Parallel layer for composing
//! multiple layers in parallel. The Parallel layer applies all branches to
//! the same input and combines their outputs using various strategies.
//!
//! Run with: cargo run --example parallel_layer_example

use qilin_inference::layers::*;
use qilin_inference::tensor::{Tensor, <PERSON>ha<PERSON>, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
use qilin_inference::error::TensorError;

fn main() -> Result<(), TensorError> {
    println!("🔀 Parallel Layer Example");
    println!("=========================\n");

    // Create a layer factory for convenience
    let factory = LayerFactory::<f32>::new();

    println!("1. Creating Parallel Branches");
    println!("-----------------------------");

    // Create individual branches
    let branch1 = factory.create_layer(LayerConfig::Linear {
        input_size: 256,
        output_size: 128,
        bias: true,
    })?;
    println!("  ✓ Branch1 (Linear) created: {} parameters", branch1.parameter_count());

    let branch2 = factory.create_layer(LayerConfig::Linear {
        input_size: 256,
        output_size: 128,
        bias: true,
    })?;
    println!("  ✓ Branch2 (Linear) created: {} parameters", branch2.parameter_count());

    let branch3 = factory.create_layer(LayerConfig::Linear {
        input_size: 256,
        output_size: 64,
        bias: true,
    })?;
    println!("  ✓ Branch3 (Linear) created: {} parameters", branch3.parameter_count());

    println!();

    // 2. Creating Parallel Networks with Different Strategies
    println!("2. Creating Parallel Networks with Different Strategies");
    println!("-------------------------------------------------------");

    // Concatenation strategy
    let mut concat_network = Parallel::new(CombineStrategy::Concatenate)
        .add_branch(branch1)
        .add_branch(branch2);

    println!("  ✓ Concatenation network created: {} branches, {} parameters", 
             concat_network.len(), concat_network.parameter_count());
    println!("    Strategy: {:?}", concat_network.combine_strategy());

    // Addition strategy
    let add_branch1 = factory.create_layer(LayerConfig::Linear {
        input_size: 256,
        output_size: 128,
        bias: true,
    })?;
    let add_branch2 = factory.create_layer(LayerConfig::Linear {
        input_size: 256,
        output_size: 128,
        bias: true,
    })?;

    let mut add_network = Parallel::new(CombineStrategy::Add)
        .add_branch(add_branch1)
        .add_branch(add_branch2);

    println!("  ✓ Addition network created: {} branches, {} parameters", 
             add_network.len(), add_network.parameter_count());
    println!("    Strategy: {:?}", add_network.combine_strategy());

    // Average strategy
    let avg_branch1 = factory.create_layer(LayerConfig::Linear {
        input_size: 256,
        output_size: 128,
        bias: true,
    })?;
    let avg_branch2 = factory.create_layer(LayerConfig::Linear {
        input_size: 256,
        output_size: 128,
        bias: true,
    })?;
    let avg_branch3 = factory.create_layer(LayerConfig::Linear {
        input_size: 256,
        output_size: 128,
        bias: true,
    })?;

    let mut avg_network = Parallel::new(CombineStrategy::Average)
        .add_branch(avg_branch1)
        .add_branch(avg_branch2)
        .add_branch(avg_branch3);

    println!("  ✓ Average network created: {} branches, {} parameters", 
             avg_network.len(), avg_network.parameter_count());
    println!("    Strategy: {:?}", avg_network.combine_strategy());

    println!();

    // 3. Testing Forward Pass
    println!("3. Testing Forward Pass");
    println!("-----------------------");

    // Create test input
    let input = CpuTensorFactory::randn(&Shape::new(vec![16, 256]), 0.0, 1.0)?;
    println!("  Input shape: {:?}", input.shape().dims());

    // Test concatenation network
    let concat_output = concat_network.forward(input.clone())?;
    println!("  ✓ Concatenation forward pass successful");
    println!("    Output shape: {:?}", concat_output.shape().dims());

    // Test addition network
    let add_output = add_network.forward(input.clone())?;
    println!("  ✓ Addition forward pass successful");
    println!("    Output shape: {:?}", add_output.shape().dims());

    // Test average network
    let avg_output = avg_network.forward(input.clone())?;
    println!("  ✓ Average forward pass successful");
    println!("    Output shape: {:?}", avg_output.shape().dims());

    println!();

    // 4. Branch Management
    println!("4. Branch Management");
    println!("-------------------");

    println!("  Concatenation network branches:");
    let concat_summary = concat_network.branch_summary();
    for (index, name, params) in concat_summary {
        println!("    [{:2}] {}: {} parameters", index, name, params);
    }

    // Test branch access
    if let Some(branch) = concat_network.get_branch(0) {
        println!("  ✓ First branch accessible: {} parameters", branch.parameter_count());
    }

    println!();

    // 5. Training Mode Control
    println!("5. Training Mode Control");
    println!("-----------------------");

    println!("  Current training mode: {}", concat_network.training());
    
    // Switch to evaluation mode
    concat_network.set_training(false);
    println!("  ✓ Switched to evaluation mode: {}", concat_network.training());

    // Test forward pass in eval mode
    let eval_input = CpuTensorFactory::randn(&Shape::new(vec![8, 256]), 0.0, 1.0)?;
    let eval_output = concat_network.forward(eval_input)?;
    println!("  ✓ Forward pass in eval mode successful");
    println!("    Eval output shape: {:?}", eval_output.shape().dims());

    // Switch back to training mode
    concat_network.set_training(true);
    println!("  ✓ Switched back to training mode: {}", concat_network.training());

    println!();

    // 6. Complex Parallel Architectures
    println!("6. Complex Parallel Architectures");
    println!("---------------------------------");

    // Multi-head attention-like architecture
    let head1 = factory.create_layer(LayerConfig::Linear { input_size: 512, output_size: 64, bias: true })?;
    let head2 = factory.create_layer(LayerConfig::Linear { input_size: 512, output_size: 64, bias: true })?;
    let head3 = factory.create_layer(LayerConfig::Linear { input_size: 512, output_size: 64, bias: true })?;
    let head4 = factory.create_layer(LayerConfig::Linear { input_size: 512, output_size: 64, bias: true })?;

    let mut multi_head = Parallel::new(CombineStrategy::Concatenate)
        .add_branch(head1)
        .add_branch(head2)
        .add_branch(head3)
        .add_branch(head4);

    println!("  ✓ Multi-head architecture created: {} heads, {} parameters", 
             multi_head.len(), multi_head.parameter_count());

    // Test multi-head forward pass
    let mh_input = CpuTensorFactory::randn(&Shape::new(vec![32, 512]), 0.0, 1.0)?;
    let mh_output = multi_head.forward(mh_input)?;
    println!("  ✓ Multi-head forward pass successful");
    println!("    Input shape: [32, 512] -> Output shape: {:?}", mh_output.shape().dims());

    // Residual-like architecture (addition)
    let main_branch = factory.create_layer(LayerConfig::FeedForward {
        input_dim: 256,
        hidden_dim: 512,
        output_dim: 256,
        activation: ActivationType::ReLU,
        dropout: 0.0,
        bias: true,
    })?;

    // Identity-like branch (just a linear layer that preserves dimensions)
    let identity_branch = factory.create_layer(LayerConfig::Linear {
        input_size: 256,
        output_size: 256,
        bias: false,
    })?;

    let mut residual_like = Parallel::new(CombineStrategy::Add)
        .add_branch(main_branch)
        .add_branch(identity_branch);

    println!("  ✓ Residual-like architecture created: {} branches, {} parameters", 
             residual_like.len(), residual_like.parameter_count());

    let res_input = CpuTensorFactory::randn(&Shape::new(vec![16, 256]), 0.0, 1.0)?;
    let res_output = residual_like.forward(res_input)?;
    println!("  ✓ Residual-like forward pass successful");
    println!("    Output shape: {:?}", res_output.shape().dims());

    println!();

    // 7. Strategy Comparison
    println!("7. Strategy Comparison");
    println!("---------------------");

    // Create identical branches for fair comparison
    let test_branches = vec![
        factory.create_layer(LayerConfig::Linear { input_size: 128, output_size: 64, bias: true })?,
        factory.create_layer(LayerConfig::Linear { input_size: 128, output_size: 64, bias: true })?,
    ];

    let strategies = vec![
        CombineStrategy::First,
        CombineStrategy::Last,
        CombineStrategy::Add,
        CombineStrategy::Average,
    ];

    let test_input = CpuTensorFactory::randn(&Shape::new(vec![8, 128]), 0.0, 1.0)?;

    for strategy in strategies {
        let branch1 = factory.create_layer(LayerConfig::Linear { input_size: 128, output_size: 64, bias: true })?;
        let branch2 = factory.create_layer(LayerConfig::Linear { input_size: 128, output_size: 64, bias: true })?;
        
        let mut test_network = Parallel::new(strategy.clone())
            .add_branch(branch1)
            .add_branch(branch2);

        let output = test_network.forward(test_input.clone())?;
        println!("  ✓ {:?} strategy: input {:?} -> output {:?}", 
                 strategy, test_input.shape().dims(), output.shape().dims());
    }

    println!();

    // 8. Performance Benchmark
    println!("8. Performance Benchmark");
    println!("-----------------------");

    let bench_input = CpuTensorFactory::randn(&Shape::new(vec![32, 256]), 0.0, 1.0)?;
    let iterations = 50;

    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = concat_network.forward(bench_input.clone())?;
    }
    let duration = start.elapsed();

    println!("  ✓ Performance benchmark completed");
    println!("    • Network: 2 parallel branches (256 -> 128 each)");
    println!("    • Strategy: Concatenate");
    println!("    • Input shape: [32, 256]");
    println!("    • Iterations: {}", iterations);
    println!("    • Total time: {:.2}ms", duration.as_secs_f64() * 1000.0);
    println!("    • Time per iteration: {:.3}ms", duration.as_secs_f64() * 1000.0 / iterations as f64);
    println!("    • Throughput: {:.1} samples/sec", (32 * iterations) as f64 / duration.as_secs_f64());

    println!();

    // 9. Error Handling
    println!("9. Error Handling");
    println!("----------------");

    // Test empty parallel
    let empty_parallel = Parallel::<f32>::new(CombineStrategy::Concatenate);
    match empty_parallel.forward(CpuTensorFactory::zeros(&Shape::new(vec![1, 10]))?) {
        Ok(_) => println!("  ❌ Empty parallel should have failed"),
        Err(_) => println!("  ✓ Empty parallel correctly returns error"),
    }

    // Test branch access bounds
    if concat_network.get_branch(100).is_none() {
        println!("  ✓ Out-of-bounds branch access correctly returns None");
    }

    println!();

    // 10. Summary
    println!("10. Summary");
    println!("----------");
    println!("  ✅ Parallel layer successfully created and tested");
    println!("  ✅ Multiple combine strategies work correctly");
    println!("  ✅ Forward propagation applies all branches in parallel");
    println!("  ✅ Parameter counting aggregates correctly across branches");
    println!("  ✅ Training mode propagates to all contained branches");
    println!("  ✅ Branch management functions work properly");
    println!("  ✅ Complex architectures (multi-head, residual-like) can be built");
    println!("  ✅ Performance is reasonable for inference workloads");
    println!("  ✅ Error handling works as expected");
    println!();

    println!("🎯 Parallel layer enables powerful branching architectures!");
    println!("📦 Key features:");
    println!("   • Multiple combine strategies (Concatenate, Add, Average, etc.)");
    println!("   • Parallel branch execution with same input");
    println!("   • Flexible output combination");
    println!("   • Support for complex architectures (multi-head, residual)");
    println!("   • Aggregate parameter and training mode management");
    println!();

    println!("✨ Sequential + Parallel layers provide complete building blocks for any neural architecture!");

    Ok(())
}
