//! Basic performance test to validate the performance analysis system
//! This example demonstrates basic performance measurement capabilities.

use qilin_inference::performance::{
    PerformanceMeasurement, PerformanceReport,
    reporter::PerformanceReporter,
    analyzer::PerformanceAnalyzer,
};
use qilin_inference::tensor::{
    Shape, TensorFactory, TensorOps,
    cpu::{CpuTensor, CpuTensorFactory},
};
use qilin_inference::error::TensorError;
use std::time::{Duration, Instant};

fn main() -> Result<(), TensorError> {
    println!("🚀 Qilin Inference Engine - Basic Performance Test");
    println!("================================================================================");

    // 创建简单的性能测量
    let mut measurements = Vec::new();

    // 测试张量创建性能
    println!("\n📊 Testing Tensor Creation Performance...");
    let shape = Shape::new(vec![1000, 1000]);
    
    let iterations = 10usize;
    let start = Instant::now();
    
    for _ in 0..iterations {
        let _tensor: CpuTensor<f32> = CpuTensorFactory::zeros(&shape)?;
    }
    
    let total_duration = start.elapsed();
    let avg_duration = total_duration / iterations as u32;
    
    let measurement = PerformanceMeasurement {
        operation_name: "Tensor Creation".to_string(),
        configuration: "1000x1000 zeros".to_string(),
        iterations,
        total_duration,
        avg_duration,
        min_duration: avg_duration, // 简化
        max_duration: avg_duration, // 简化
        std_deviation: Duration::from_nanos(0), // 简化
        throughput: iterations as f64 / total_duration.as_secs_f64(),
        memory_usage: Some(shape.size() * std::mem::size_of::<f32>()),
        additional_metrics: std::collections::HashMap::new(),
    };
    
    measurements.push(measurement);
    
    println!("✅ Tensor creation test completed:");
    println!("   - Iterations: {}", iterations);
    println!("   - Total time: {:.2}ms", total_duration.as_millis());
    println!("   - Average time: {:.2}ms", avg_duration.as_millis());
    println!("   - Throughput: {:.2} ops/sec", iterations as f64 / total_duration.as_secs_f64());

    // 测试张量加法性能
    println!("\n📊 Testing Tensor Addition Performance...");
    let a: CpuTensor<f32> = CpuTensorFactory::ones(&shape)?;
    let b: CpuTensor<f32> = CpuTensorFactory::ones(&shape)?;
    
    let start = Instant::now();
    for _ in 0..iterations {
        let _result = a.add(&b)?;
    }
    let total_duration = start.elapsed();
    let avg_duration = total_duration / iterations as u32;
    
    let measurement = PerformanceMeasurement {
        operation_name: "Tensor Addition".to_string(),
        configuration: "1000x1000 + 1000x1000".to_string(),
        iterations,
        total_duration,
        avg_duration,
        min_duration: avg_duration,
        max_duration: avg_duration,
        std_deviation: Duration::from_nanos(0),
        throughput: iterations as f64 / total_duration.as_secs_f64(),
        memory_usage: Some(shape.size() * std::mem::size_of::<f32>() * 2),
        additional_metrics: std::collections::HashMap::new(),
    };
    
    measurements.push(measurement);
    
    println!("✅ Tensor addition test completed:");
    println!("   - Iterations: {}", iterations);
    println!("   - Total time: {:.2}ms", total_duration.as_millis());
    println!("   - Average time: {:.2}ms", avg_duration.as_millis());
    println!("   - Throughput: {:.2} ops/sec", iterations as f64 / total_duration.as_secs_f64());

    // 测试矩阵乘法性能
    println!("\n📊 Testing Matrix Multiplication Performance...");
    let matrix_shape_a = Shape::new(vec![512, 256]);
    let matrix_shape_b = Shape::new(vec![256, 512]);
    let matrix_a: CpuTensor<f32> = CpuTensorFactory::randn(&matrix_shape_a, 0.0, 0.1)?;
    let matrix_b: CpuTensor<f32> = CpuTensorFactory::randn(&matrix_shape_b, 0.0, 0.1)?;
    
    let start = Instant::now();
    for _ in 0..iterations {
        let _result = matrix_a.matmul(&matrix_b)?;
    }
    let total_duration = start.elapsed();
    let avg_duration = total_duration / iterations as u32;
    
    let measurement = PerformanceMeasurement {
        operation_name: "Matrix Multiplication".to_string(),
        configuration: "512x256 @ 256x512".to_string(),
        iterations,
        total_duration,
        avg_duration,
        min_duration: avg_duration,
        max_duration: avg_duration,
        std_deviation: Duration::from_nanos(0),
        throughput: iterations as f64 / total_duration.as_secs_f64(),
        memory_usage: Some((matrix_shape_a.size() + matrix_shape_b.size()) * std::mem::size_of::<f32>()),
        additional_metrics: std::collections::HashMap::new(),
    };
    
    measurements.push(measurement);
    
    println!("✅ Matrix multiplication test completed:");
    println!("   - Iterations: {}", iterations);
    println!("   - Total time: {:.2}ms", total_duration.as_millis());
    println!("   - Average time: {:.2}ms", avg_duration.as_millis());
    println!("   - Throughput: {:.2} ops/sec", iterations as f64 / total_duration.as_secs_f64());

    // 生成性能报告
    println!("\n📈 Generating Performance Report...");
    let mut report = PerformanceReport::new();
    for measurement in measurements.iter() {
        report.add_measurement(measurement.clone());
    }
    let console_report = PerformanceReporter::generate_console_report(&report);
    println!("{}", console_report);

    // 统计分析
    println!("\n📊 Statistical Analysis...");
    if let Some(stats) = PerformanceAnalyzer::calculate_statistics(&measurements) {
        println!("✅ Performance Statistics:");
        println!("   - Count: {}", stats.count);
        println!("   - Mean duration: {:.2}ms", stats.mean.as_millis());
        println!("   - Median duration: {:.2}ms", stats.median.as_millis());
        println!("   - Min duration: {:.2}ms", stats.min.as_millis());
        println!("   - Max duration: {:.2}ms", stats.max.as_millis());
        println!("   - Standard deviation: {:.2}ms", stats.std_dev.as_millis());
        println!("   - Coefficient of variation: {:.2}%", stats.coefficient_of_variation * 100.0);
    }

    println!("\n🎉 Basic performance test completed successfully!");
    println!("================================================================================");

    Ok(())
}
