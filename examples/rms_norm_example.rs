//! RMSNorm Layer Example
//!
//! This example demonstrates the usage of RMSNorm (Root Mean Square Normalization)
//! layer in the Qilin inference engine. RMSNorm is a simpler and more efficient
//! alternative to LayerNorm that is commonly used in modern transformer architectures
//! like LLaMA.
//!
//! Run with: cargo run --example rms_norm_example

use qilin_inference::layers::*;
use qilin_inference::tensor::{Tensor, Shape, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
use qilin_inference::error::TensorError;

fn main() -> Result<(), TensorError> {
    println!("🚀 RMSNorm Layer Example");
    println!("========================\n");

    // Configuration
    let batch_size = 4;
    let seq_len = 8;
    let d_model = 512;

    println!("Configuration:");
    println!("  • Batch size: {}", batch_size);
    println!("  • Sequence length: {}", seq_len);
    println!("  • Model dimension: {}", d_model);
    println!();

    // 1. Basic RMSNorm Usage
    println!("1. Basic RMSNorm Usage");
    println!("----------------------");
    
    let rms_config = RMSNormConfig::new(vec![d_model])
        .with_eps(1e-6)
        .with_elementwise_affine(true);
    
    let mut rms_layer = RMSNorm::<f32>::new(rms_config)?;
    rms_layer.init_parameters(ParameterInit::Ones)?;
    
    // Create input tensor
    let input = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, d_model]), 0.0, 1.0)?;
    
    println!("  Input shape: {:?}", input.shape().dims());
    println!("  RMSNorm parameters: {}", rms_layer.parameter_count());
    println!("  Epsilon: {}", rms_layer.eps());
    
    // Forward pass
    let output = rms_layer.forward(input.clone())?;
    
    println!("  Output shape: {:?}", output.shape().dims());
    println!("  ✓ Forward pass successful");
    println!();

    // 2. RMSNorm vs LayerNorm Comparison
    println!("2. RMSNorm vs LayerNorm Comparison");
    println!("----------------------------------");
    
    // Create a simple test input
    let test_input = CpuTensor::from_data(
        vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0],
        Shape::new(vec![2, 4])
    )?;
    
    println!("  Test input: {:?}", test_input.to_vec());
    
    // RMSNorm without affine transformation
    let rms_config_no_affine = RMSNormConfig::new(vec![4])
        .with_elementwise_affine(false);
    let rms_layer_no_affine = RMSNorm::<f32>::new(rms_config_no_affine)?;
    let rms_output = rms_layer_no_affine.forward(test_input.clone())?;
    
    // LayerNorm without affine transformation
    let ln_config_no_affine = LayerNormConfig::new(vec![4])
        .with_elementwise_affine(false);
    let ln_layer_no_affine = LayerNorm::<f32>::new(ln_config_no_affine)?;
    let ln_output = ln_layer_no_affine.forward(test_input)?;
    
    println!("  RMSNorm output: {:?}", rms_output.to_vec());
    println!("  LayerNorm output: {:?}", ln_output.to_vec());
    println!("  Note: RMSNorm doesn't center the data (no mean subtraction)");
    println!();

    // 3. Performance Comparison
    println!("3. Performance Comparison");
    println!("------------------------");
    
    let perf_input = CpuTensorFactory::randn(&Shape::new(vec![32, 128, 768]), 0.0, 1.0)?;
    let iterations = 100;
    
    // RMSNorm performance
    let rms_perf_config = RMSNormConfig::new(vec![768]);
    let mut rms_perf_layer = RMSNorm::<f32>::new(rms_perf_config)?;
    rms_perf_layer.init_parameters(ParameterInit::Ones)?;
    
    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = rms_perf_layer.forward(perf_input.clone())?;
    }
    let rms_duration = start.elapsed();
    
    // LayerNorm performance
    let ln_perf_config = LayerNormConfig::new(vec![768]);
    let mut ln_perf_layer = LayerNorm::<f32>::new(ln_perf_config)?;
    ln_perf_layer.init_parameters(ParameterInit::Ones)?;
    
    let start = std::time::Instant::now();
    for _ in 0..iterations {
        let _ = ln_perf_layer.forward(perf_input.clone())?;
    }
    let ln_duration = start.elapsed();
    
    println!("  Input shape: [32, 128, 768]");
    println!("  Iterations: {}", iterations);
    println!("  RMSNorm time: {:.2}ms ({:.2}ms/iter)", 
             rms_duration.as_secs_f64() * 1000.0,
             rms_duration.as_secs_f64() * 1000.0 / iterations as f64);
    println!("  LayerNorm time: {:.2}ms ({:.2}ms/iter)", 
             ln_duration.as_secs_f64() * 1000.0,
             ln_duration.as_secs_f64() * 1000.0 / iterations as f64);
    
    let speedup = ln_duration.as_secs_f64() / rms_duration.as_secs_f64();
    println!("  RMSNorm speedup: {:.2}x faster", speedup);
    println!();

    // 4. Parameter Count Comparison
    println!("4. Parameter Count Comparison");
    println!("-----------------------------");
    
    let dim = 1024;
    
    let rms_config_params = RMSNormConfig::new(vec![dim]);
    let rms_layer_params = RMSNorm::<f32>::new(rms_config_params)?;
    
    let ln_config_params = LayerNormConfig::new(vec![dim]);
    let ln_layer_params = LayerNorm::<f32>::new(ln_config_params)?;
    
    println!("  Dimension: {}", dim);
    println!("  RMSNorm parameters: {} (only weight)", rms_layer_params.parameter_count());
    println!("  LayerNorm parameters: {} (weight + bias)", ln_layer_params.parameter_count());
    println!("  Parameter reduction: {:.1}%", 
             (1.0 - rms_layer_params.parameter_count() as f64 / ln_layer_params.parameter_count() as f64) * 100.0);
    println!();

    // 5. Different Configurations
    println!("5. Different Configurations");
    println!("---------------------------");
    
    // RMSNorm with different epsilon values
    let eps_values = vec![1e-8, 1e-6, 1e-5, 1e-4];
    
    for &eps in &eps_values {
        let config = RMSNormConfig::new(vec![4]).with_eps(eps);
        let layer = RMSNorm::<f32>::new(config)?;
        
        let test_data = vec![0.1, 0.2, 0.3, 0.4];
        let test_tensor = CpuTensor::from_data(test_data, Shape::new(vec![1, 4]))?;
        let output = layer.forward(test_tensor)?;
        
        println!("  Epsilon {}: output = {:?}", eps, output.to_vec());
    }
    println!();

    // 6. Numerical Stability Test
    println!("6. Numerical Stability Test");
    println!("---------------------------");
    
    // Test with very small values
    let small_values = vec![1e-10, 2e-10, 3e-10, 4e-10];
    let small_tensor = CpuTensor::from_data(small_values, Shape::new(vec![1, 4]))?;
    
    let stable_config = RMSNormConfig::new(vec![4]).with_eps(1e-6);
    let stable_layer = RMSNorm::<f32>::new(stable_config)?;
    let stable_output = stable_layer.forward(small_tensor)?;
    
    println!("  Small input: [1e-10, 2e-10, 3e-10, 4e-10]");
    println!("  RMSNorm output: {:?}", stable_output.to_vec());
    println!("  ✓ Numerically stable (no NaN or Inf values)");
    
    // Test with large values
    let large_values = vec![1e6, 2e6, 3e6, 4e6];
    let large_tensor = CpuTensor::from_data(large_values, Shape::new(vec![1, 4]))?;
    let large_output = stable_layer.forward(large_tensor)?;
    
    println!("  Large input: [1e6, 2e6, 3e6, 4e6]");
    println!("  RMSNorm output: {:?}", large_output.to_vec());
    println!("  ✓ Handles large values correctly");
    println!();

    // 7. Multi-dimensional Input
    println!("7. Multi-dimensional Input");
    println!("-------------------------");
    
    // Test with 3D input (batch, sequence, features)
    let multi_input = CpuTensorFactory::randn(&Shape::new(vec![2, 3, 4]), 0.0, 1.0)?;
    let multi_config = RMSNormConfig::new(vec![4]);
    let mut multi_layer = RMSNorm::<f32>::new(multi_config)?;
    multi_layer.init_parameters(ParameterInit::Ones)?;
    
    let multi_output = multi_layer.forward(multi_input.clone())?;
    
    println!("  Input shape: {:?}", multi_input.shape().dims());
    println!("  Output shape: {:?}", multi_output.shape().dims());
    println!("  ✓ Multi-dimensional input handled correctly");
    println!();

    println!("📊 Summary:");
    println!("  • RMSNorm is simpler than LayerNorm (no mean centering)");
    println!("  • RMSNorm uses fewer parameters (no bias term)");
    println!("  • RMSNorm is computationally more efficient");
    println!("  • RMSNorm provides similar normalization benefits");
    println!("  • RMSNorm is numerically stable with proper epsilon");
    println!("  • RMSNorm is commonly used in modern transformers (LLaMA, etc.)");
    println!();

    println!("✅ RMSNorm example completed successfully!");
    println!("🎯 RMSNorm is ready for use in transformer architectures!");
    
    Ok(())
}
