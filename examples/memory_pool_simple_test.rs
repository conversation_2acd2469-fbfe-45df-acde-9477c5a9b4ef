//! Simple Memory Pool Test
//!
//! This example tests the basic functionality of the enhanced memory pool.

use qilin_inference::tensor::memory_pool::{TensorMemoryPool, MemoryPoolConfig, GlobalMemoryPool};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Simple Memory Pool Test ===\n");
    
    // Test 1: Basic functionality
    test_basic_functionality()?;
    
    // Test 2: Statistics
    test_statistics()?;
    
    // Test 3: Batch operations
    test_batch_operations()?;
    
    // Test 4: Global pools
    test_global_pools()?;
    
    println!("\n=== All Tests Passed! ===");
    Ok(())
}

fn test_basic_functionality() -> Result<(), Box<dyn std::error::Error>> {
    println!("1. Testing Basic Functionality");
    
    let config = MemoryPoolConfig {
        enable_stats: true,
        enable_size_bucketing: true,
        preallocation_sizes: vec![128, 256, 512],
        ..Default::default()
    };
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(config);
    
    // Test allocation
    let buffer1 = pool.allocate(128)?;
    let buffer2 = pool.allocate(256)?;
    
    println!("  ✓ Allocated buffers of sizes {} and {}", buffer1.len(), buffer2.len());
    
    // Test deallocation
    pool.deallocate(buffer1);
    pool.deallocate(buffer2);
    
    println!("  ✓ Deallocated buffers");
    
    // Test reuse
    let buffer3 = pool.allocate(128)?;
    let buffer4 = pool.allocate(256)?;
    
    println!("  ✓ Reused buffers of sizes {} and {}", buffer3.len(), buffer4.len());
    
    Ok(())
}

fn test_statistics() -> Result<(), Box<dyn std::error::Error>> {
    println!("2. Testing Statistics");
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::new(MemoryPoolConfig {
        enable_stats: true,
        ..Default::default()
    });
    
    // Perform operations
    let buffer = pool.allocate(1024)?;
    pool.deallocate(buffer);
    let _buffer2 = pool.allocate(1024)?; // Should be a hit
    
    let stats = pool.stats();
    println!("  Total allocations: {}", stats.total_allocations);
    println!("  Pool hits: {}", stats.pool_hits);
    println!("  Pool misses: {}", stats.pool_misses);
    println!("  Hit rate: {:.1}%", stats.hit_rate());
    println!("  Average allocation size: {:.1} bytes", stats.avg_allocation_size());
    println!("  Summary: {}", stats.summary());
    
    println!("  ✓ Statistics working correctly");
    Ok(())
}

fn test_batch_operations() -> Result<(), Box<dyn std::error::Error>> {
    println!("3. Testing Batch Operations");
    
    let pool: TensorMemoryPool<f32> = TensorMemoryPool::default();
    
    // Batch allocation
    let buffers = pool.allocate_batch(512, 5)?;
    println!("  ✓ Batch allocated {} buffers", buffers.len());
    
    // Verify all buffers have correct size
    let all_correct_size = buffers.iter().all(|b| b.len() == 512);
    println!("  ✓ All buffers have correct size: {}", all_correct_size);
    
    // Batch deallocation
    pool.deallocate_batch(buffers);
    println!("  ✓ Batch deallocated buffers");
    
    Ok(())
}

fn test_global_pools() -> Result<(), Box<dyn std::error::Error>> {
    println!("4. Testing Global Pools");
    
    // Test f32 global pool
    let f32_pool = GlobalMemoryPool::f32();
    let buffer1 = f32_pool.allocate(256)?;
    println!("  ✓ f32 global pool allocated buffer of size {}", buffer1.len());
    f32_pool.deallocate(buffer1);
    
    // Test f64 global pool
    let f64_pool = GlobalMemoryPool::f64();
    let buffer2 = f64_pool.allocate(128)?;
    println!("  ✓ f64 global pool allocated buffer of size {}", buffer2.len());
    f64_pool.deallocate(buffer2);
    
    // Test singleton behavior
    let f32_pool2 = GlobalMemoryPool::f32();
    let same_instance = std::ptr::eq(f32_pool, f32_pool2);
    println!("  ✓ Global pools are singletons: {}", same_instance);
    
    Ok(())
}
