//! Comprehensive Performance Analysis Example
//!
//! This example demonstrates the complete performance analysis capabilities
//! of the Qilin inference engine, including layer profiling, performance
//! reporting, and regression analysis.

use qilin_inference::performance::{
    PerformanceBenchmarker, PerformanceReport, BenchmarkConfig,
    profiler::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    reporter::<PERSON>Reporter,
    analyzer::PerformanceAnalyzer,
    metrics::{MetricsCollector, PerformanceRegistry},
    PerformanceMeasurement,
};
use qilin_inference::layers::*;
use qilin_inference::tensor::{
    Tensor, Shape, TensorOps, TensorFactory,
    cpu::{CpuTensor, CpuTensorFactory},
};
use qilin_inference::error::TensorError;
use std::time::Duration;

fn main() -> Result<(), TensorError> {
    println!("🚀 Qilin Inference Engine - Comprehensive Performance Analysis");
    println!("{}", "=".repeat(80));
    
    // Initialize performance analysis components
    let benchmark_config = BenchmarkConfig {
        warmup_iterations: 5,
        measurement_iterations: 50,
        max_duration: Duration::from_secs(10),
        min_iterations: 10,
        stability_threshold: 0.15,
        measure_memory: true,
    };
    
    let mut profiler = LayerProfiler::with_config(benchmark_config);
    let mut performance_registry = PerformanceRegistry::new();
    
    println!("\n📊 Starting comprehensive layer profiling...");
    
    // 1. Profile all neural network layers
    println!("\n🔧 Profiling all layer types...");
    let comprehensive_report = profiler.profile_all_layers()?;
    
    println!("✅ Comprehensive profiling completed!");
    println!("   • Total measurements: {}", comprehensive_report.measurements.len());
    
    // 2. Generate detailed console report
    println!("\n📋 Generating performance report...");
    let console_report = PerformanceReporter::generate_console_report(&comprehensive_report);
    println!("{}", console_report);
    
    // 3. Analyze specific layer performance
    println!("\n🔍 Detailed Layer Analysis");
    println!("{}", "-".repeat(50));
    
    analyze_linear_layer_performance(&mut profiler)?;
    analyze_activation_performance(&mut profiler)?;
    analyze_normalization_performance(&mut profiler)?;
    
    // 4. Performance comparison analysis
    println!("\n⚖️  Performance Comparison Analysis");
    println!("{}", "-".repeat(50));
    
    perform_comparison_analysis(&mut profiler)?;
    
    // 5. Memory usage analysis
    println!("\n💾 Memory Usage Analysis");
    println!("{}", "-".repeat(50));
    
    analyze_memory_usage()?;
    
    // 6. Generate JSON report for external analysis
    println!("\n📄 Generating JSON report...");
    match PerformanceReporter::generate_json_report(&comprehensive_report) {
        Ok(json_report) => {
            println!("✅ JSON report generated ({} characters)", json_report.len());
            // In a real application, you would save this to a file
            // std::fs::write("performance_report.json", json_report)?;
        }
        Err(e) => println!("❌ Failed to generate JSON report: {}", e),
    }
    
    // 7. Generate CSV report for data analysis
    println!("\n📊 Generating CSV report...");
    let csv_report = PerformanceReporter::generate_csv_report(&comprehensive_report);
    println!("✅ CSV report generated ({} lines)", csv_report.lines().count());
    
    // 8. Performance insights
    println!("\n💡 Performance Insights");
    println!("{}", "-".repeat(50));
    
    let insights = PerformanceAnalyzer::generate_insights(&comprehensive_report);
    for (i, insight) in insights.iter().enumerate() {
        println!("{}. {}", i + 1, insight);
    }
    
    println!("\n🎉 Comprehensive performance analysis completed successfully!");
    println!("{}", "=".repeat(80));
    
    Ok(())
}

fn analyze_linear_layer_performance(profiler: &mut LayerProfiler) -> Result<(), TensorError> {
    println!("\n🔧 Linear Layer Performance Analysis");
    
    // Profile linear layers with different configurations
    let input_dims = [256, 512, 768, 1024];
    let hidden_dims = [512, 1024, 2048, 4096];
    
    let linear_report = profiler.profile_linear_layer(&input_dims, &hidden_dims)?;
    
    // Find best and worst performing configurations
    let mut measurements = linear_report.measurements.clone();
    measurements.sort_by_key(|m| m.avg_duration);
    
    if let (Some(fastest), Some(slowest)) = (measurements.first(), measurements.last()) {
        let speedup = slowest.avg_duration.as_secs_f64() / fastest.avg_duration.as_secs_f64();
        
        println!("  🏆 Fastest configuration: {} ({:.2}μs)", 
                fastest.configuration, fastest.avg_duration.as_micros());
        println!("  🐌 Slowest configuration: {} ({:.2}μs)", 
                slowest.configuration, slowest.avg_duration.as_micros());
        println!("  📈 Performance range: {:.2}x difference", speedup);
        
        // Calculate FLOPS for the fastest configuration
        if let Some(flops) = fastest.additional_metrics.get("flops") {
            let gflops = flops / fastest.avg_duration.as_secs_f64() / 1e9;
            println!("  ⚡ Peak performance: {:.2} GFLOPS", gflops);
        }
    }
    
    Ok(())
}

fn analyze_activation_performance(profiler: &mut LayerProfiler) -> Result<(), TensorError> {
    println!("\n🎯 Activation Function Performance Analysis");
    
    let shapes = vec![
        vec![32, 1024],
        vec![16, 2048, 768],
        vec![8, 4096, 1024],
    ];
    
    let activation_report = profiler.profile_activation_functions(&shapes)?;
    
    // Group by activation type and find the fastest
    let mut activation_performance = std::collections::HashMap::new();
    
    for measurement in &activation_report.measurements {
        let parts: Vec<&str> = measurement.configuration.split('_').collect();
        if let Some(activation_name) = parts.first() {
            let entry = activation_performance.entry(activation_name.to_string())
                .or_insert_with(Vec::new);
            entry.push(measurement);
        }
    }
    
    println!("  Activation function rankings (by average performance):");
    let mut avg_times: Vec<(String, f64)> = activation_performance.iter()
        .map(|(name, measurements)| {
            let avg_time = measurements.iter()
                .map(|m| m.avg_duration.as_secs_f64())
                .sum::<f64>() / measurements.len() as f64;
            (name.clone(), avg_time)
        })
        .collect();
    
    avg_times.sort_by(|a, b| a.1.partial_cmp(&b.1).unwrap());
    
    for (i, (name, avg_time)) in avg_times.iter().enumerate() {
        let rank_emoji = match i {
            0 => "🥇",
            1 => "🥈",
            2 => "🥉",
            _ => "  ",
        };
        println!("  {} {}: {:.2}μs average", rank_emoji, name, avg_time * 1_000_000.0);
    }
    
    Ok(())
}

fn analyze_normalization_performance(profiler: &mut LayerProfiler) -> Result<(), TensorError> {
    println!("\n📏 Normalization Layer Performance Analysis");
    
    let shapes = vec![
        vec![32, 768],
        vec![16, 1024, 512],
        vec![8, 2048, 768],
    ];
    
    let norm_report = profiler.profile_normalization_layers(&shapes)?;
    
    // Compare LayerNorm vs RMSNorm performance
    let layernorm_measurements: Vec<_> = norm_report.measurements.iter()
        .filter(|m| m.operation_name == "layernorm_forward")
        .collect();
    
    let rmsnorm_measurements: Vec<_> = norm_report.measurements.iter()
        .filter(|m| m.operation_name == "rmsnorm_forward")
        .collect();
    
    if !layernorm_measurements.is_empty() && !rmsnorm_measurements.is_empty() {
        let layernorm_vec: Vec<PerformanceMeasurement> = layernorm_measurements.iter().map(|m| (*m).clone()).collect();
        let rmsnorm_vec: Vec<PerformanceMeasurement> = rmsnorm_measurements.iter().map(|m| (*m).clone()).collect();
        if let Some(comparison) = PerformanceAnalyzer::compare_performance(
            &layernorm_vec,
            &rmsnorm_vec,
        ) {
            println!("  LayerNorm vs RMSNorm comparison:");
            println!("    • RMSNorm is {:.2}x faster than LayerNorm", comparison.speedup);
            println!("    • Performance improvement: {:.1}%", comparison.improvement_percent);
            println!("    • Statistically significant: {}", 
                    if comparison.is_significant { "Yes ✅" } else { "No ❌" });
        }
    }
    
    Ok(())
}

fn perform_comparison_analysis(profiler: &mut LayerProfiler) -> Result<(), TensorError> {
    println!("\n⚖️  Comparing different layer configurations...");
    
    // Compare standard vs gated feedforward networks
    let ffn_report = profiler.profile_feedforward_networks(&[768], &[2048])?;
    
    let standard_ffn: Vec<_> = ffn_report.measurements.iter()
        .filter(|m| m.configuration.contains("standard"))
        .collect();
    
    let gated_ffn: Vec<_> = ffn_report.measurements.iter()
        .filter(|m| m.configuration.contains("gated"))
        .collect();
    
    if !standard_ffn.is_empty() && !gated_ffn.is_empty() {
        let standard_vec: Vec<PerformanceMeasurement> = standard_ffn.iter().map(|m| (*m).clone()).collect();
        let gated_vec: Vec<PerformanceMeasurement> = gated_ffn.iter().map(|m| (*m).clone()).collect();
        if let Some(comparison) = PerformanceAnalyzer::compare_performance(&standard_vec, &gated_vec) {
            println!("  Standard FFN vs Gated FFN (SwiGLU):");
            println!("    • Speed difference: {:.2}x", comparison.speedup);
            println!("    • Performance change: {:.1}%", comparison.improvement_percent);
            
            // Calculate parameter efficiency
            if let (Some(std_params), Some(gated_params)) = (
                standard_ffn.first().and_then(|m| m.additional_metrics.get("parameters")),
                gated_ffn.first().and_then(|m| m.additional_metrics.get("parameters"))
            ) {
                let param_ratio = gated_params / std_params;
                println!("    • Parameter ratio: {:.2}x more parameters in gated FFN", param_ratio);
                
                let efficiency = comparison.speedup / param_ratio;
                println!("    • Parameter efficiency: {:.3}", efficiency);
            }
        }
    }
    
    Ok(())
}

fn analyze_memory_usage() -> Result<(), TensorError> {
    println!("\n💾 Analyzing memory usage patterns...");
    
    let mut metrics_collector = MetricsCollector::new();
    metrics_collector.start();
    
    // Simulate some tensor operations
    let shapes = [
        Shape::new(vec![1, 512, 768]),
        Shape::new(vec![4, 1024, 512]),
        Shape::new(vec![8, 2048, 1024]),
    ];
    
    for (i, shape) in shapes.iter().enumerate() {
        let tensor: CpuTensor<f32> = CpuTensorFactory::zeros(shape)?;
        let size = shape.size() * std::mem::size_of::<f32>();
        metrics_collector.record_allocation(size);
        
        println!("  Tensor {}: shape {:?}, size: {:.2} MB", 
                i + 1, shape.dims(), size as f64 / 1_000_000.0);
    }
    
    let final_metrics = metrics_collector.finish();
    
    println!("  Memory usage summary:");
    println!("    • Total allocations: {}", final_metrics.allocation_count);
    println!("    • Total allocated: {:.2} MB", final_metrics.total_allocated as f64 / 1_000_000.0);
    println!("    • Peak usage: {:.2} MB", final_metrics.peak_memory_usage as f64 / 1_000_000.0);
    println!("    • Wall time: {:.2}ms", final_metrics.wall_time.as_millis());
    
    Ok(())
}
