//! Sequential Layer Container
//!
//! This module provides the Sequential layer, which allows composing multiple layers
//! in a sequential manner. The Sequential layer applies each layer in order,
//! passing the output of one layer as input to the next.
//!
//! # Features
//!
//! - **Layer Composition**: Combine multiple layers into a single sequential unit
//! - **Forward Propagation**: Automatic chaining of layer outputs to inputs
//! - **Parameter Management**: Aggregate parameter counting and initialization
//! - **Training Mode**: Propagate training mode to all contained layers
//! - **Type Safety**: Ensure compatible input/output types between layers
//!
//! # Example
//!
//! ```rust
//! use qilin_inference::layers::*;
//! use qilin_inference::tensor::cpu::CpuTensor;
//!
//! // Create individual layers
//! let linear1 = Linear::new(LinearConfig::new(784, 256))?;
//! let activation = Activation::new(ActivationType::ReLU);
//! let linear2 = Linear::new(LinearConfig::new(256, 10))?;
//!
//! // Compose into sequential network
//! let mut network = Sequential::new()
//!     .add_layer(Box::new(linear1))
//!     .add_layer(Box::new(activation))
//!     .add_layer(Box::new(linear2));
//!
//! // Forward pass
//! let input = CpuTensor::randn(&[32, 784])?;
//! let output = network.forward(input)?;
//! ```

use std::marker::PhantomData;
use serde::{Serialize, Deserialize};
use crate::error::{TensorError, ErrorContext};
use crate::tensor::{Tensor, TensorOps, Numeric, Shape, cpu::CpuTensor};
use crate::layers::{Layer, ConfigurableLayer, ParameterizedLayer, ParameterInit};

/// Sequential layer configuration.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SequentialConfig {
    /// Layer configurations in order.
    pub layers: Vec<serde_json::Value>, // Generic layer configs
    /// Whether to validate layer compatibility.
    pub validate_compatibility: bool,
}

impl SequentialConfig {
    /// Create a new sequential configuration.
    pub fn new() -> Self {
        Self {
            layers: Vec::new(),
            validate_compatibility: true,
        }
    }

    /// Add a layer configuration.
    pub fn add_layer_config(mut self, config: serde_json::Value) -> Self {
        self.layers.push(config);
        self
    }

    /// Set compatibility validation.
    pub fn with_validation(mut self, validate: bool) -> Self {
        self.validate_compatibility = validate;
        self
    }
}

impl Default for SequentialConfig {
    fn default() -> Self {
        Self::new()
    }
}

/// Sequential layer that applies multiple layers in sequence.
///
/// The Sequential layer maintains a vector of layers and applies them
/// in order during forward propagation. Each layer's output becomes
/// the input to the next layer.
pub struct Sequential<T: Numeric> {
    /// The layers in sequential order.
    layers: Vec<Box<dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>>>,
    /// Configuration.
    config: SequentialConfig,
    /// Training mode.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> Sequential<T> {
    /// Create a new empty sequential layer.
    pub fn new() -> Self {
        Self {
            layers: Vec::new(),
            config: SequentialConfig::new(),
            training: true,
            _phantom: PhantomData,
        }
    }

    /// Create a sequential layer from configuration.
    pub fn from_config(config: SequentialConfig) -> Self {
        Self {
            layers: Vec::new(),
            config,
            training: true,
            _phantom: PhantomData,
        }
    }

    /// Add a layer to the sequence.
    pub fn add_layer(mut self, layer: Box<dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>>) -> Self {
        self.layers.push(layer);
        self
    }

    /// Insert a layer at a specific position.
    pub fn insert_layer(&mut self, index: usize, layer: Box<dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>>) -> Result<(), TensorError> {
        if index > self.layers.len() {
            return Err(TensorError::IndexOutOfBounds {
                index,
                size: self.layers.len(),
                context: Some(ErrorContext::new("insert_layer", "layers::sequential")
                    .with_info("operation", "insert layer at index")),
            });
        }
        self.layers.insert(index, layer);
        Ok(())
    }

    /// Remove a layer at a specific position.
    pub fn remove_layer(&mut self, index: usize) -> Result<Box<dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>>, TensorError> {
        if index >= self.layers.len() {
            return Err(TensorError::IndexOutOfBounds {
                index,
                size: self.layers.len(),
                context: Some(ErrorContext::new("remove_layer", "layers::sequential")
                    .with_info("operation", "remove layer at index")),
            });
        }
        Ok(self.layers.remove(index))
    }

    /// Get the number of layers.
    pub fn len(&self) -> usize {
        self.layers.len()
    }

    /// Check if the sequential layer is empty.
    pub fn is_empty(&self) -> bool {
        self.layers.is_empty()
    }

    /// Get a reference to a layer at a specific index.
    pub fn get_layer(&self, index: usize) -> Option<&dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>> {
        self.layers.get(index).map(|layer| layer.as_ref())
    }

    /// Get a mutable reference to a layer at a specific index.
    /// Note: This method is commented out due to lifetime issues with trait objects.
    /// Use direct access to layers vector if needed.
    // pub fn get_layer_mut(&mut self, index: usize) -> Option<&mut (dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError> + '_)> {
    //     self.layers.get_mut(index).map(|layer| &mut **layer)
    // }

    /// Clear all layers.
    pub fn clear(&mut self) {
        self.layers.clear();
    }

    /// Validate layer compatibility (input/output shapes).
    pub fn validate_compatibility(&self) -> Result<(), TensorError> {
        if !self.config.validate_compatibility {
            return Ok(());
        }

        // Note: In a real implementation, we would check that each layer's
        // output shape is compatible with the next layer's expected input shape.
        // For now, we assume compatibility since we're using the same tensor type.
        
        if self.layers.is_empty() {
            return Err(TensorError::DataTypeIncompatible {
                operation: "validate_compatibility".to_string(),
                dtype: "empty_sequential".to_string(),
                context: Some(ErrorContext::new("validate_compatibility", "layers::sequential")
                    .with_info("error", "Sequential layer cannot be empty")),
            });
        }

        Ok(())
    }

    /// Apply a function to each layer.
    pub fn for_each_layer<F>(&mut self, mut f: F) -> Result<(), TensorError>
    where
        F: FnMut(&mut dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>) -> Result<(), TensorError>,
    {
        for layer in &mut self.layers {
            f(layer.as_mut())?;
        }
        Ok(())
    }

    /// Get layer information summary.
    pub fn layer_summary(&self) -> Vec<(usize, String, usize)> {
        self.layers
            .iter()
            .enumerate()
            .map(|(i, layer)| {
                (i, format!("Layer_{}", i), layer.parameter_count())
            })
            .collect()
    }
}

impl<T: Numeric> Default for Sequential<T> {
    fn default() -> Self {
        Self::new()
    }
}

impl<T: Numeric> Layer<T> for Sequential<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through all layers in sequence.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        if self.layers.is_empty() {
            return Err(TensorError::DataTypeIncompatible {
                operation: "forward".to_string(),
                dtype: "empty_sequential".to_string(),
                context: Some(ErrorContext::new("forward", "layers::sequential")
                    .with_info("error", "Cannot forward through empty Sequential layer")),
            });
        }

        let mut current_input = input;
        
        for (i, layer) in self.layers.iter().enumerate() {
            current_input = layer.forward(current_input)
                .map_err(|e| TensorError::DataTypeIncompatible {
                    operation: format!("forward_layer_{}", i),
                    dtype: "sequential_forward".to_string(),
                    context: Some(ErrorContext::new("forward", "layers::sequential")
                        .with_info("layer_index", &i.to_string())
                        .with_info("original_error", &e.to_string())),
                })?;
        }

        Ok(current_input)
    }

    /// Get the total number of parameters across all layers.
    fn parameter_count(&self) -> usize {
        self.layers.iter().map(|layer| layer.parameter_count()).sum()
    }

    /// Check if any layer is in training mode.
    fn training(&self) -> bool {
        self.training
    }

    /// Set training mode for all layers.
    fn set_training(&mut self, training: bool) {
        self.training = training;
        for layer in &mut self.layers {
            layer.set_training(training);
        }
    }
}

impl<T: Numeric> ConfigurableLayer<T, SequentialConfig> for Sequential<T> {
    fn new(config: SequentialConfig) -> Result<Self, Self::Error> {
        let sequential = Self::from_config(config);
        sequential.validate_compatibility()?;
        Ok(sequential)
    }

    fn config(&self) -> &SequentialConfig {
        &self.config
    }

    fn update_config(&mut self, config: SequentialConfig) -> Result<(), Self::Error> {
        self.config = config;
        self.validate_compatibility()
    }
}

impl<T: Numeric> ParameterizedLayer<T> for Sequential<T> {
    fn init_parameters(&mut self, _init_strategy: ParameterInit) -> Result<(), Self::Error> {
        // Note: We cannot easily downcast trait objects to check if they implement
        // ParameterizedLayer. In a real implementation, we might need to store
        // additional metadata or use a different approach.
        // For now, we'll skip parameter initialization for Sequential layers.
        // Individual layers should be initialized before being added to Sequential.
        Ok(())
    }
}
