//! Dropout layer implementation.
//!
//! This module provides a dropout layer implementation that randomly sets input
//! elements to zero during training to prevent overfitting. During inference,
//! the layer acts as an identity function.
//!
//! The dropout layer applies the following transformation:
//! - Training mode: y = x * mask / (1 - p), where mask is a random binary mask
//! - Inference mode: y = x (identity transformation)
//!
//! The scaling factor (1 - p) ensures that the expected value of the output
//! remains the same during training and inference.

use std::marker::PhantomData;
use std::sync::atomic::{AtomicU64, Ordering};
use crate::error::TensorError;
use crate::tensor::{Tensor, TensorOps, TensorFactory, Numeric, Shape, cpu::{CpuTensor, CpuTensorFactory}};
use crate::layers::{Layer, ConfigurableLayer, ParameterizedLayer, ParameterInit};

/// Configuration for dropout layers.
#[derive(Debug, Clone)]
pub struct DropoutConfig {
    /// Dropout probability (0.0 to 1.0).
    pub p: f32,
    /// Whether to apply dropout during training.
    pub training: bool,
    /// Random seed for reproducible results (optional).
    pub seed: Option<u64>,
}

impl DropoutConfig {
    /// Create a new dropout configuration.
    pub fn new(p: f32) -> Self {
        assert!(p >= 0.0 && p <= 1.0, "Dropout probability must be between 0.0 and 1.0");
        Self {
            p,
            training: true,
            seed: None,
        }
    }

    /// Set the training mode.
    pub fn with_training(mut self, training: bool) -> Self {
        self.training = training;
        self
    }

    /// Set the random seed for reproducible results.
    pub fn with_seed(mut self, seed: u64) -> Self {
        self.seed = Some(seed);
        self
    }
}

impl Default for DropoutConfig {
    fn default() -> Self {
        Self::new(0.5)
    }
}

/// Dropout layer for regularization.
///
/// Dropout randomly sets input elements to zero during training with probability `p`,
/// and scales the remaining elements by `1/(1-p)` to maintain the expected value.
/// During inference, the layer acts as an identity function.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{Dropout, DropoutConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create a dropout layer with 50% dropout probability
/// let config = DropoutConfig::new(0.5);
/// let mut layer = Dropout::new(config).unwrap();
///
/// // Set to training mode
/// layer.set_training(true);
///
/// // Forward pass
/// let input = CpuTensor::ones(&Shape::new(vec![32, 128])).unwrap();
/// let output = layer.forward(input).unwrap();
/// ```
#[derive(Debug)]
pub struct Dropout<T: Numeric> {
    /// Dropout configuration.
    config: DropoutConfig,
    /// Training mode flag.
    training: bool,
    /// Random number generator state (using AtomicU64 for thread-safe interior mutability).
    rng_state: AtomicU64,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> Dropout<T> {
    /// Create a new dropout layer.
    pub fn new(config: DropoutConfig) -> Result<Self, TensorError> {
        let rng_state = config.seed.unwrap_or_else(|| {
            use std::time::{SystemTime, UNIX_EPOCH};
            SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_nanos() as u64
        });

        Ok(Self {
            training: config.training,
            config,
            rng_state: AtomicU64::new(rng_state),
            _phantom: PhantomData,
        })
    }

    /// Get the dropout probability.
    pub fn p(&self) -> f32 {
        self.config.p
    }

    /// Generate a random mask for dropout.
    fn generate_mask(&self, shape: &Shape) -> Result<CpuTensor<T>, TensorError> {
        let size = shape.size();
        let mut mask_data = Vec::with_capacity(size);

        // Simple linear congruential generator for reproducible results
        let a = 1664525u64;
        let c = 1013904223u64;
        let m = 1u64 << 32;

        let keep_prob = 1.0 - self.config.p;
        let scale = T::from_f32(1.0 / keep_prob);

        for _ in 0..size {
            let current_state = self.rng_state.load(Ordering::Relaxed);
            let new_state = (a.wrapping_mul(current_state).wrapping_add(c)) % m;
            self.rng_state.store(new_state, Ordering::Relaxed);
            let random_val = (new_state as f32) / (m as f32);

            if random_val < keep_prob {
                mask_data.push(scale); // Keep and scale
            } else {
                mask_data.push(T::ZERO); // Drop
            }
        }

        CpuTensor::from_data(mask_data, shape.clone())
    }
}

impl<T: Numeric> Clone for Dropout<T> {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            training: self.training,
            rng_state: AtomicU64::new(self.rng_state.load(Ordering::Relaxed)),
            _phantom: PhantomData,
        }
    }
}

impl<T: Numeric> Layer<T> for Dropout<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the dropout layer.
    ///
    /// During training, randomly sets elements to zero with probability `p`
    /// and scales remaining elements by `1/(1-p)`.
    /// During inference, acts as identity function.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        if !self.training || self.config.p == 0.0 {
            // Inference mode or no dropout - return input unchanged
            return Ok(input);
        }

        if self.config.p == 1.0 {
            // Complete dropout - return zeros
            return <CpuTensorFactory as TensorFactory<T>>::zeros(input.shape());
        }

        // Training mode with dropout
        let mask = self.generate_mask(input.shape())?;
        input.mul(&mask)
    }

    fn parameter_count(&self) -> usize {
        0 // Dropout has no learnable parameters
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

impl<T: Numeric> ConfigurableLayer<T, DropoutConfig> for Dropout<T> {
    fn new(config: DropoutConfig) -> Result<Self, Self::Error> {
        Self::new(config)
    }

    fn config(&self) -> &DropoutConfig {
        &self.config
    }

    fn update_config(&mut self, config: DropoutConfig) -> Result<(), Self::Error> {
        // Update the configuration
        self.config = config;

        // Update training mode if specified in config
        self.training = self.config.training;

        // Update RNG state if seed changed
        if let Some(seed) = self.config.seed {
            self.rng_state.store(seed, Ordering::Relaxed);
        }

        Ok(())
    }
}

impl<T: Numeric> ParameterizedLayer<T> for Dropout<T> {
    fn init_parameters(&mut self, _init_strategy: ParameterInit) -> Result<(), Self::Error> {
        // Dropout has no parameters to initialize
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::{Shape, TensorOps};

    #[test]
    fn test_dropout_creation() {
        let config = DropoutConfig::new(0.5);
        let layer = Dropout::<f32>::new(config).unwrap();

        assert_eq!(layer.p(), 0.5);
        assert!(layer.training());
        assert_eq!(layer.parameter_count(), 0);
    }

    #[test]
    fn test_dropout_inference_mode() {
        let config = DropoutConfig::new(0.5);
        let mut layer = Dropout::<f32>::new(config).unwrap();
        layer.set_training(false);

        let input_data = vec![1.0, 2.0, 3.0, 4.0];
        let input = CpuTensor::from_data(input_data.clone(), Shape::new(vec![2, 2])).unwrap();

        let output = layer.forward(input).unwrap();

        // In inference mode, output should be identical to input
        assert_eq!(output.to_vec(), input_data);
    }

    #[test]
    fn test_dropout_no_dropout() {
        let config = DropoutConfig::new(0.0);
        let mut layer = Dropout::<f32>::new(config).unwrap();
        layer.set_training(true);

        let input_data = vec![1.0, 2.0, 3.0, 4.0];
        let input = CpuTensor::from_data(input_data.clone(), Shape::new(vec![2, 2])).unwrap();

        let output = layer.forward(input).unwrap();

        // With p=0.0, output should be identical to input
        assert_eq!(output.to_vec(), input_data);
    }

    #[test]
    fn test_dropout_complete_dropout() {
        let config = DropoutConfig::new(1.0);
        let mut layer = Dropout::<f32>::new(config).unwrap();
        layer.set_training(true);

        let input_data = vec![1.0, 2.0, 3.0, 4.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 2])).unwrap();

        let output = layer.forward(input).unwrap();

        // With p=1.0, all outputs should be zero
        assert_eq!(output.to_vec(), vec![0.0, 0.0, 0.0, 0.0]);
    }

    #[test]
    fn test_dropout_training_mode() {
        let config = DropoutConfig::new(0.5).with_seed(42);
        let mut layer = Dropout::<f32>::new(config).unwrap();
        layer.set_training(true);

        let input_data = vec![1.0; 100]; // Large enough to see statistical effects
        let input = CpuTensor::from_data(input_data, Shape::new(vec![100])).unwrap();

        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        // Check that some elements are zero (dropped)
        let zero_count = output_data.iter().filter(|&&x| x == 0.0).count();
        assert!(zero_count > 0, "Some elements should be dropped");

        // Check that non-zero elements are scaled
        let non_zero_values: Vec<f32> = output_data.iter().filter(|&&x| x != 0.0).cloned().collect();
        if !non_zero_values.is_empty() {
            // With p=0.5, scaling factor should be 1/(1-0.5) = 2.0
            assert!((non_zero_values[0] - 2.0).abs() < 1e-6);
        }
    }

    #[test]
    fn test_dropout_config_update() {
        let config = DropoutConfig::new(0.3);
        let mut layer = Dropout::<f32>::new(config).unwrap();

        assert_eq!(layer.p(), 0.3);

        let new_config = DropoutConfig::new(0.7);
        layer.update_config(new_config).unwrap();

        assert_eq!(layer.p(), 0.7);
    }

    #[test]
    fn test_dropout_reproducibility() {
        let config1 = DropoutConfig::new(0.5).with_seed(123);
        let mut layer1 = Dropout::<f32>::new(config1).unwrap();
        layer1.set_training(true);

        let config2 = DropoutConfig::new(0.5).with_seed(123);
        let mut layer2 = Dropout::<f32>::new(config2).unwrap();
        layer2.set_training(true);

        let input_data = vec![1.0, 2.0, 3.0, 4.0];
        let input1 = CpuTensor::from_data(input_data.clone(), Shape::new(vec![2, 2])).unwrap();
        let input2 = CpuTensor::from_data(input_data, Shape::new(vec![2, 2])).unwrap();

        let output1 = layer1.forward(input1).unwrap();
        let output2 = layer2.forward(input2).unwrap();

        // With same seed, outputs should be identical
        assert_eq!(output1.to_vec(), output2.to_vec());
    }
}
