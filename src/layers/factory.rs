//! Layer Factory
//!
//! This module provides factory patterns for creating layers from configurations.
//! It simplifies layer creation and supports serialization/deserialization of layer configurations.

use std::marker::PhantomData;
use serde::{Serialize, Deserialize};

use crate::error::{TensorError, E<PERSON>r<PERSON>ontext};
use crate::tensor::Numeric;
use crate::layers::*;

/// Unified layer configuration enum that can represent any layer type.
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum LayerConfig {
    /// Linear layer configuration
    Linear {
        input_size: usize,
        output_size: usize,
        bias: bool,
    },
    /// Layer normalization configuration
    LayerNorm {
        normalized_shape: Vec<usize>,
        eps: f32,
        elementwise_affine: bool,
    },
    /// RMS normalization configuration
    RMSNorm {
        normalized_shape: Vec<usize>,
        eps: f32,
        elementwise_affine: bool,
    },
    /// Batch normalization configuration
    BatchNorm {
        num_features: usize,
        eps: f32,
        momentum: f32,
        affine: bool,
        track_running_stats: bool,
    },
    /// Group normalization configuration
    GroupNorm {
        num_groups: usize,
        num_channels: usize,
        eps: f32,
        affine: bool,
    },
    /// Activation function configuration
    Activation {
        activation_type: ActivationType,
    },
    /// GLU configuration
    GLU,
    /// SwiGLU configuration
    SwiGLU,
    /// GeGLU configuration
    GeGLU,
    // Note: Embedding has different Input type (Vec<usize>), removing for now
    /// Positional encoding configuration
    PositionalEncoding {
        encoding_type: PositionEncodingType,
        d_model: usize,
        max_len: usize,
    },
    /// Feed-forward network configuration
    FeedForward {
        input_dim: usize,
        hidden_dim: usize,
        output_dim: usize,
        activation: ActivationType,
        dropout: f32,
        bias: bool,
    },
    // Note: GatedFeedForward is not implemented yet, removing for now
    // Note: Dropout is not fully implemented yet, removing for now
}

/// Layer factory for creating layers from configurations.
pub struct LayerFactory<T: Numeric> {
    _phantom: PhantomData<T>,
}

impl<T: Numeric> LayerFactory<T> {
    /// Create a new layer factory.
    pub fn new() -> Self {
        Self {
            _phantom: PhantomData,
        }
    }

    /// Create a layer from configuration.
    pub fn create_layer(&self, config: LayerConfig) -> Result<Box<dyn Layer<T, Input = crate::tensor::cpu::CpuTensor<T>, Output = crate::tensor::cpu::CpuTensor<T>, Error = TensorError>>, TensorError> {
        match config {
            LayerConfig::Linear { input_size, output_size, bias } => {
                let linear_config = LinearConfig::new(input_size, output_size).with_bias(bias);
                let layer = Linear::new(linear_config)?;
                Ok(Box::new(layer))
            },
            LayerConfig::LayerNorm { normalized_shape, eps, elementwise_affine } => {
                let norm_config = LayerNormConfig::new(normalized_shape)
                    .with_eps(eps)
                    .with_elementwise_affine(elementwise_affine);
                let layer = LayerNorm::new(norm_config)?;
                Ok(Box::new(layer))
            },
            LayerConfig::RMSNorm { normalized_shape, eps, elementwise_affine } => {
                let norm_config = RMSNormConfig::new(normalized_shape)
                    .with_eps(eps)
                    .with_elementwise_affine(elementwise_affine);
                let layer = RMSNorm::new(norm_config)?;
                Ok(Box::new(layer))
            },
            LayerConfig::BatchNorm { num_features, eps, momentum, affine, track_running_stats } => {
                let norm_config = BatchNormConfig::new(num_features)
                    .with_eps(eps)
                    .with_momentum(momentum)
                    .with_affine(affine)
                    .with_track_running_stats(track_running_stats);
                let layer = BatchNorm::new(norm_config)?;
                Ok(Box::new(layer))
            },
            LayerConfig::GroupNorm { num_groups, num_channels, eps, affine } => {
                let norm_config = GroupNormConfig::new(num_groups, num_channels)?
                    .with_eps(eps)
                    .with_affine(affine);
                let layer = GroupNorm::new(norm_config)?;
                Ok(Box::new(layer))
            },
            LayerConfig::Activation { activation_type } => {
                let activation_config = ActivationConfig::new(activation_type);
                let layer = Activation::new(activation_config)?;
                Ok(Box::new(layer))
            },
            LayerConfig::GLU => {
                let layer = GLU::new();
                Ok(Box::new(layer))
            },
            LayerConfig::SwiGLU => {
                let layer = SwiGLU::new();
                Ok(Box::new(layer))
            },
            LayerConfig::GeGLU => {
                let layer = GeGLU::new();
                Ok(Box::new(layer))
            },
            // Embedding has different Input type, skipping for now
            LayerConfig::PositionalEncoding { encoding_type, d_model, max_len } => {
                let pos_config = PositionalEncodingConfig::new(encoding_type, max_len, d_model);
                let layer = PositionalEncoding::new(pos_config)?;
                Ok(Box::new(layer))
            },
            LayerConfig::FeedForward { input_dim, hidden_dim, output_dim, activation, dropout, bias } => {
                let ff_config = FeedForwardConfig::new(input_dim, hidden_dim)
                    .with_output_dim(output_dim)
                    .with_activation(activation)
                    .with_dropout(dropout)
                    .with_bias(bias);
                let layer = FeedForward::new(ff_config)?;
                Ok(Box::new(layer))
            },
            // GatedFeedForward and Dropout are not implemented yet
        }
    }

    /// Create multiple layers from a list of configurations.
    pub fn create_layers(&self, configs: Vec<LayerConfig>) -> Result<Vec<Box<dyn Layer<T, Input = crate::tensor::cpu::CpuTensor<T>, Output = crate::tensor::cpu::CpuTensor<T>, Error = TensorError>>>, TensorError> {
        let mut layers = Vec::new();
        for config in configs {
            layers.push(self.create_layer(config)?);
        }
        Ok(layers)
    }

    /// Create a layer from JSON configuration.
    pub fn create_from_json(&self, json: &str) -> Result<Box<dyn Layer<T, Input = crate::tensor::cpu::CpuTensor<T>, Output = crate::tensor::cpu::CpuTensor<T>, Error = TensorError>>, TensorError> {
        let config: LayerConfig = serde_json::from_str(json)
            .map_err(|e| TensorError::DataTypeIncompatible {
                operation: "parse_json".to_string(),
                dtype: "LayerConfig".to_string(),
                context: Some(ErrorContext::new("create_from_json", "layers::factory")
                    .with_info("error", &e.to_string())),
            })?;
        self.create_layer(config)
    }

    /// Create multiple layers from JSON array.
    pub fn create_layers_from_json(&self, json: &str) -> Result<Vec<Box<dyn Layer<T, Input = crate::tensor::cpu::CpuTensor<T>, Output = crate::tensor::cpu::CpuTensor<T>, Error = TensorError>>>, TensorError> {
        let configs: Vec<LayerConfig> = serde_json::from_str(json)
            .map_err(|e| TensorError::DataTypeIncompatible {
                operation: "parse_json".to_string(),
                dtype: "Vec<LayerConfig>".to_string(),
                context: Some(ErrorContext::new("create_layers_from_json", "layers::factory")
                    .with_info("error", &e.to_string())),
            })?;
        self.create_layers(configs)
    }
}

impl<T: Numeric> Default for LayerFactory<T> {
    fn default() -> Self {
        Self::new()
    }
}

/// Convenience functions for common layer creation patterns.
impl<T: Numeric> LayerFactory<T> {
    /// Create a simple MLP (Multi-Layer Perceptron) with specified dimensions and activation.
    pub fn create_mlp(&self, dims: Vec<usize>, activation: ActivationType, dropout: f32) -> Result<Vec<Box<dyn Layer<T, Input = crate::tensor::cpu::CpuTensor<T>, Output = crate::tensor::cpu::CpuTensor<T>, Error = TensorError>>>, TensorError> {
        if dims.len() < 2 {
            return Err(TensorError::InvalidDimension {
                dimension: dims.len(),
                total_dims: 2,
                context: Some(ErrorContext::new("create_mlp", "layers::factory")
                    .with_info("reason", "MLP requires at least 2 dimensions")),
            });
        }

        let mut layers = Vec::new();
        
        for i in 0..dims.len() - 1 {
            // Add linear layer
            let linear_config = LayerConfig::Linear {
                input_size: dims[i],
                output_size: dims[i + 1],
                bias: true,
            };
            layers.push(self.create_layer(linear_config)?);

            // Add activation (except for the last layer)
            if i < dims.len() - 2 {
                let activation_config = LayerConfig::Activation {
                    activation_type: activation.clone(),
                };
                layers.push(self.create_layer(activation_config)?);

                // Note: Dropout not implemented yet, skipping
            }
        }

        Ok(layers)
    }

    /// Create a transformer feed-forward block.
    pub fn create_transformer_ffn(&self, d_model: usize, d_ff: usize, activation: ActivationType, _dropout: f32) -> Result<Vec<Box<dyn Layer<T, Input = crate::tensor::cpu::CpuTensor<T>, Output = crate::tensor::cpu::CpuTensor<T>, Error = TensorError>>>, TensorError> {
        let configs = vec![
            LayerConfig::Linear {
                input_size: d_model,
                output_size: d_ff,
                bias: true,
            },
            LayerConfig::Activation {
                activation_type: activation,
            },
            // Note: Dropout not implemented yet, skipping
            LayerConfig::Linear {
                input_size: d_ff,
                output_size: d_model,
                bias: true,
            },
            // Note: Dropout not implemented yet, skipping
        ];

        self.create_layers(configs)
    }
}
