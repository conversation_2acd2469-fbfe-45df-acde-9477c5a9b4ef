//! Feed-forward network layers.
//!
//! This module provides feed-forward network implementations commonly used in
//! transformer architectures, including standard FFN and gated FFN variants.
//!
//! All FFN layers support:
//! - Configurable hidden dimensions and activation functions
//! - Dropout for regularization (when implemented)
//! - Efficient computation with proper weight initialization
//! - Support for different gating mechanisms

use std::marker::PhantomData;
use crate::error::{TensorError, ErrorContext};
use crate::tensor::{Tensor, TensorOps, Numeric, Shape, cpu::CpuTensor};
use crate::layers::{Layer, ConfigurableLayer, ParameterizedLayer, ParameterInit};
use crate::layers::{Linear, LinearConfig, Activation, ActivationConfig, ActivationType};

/// Configuration for feed-forward networks.
#[derive(Debug, Clone)]
pub struct FeedForwardConfig {
    /// Input dimension.
    pub input_dim: usize,
    /// Hidden dimension (typically 4x input_dim in transformers).
    pub hidden_dim: usize,
    /// Output dimension (usually same as input_dim).
    pub output_dim: usize,
    /// Activation function for the hidden layer.
    pub activation: ActivationType,
    /// Whether to use bias in linear layers.
    pub bias: bool,
    /// Dropout probability (not implemented yet).
    pub dropout: f32,
    /// Whether to use gated mechanism.
    pub gated: bool,
}

impl FeedForwardConfig {
    /// Create a new feed-forward network configuration.
    pub fn new(input_dim: usize, hidden_dim: usize) -> Self {
        Self {
            input_dim,
            hidden_dim,
            output_dim: input_dim, // Default to same as input
            activation: ActivationType::ReLU,
            bias: true,
            dropout: 0.0,
            gated: false,
        }
    }

    /// Set the output dimension.
    pub fn with_output_dim(mut self, output_dim: usize) -> Self {
        self.output_dim = output_dim;
        self
    }

    /// Set the activation function.
    pub fn with_activation(mut self, activation: ActivationType) -> Self {
        self.activation = activation;
        self
    }

    /// Set whether to use bias.
    pub fn with_bias(mut self, bias: bool) -> Self {
        self.bias = bias;
        self
    }

    /// Set dropout probability.
    pub fn with_dropout(mut self, dropout: f32) -> Self {
        self.dropout = dropout;
        self
    }

    /// Set whether to use gated mechanism.
    pub fn with_gated(mut self, gated: bool) -> Self {
        self.gated = gated;
        self
    }
}

/// Feed-forward network layer.
///
/// Implements a standard feed-forward network with two linear layers and an activation function:
/// FFN(x) = W2 * activation(W1 * x + b1) + b2
///
/// For gated FFN, it implements:
/// GatedFFN(x) = W2 * (activation(W1 * x + b1) ⊙ (W_gate * x + b_gate)) + b2
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{FeedForward, FeedForwardConfig, ActivationType};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create standard FFN: 512 -> 2048 -> 512 with GELU activation
/// let config = FeedForwardConfig::new(512, 2048)
///     .with_activation(ActivationType::GELU);
/// let mut layer = FeedForward::new(config).unwrap();
///
/// // Initialize parameters
/// layer.init_parameters(ParameterInit::XavierUniform).unwrap();
///
/// // Forward pass
/// let input = CpuTensor::randn(&Shape::new(vec![32, 128, 512]), 0.0, 1.0).unwrap();
/// let output = layer.forward(input).unwrap();
/// assert_eq!(output.shape().dims(), &[32, 128, 512]);
/// ```
#[derive(Debug, Clone)]
pub struct FeedForward<T: Numeric> {
    /// First linear layer (input -> hidden).
    linear1: Linear<T>,
    /// Second linear layer (hidden -> output).
    linear2: Linear<T>,
    /// Gate linear layer (for gated FFN).
    gate_linear: Option<Linear<T>>,
    /// Activation function.
    activation: Activation<T>,
    /// Layer configuration.
    config: FeedForwardConfig,
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> FeedForward<T> {
    /// Create a new feed-forward network.
    pub fn new(config: FeedForwardConfig) -> Result<Self, TensorError> {
        // Create linear layers
        let linear1_config = LinearConfig::new(config.input_dim, config.hidden_dim)
            .with_bias(config.bias);
        let linear1 = Linear::new(linear1_config)?;

        let linear2_config = LinearConfig::new(config.hidden_dim, config.output_dim)
            .with_bias(config.bias);
        let linear2 = Linear::new(linear2_config)?;

        // Create gate linear layer if gated
        let gate_linear = if config.gated {
            let gate_config = LinearConfig::new(config.input_dim, config.hidden_dim)
                .with_bias(config.bias);
            Some(Linear::new(gate_config)?)
        } else {
            None
        };

        // Create activation function
        let activation_config = ActivationConfig::new(config.activation.clone());
        let activation = Activation::new(activation_config)?;

        Ok(Self {
            linear1,
            linear2,
            gate_linear,
            activation,
            config,
            training: false,
            _phantom: PhantomData,
        })
    }

    /// Get the input dimension.
    pub fn input_dim(&self) -> usize {
        self.config.input_dim
    }

    /// Get the hidden dimension.
    pub fn hidden_dim(&self) -> usize {
        self.config.hidden_dim
    }

    /// Get the output dimension.
    pub fn output_dim(&self) -> usize {
        self.config.output_dim
    }

    /// Check if gated mechanism is enabled.
    pub fn is_gated(&self) -> bool {
        self.config.gated
    }

    /// Get a reference to the first linear layer.
    pub fn linear1(&self) -> &Linear<T> {
        &self.linear1
    }

    /// Get a reference to the second linear layer.
    pub fn linear2(&self) -> &Linear<T> {
        &self.linear2
    }

    /// Get a reference to the gate linear layer (if present).
    pub fn gate_linear(&self) -> Option<&Linear<T>> {
        self.gate_linear.as_ref()
    }

    /// Get a reference to the activation function.
    pub fn activation(&self) -> &Activation<T> {
        &self.activation
    }
}

impl<T: Numeric> Layer<T> for FeedForward<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the feed-forward network.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        // First linear transformation
        let hidden = self.linear1.forward(input.clone())?;
        
        // Apply activation
        let activated = self.activation.forward(hidden)?;

        // Apply gating if enabled
        let gated = if let Some(ref gate_linear) = self.gate_linear {
            // Compute gate values
            let gate_values = gate_linear.forward(input)?;
            
            // Element-wise multiplication (gating)
            activated.mul(&gate_values)?
        } else {
            activated
        };

        // Second linear transformation
        self.linear2.forward(gated)
    }

    fn parameter_count(&self) -> usize {
        let linear1_params = self.linear1.parameter_count();
        let linear2_params = self.linear2.parameter_count();
        let gate_params = self.gate_linear.as_ref()
            .map(|gate| gate.parameter_count())
            .unwrap_or(0);
        let activation_params = self.activation.parameter_count();
        
        linear1_params + linear2_params + gate_params + activation_params
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
        self.linear1.set_training(training);
        self.linear2.set_training(training);
        if let Some(ref mut gate_linear) = self.gate_linear {
            gate_linear.set_training(training);
        }
        self.activation.set_training(training);
    }
}

impl<T: Numeric> ConfigurableLayer<T, FeedForwardConfig> for FeedForward<T> {
    fn new(config: FeedForwardConfig) -> Result<Self, Self::Error> {
        Self::new(config)
    }

    fn config(&self) -> &FeedForwardConfig {
        &self.config
    }

    fn update_config(&mut self, config: FeedForwardConfig) -> Result<(), Self::Error> {
        // Check if the new configuration is compatible
        if config.input_dim != self.config.input_dim ||
           config.hidden_dim != self.config.hidden_dim ||
           config.output_dim != self.config.output_dim ||
           config.gated != self.config.gated {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.config.input_dim, self.config.hidden_dim, self.config.output_dim],
                actual: vec![config.input_dim, config.hidden_dim, config.output_dim],
                context: Some(ErrorContext::new("update_config", "layers::feedforward")
                    .with_info("reason", "core dimensions cannot be changed after initialization")),
            });
        }

        self.config = config;
        Ok(())
    }
}

impl<T: Numeric> ParameterizedLayer<T> for FeedForward<T> {
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), Self::Error> {
        // Initialize linear layers
        self.linear1.init_parameters(init_strategy.clone())?;
        self.linear2.init_parameters(init_strategy.clone())?;
        
        // Initialize gate linear layer if present
        if let Some(ref mut gate_linear) = self.gate_linear {
            gate_linear.init_parameters(init_strategy)?;
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::TensorOps;

    #[test]
    fn test_feedforward_creation() {
        let config = FeedForwardConfig::new(512, 2048);
        let layer = FeedForward::<f32>::new(config).unwrap();

        assert_eq!(layer.input_dim(), 512);
        assert_eq!(layer.hidden_dim(), 2048);
        assert_eq!(layer.output_dim(), 512); // Default same as input
        assert!(!layer.is_gated());

        // Parameter count: (512 * 2048 + 2048) + (2048 * 512 + 512)
        let expected_params = (512 * 2048 + 2048) + (2048 * 512 + 512);
        assert_eq!(layer.parameter_count(), expected_params);
    }

    #[test]
    fn test_feedforward_forward() {
        let config = FeedForwardConfig::new(4, 8)
            .with_activation(ActivationType::ReLU);
        let mut layer = FeedForward::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::XavierUniform).unwrap();

        // Create input: (batch_size=2, seq_len=3, input_dim=4)
        let input_data = vec![1.0; 2 * 3 * 4];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 3, 4])).unwrap();

        let output = layer.forward(input).unwrap();

        // Check output shape: (batch_size=2, seq_len=3, output_dim=4)
        assert_eq!(output.shape().dims(), &[2, 3, 4]);
    }

    #[test]
    fn test_gated_feedforward() {
        let config = FeedForwardConfig::new(4, 8)
            .with_activation(ActivationType::GELU)
            .with_gated(true);
        let mut layer = FeedForward::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::XavierUniform).unwrap();

        assert!(layer.is_gated());
        assert!(layer.gate_linear().is_some());

        // Parameter count should include gate linear layer
        // linear1: 4 * 8 + 8 = 40, linear2: 8 * 4 + 4 = 36, gate_linear: 4 * 8 + 8 = 40
        let expected_params = (4 * 8 + 8) + (8 * 4 + 4) + (4 * 8 + 8);
        assert_eq!(layer.parameter_count(), expected_params);

        // Test forward pass
        let input_data = vec![0.5; 2 * 4];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 4])).unwrap();

        let output = layer.forward(input).unwrap();
        assert_eq!(output.shape().dims(), &[2, 4]);
    }

    #[test]
    fn test_feedforward_different_output_dim() {
        let config = FeedForwardConfig::new(6, 12)
            .with_output_dim(8)
            .with_activation(ActivationType::Swish);
        let mut layer = FeedForward::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::KaimingUniform).unwrap();

        assert_eq!(layer.input_dim(), 6);
        assert_eq!(layer.hidden_dim(), 12);
        assert_eq!(layer.output_dim(), 8);

        let input_data = vec![0.1; 1 * 6];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![1, 6])).unwrap();

        let output = layer.forward(input).unwrap();
        assert_eq!(output.shape().dims(), &[1, 8]);
    }

    #[test]
    fn test_feedforward_no_bias() {
        let config = FeedForwardConfig::new(3, 6)
            .with_bias(false);
        let layer = FeedForward::<f32>::new(config).unwrap();

        // Parameter count without bias: 3 * 6 + 6 * 3 = 36
        let expected_params = 3 * 6 + 6 * 3;
        assert_eq!(layer.parameter_count(), expected_params);
    }

    #[test]
    fn test_feedforward_training_mode() {
        let config = FeedForwardConfig::new(4, 8);
        let mut layer = FeedForward::<f32>::new(config).unwrap();

        assert!(!layer.training());

        layer.set_training(true);
        assert!(layer.training());
        assert!(layer.linear1().training());
        assert!(layer.linear2().training());
        assert!(layer.activation().training());
    }

    #[test]
    fn test_feedforward_parameter_initialization() {
        let config = FeedForwardConfig::new(4, 8);
        let mut layer = FeedForward::<f32>::new(config).unwrap();

        // Initialize with specific strategy
        layer.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.1 }).unwrap();

        // Check that parameters are not all zeros
        let linear1_weight = layer.linear1().weight().to_vec();
        assert!(linear1_weight.iter().any(|&x| x.abs() > 1e-6));

        let linear2_weight = layer.linear2().weight().to_vec();
        assert!(linear2_weight.iter().any(|&x| x.abs() > 1e-6));
    }
}
