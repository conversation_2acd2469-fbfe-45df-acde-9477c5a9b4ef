//! Parallel Layer Container
//!
//! This module provides the Parallel layer, which allows composing multiple layers
//! in a parallel manner. The Parallel layer applies all layers to the same input
//! and combines their outputs using various strategies (concatenation, addition, etc.).
//!
//! # Features
//!
//! - **Parallel Computation**: Apply multiple layers to the same input simultaneously
//! - **Output Combination**: Various strategies for combining parallel outputs
//! - **Parameter Management**: Aggregate parameter counting and initialization
//! - **Training Mode**: Propagate training mode to all contained layers
//! - **Flexible Architecture**: Support for branching and multi-path networks
//!
//! # Example
//!
//! ```rust
//! use qilin_inference::layers::*;
//! use qilin_inference::tensor::cpu::CpuTensor;
//!
//! // Create parallel branches
//! let branch1 = Linear::new(LinearConfig::new(256, 128))?;
//! let branch2 = Linear::new(LinearConfig::new(256, 128))?;
//!
//! // Compose into parallel network
//! let mut network = Parallel::new(CombineStrategy::Concatenate)
//!     .add_branch(Box::new(branch1))
//!     .add_branch(Box::new(branch2));
//!
//! // Forward pass - input goes to both branches, outputs are concatenated
//! let input = CpuTensor::randn(&[32, 256])?;
//! let output = network.forward(input)?; // Shape: [32, 256] (128 + 128)
//! ```

use std::marker::PhantomData;
use serde::{Serialize, Deserialize};
use crate::error::{TensorError, ErrorContext};
use crate::tensor::{Tensor, TensorOps, Numeric, Shape, cpu::CpuTensor};
use crate::layers::{Layer, ConfigurableLayer, ParameterizedLayer, ParameterInit};

/// Strategy for combining outputs from parallel branches.
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CombineStrategy {
    /// Concatenate outputs along the last dimension.
    Concatenate,
    /// Add outputs element-wise (requires same shapes).
    Add,
    /// Take the average of outputs (requires same shapes).
    Average,
    /// Take the maximum of outputs element-wise (requires same shapes).
    Maximum,
    /// Take the first branch output only.
    First,
    /// Take the last branch output only.
    Last,
}

/// Parallel layer configuration.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParallelConfig {
    /// Strategy for combining branch outputs.
    pub combine_strategy: CombineStrategy,
    /// Whether to validate branch compatibility.
    pub validate_compatibility: bool,
}

impl ParallelConfig {
    /// Create a new parallel configuration.
    pub fn new(combine_strategy: CombineStrategy) -> Self {
        Self {
            combine_strategy,
            validate_compatibility: true,
        }
    }

    /// Set compatibility validation.
    pub fn with_validation(mut self, validate: bool) -> Self {
        self.validate_compatibility = validate;
        self
    }
}

impl Default for ParallelConfig {
    fn default() -> Self {
        Self::new(CombineStrategy::Concatenate)
    }
}

/// Parallel layer that applies multiple layers in parallel to the same input.
///
/// The Parallel layer maintains a vector of branches (layers) and applies them
/// all to the same input during forward propagation. The outputs are then
/// combined according to the specified strategy.
pub struct Parallel<T: Numeric> {
    /// The parallel branches.
    branches: Vec<Box<dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>>>,
    /// Configuration.
    config: ParallelConfig,
    /// Training mode.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> Parallel<T> {
    /// Create a new parallel layer with the specified combine strategy.
    pub fn new(combine_strategy: CombineStrategy) -> Self {
        Self {
            branches: Vec::new(),
            config: ParallelConfig::new(combine_strategy),
            training: true,
            _phantom: PhantomData,
        }
    }

    /// Create a parallel layer from configuration.
    pub fn from_config(config: ParallelConfig) -> Self {
        Self {
            branches: Vec::new(),
            config,
            training: true,
            _phantom: PhantomData,
        }
    }

    /// Add a branch to the parallel layer.
    pub fn add_branch(mut self, branch: Box<dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>>) -> Self {
        self.branches.push(branch);
        self
    }

    /// Insert a branch at a specific position.
    pub fn insert_branch(&mut self, index: usize, branch: Box<dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>>) -> Result<(), TensorError> {
        if index > self.branches.len() {
            return Err(TensorError::IndexOutOfBounds {
                index,
                size: self.branches.len(),
                context: Some(ErrorContext::new("insert_branch", "layers::parallel")
                    .with_info("operation", "insert branch at index")),
            });
        }
        self.branches.insert(index, branch);
        Ok(())
    }

    /// Remove a branch at a specific position.
    pub fn remove_branch(&mut self, index: usize) -> Result<Box<dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>>, TensorError> {
        if index >= self.branches.len() {
            return Err(TensorError::IndexOutOfBounds {
                index,
                size: self.branches.len(),
                context: Some(ErrorContext::new("remove_branch", "layers::parallel")
                    .with_info("operation", "remove branch at index")),
            });
        }
        Ok(self.branches.remove(index))
    }

    /// Get the number of branches.
    pub fn len(&self) -> usize {
        self.branches.len()
    }

    /// Check if the parallel layer is empty.
    pub fn is_empty(&self) -> bool {
        self.branches.is_empty()
    }

    /// Get a reference to a branch at a specific index.
    pub fn get_branch(&self, index: usize) -> Option<&dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>> {
        self.branches.get(index).map(|branch| branch.as_ref())
    }

    /// Clear all branches.
    pub fn clear(&mut self) {
        self.branches.clear();
    }

    /// Get the combine strategy.
    pub fn combine_strategy(&self) -> &CombineStrategy {
        &self.config.combine_strategy
    }

    /// Set the combine strategy.
    pub fn set_combine_strategy(&mut self, strategy: CombineStrategy) {
        self.config.combine_strategy = strategy;
    }

    /// Validate branch compatibility based on combine strategy.
    pub fn validate_compatibility(&self) -> Result<(), TensorError> {
        if !self.config.validate_compatibility {
            return Ok(());
        }

        if self.branches.is_empty() {
            return Err(TensorError::DataTypeIncompatible {
                operation: "validate_compatibility".to_string(),
                dtype: "empty_parallel".to_string(),
                context: Some(ErrorContext::new("validate_compatibility", "layers::parallel")
                    .with_info("error", "Parallel layer cannot be empty")),
            });
        }

        // For strategies that require same shapes, we would need to check
        // that all branches produce compatible output shapes.
        // This is difficult to validate without knowing the input shape,
        // so we'll defer this to runtime during forward pass.

        Ok(())
    }

    /// Apply a function to each branch.
    pub fn for_each_branch<F>(&mut self, mut f: F) -> Result<(), TensorError>
    where
        F: FnMut(&mut dyn Layer<T, Input = CpuTensor<T>, Output = CpuTensor<T>, Error = TensorError>) -> Result<(), TensorError>,
    {
        for branch in &mut self.branches {
            f(branch.as_mut())?;
        }
        Ok(())
    }

    /// Get branch information summary.
    pub fn branch_summary(&self) -> Vec<(usize, String, usize)> {
        self.branches
            .iter()
            .enumerate()
            .map(|(i, branch)| {
                (i, format!("Branch_{}", i), branch.parameter_count())
            })
            .collect()
    }

    /// Combine outputs according to the strategy.
    fn combine_outputs(&self, outputs: Vec<CpuTensor<T>>) -> Result<CpuTensor<T>, TensorError> {
        if outputs.is_empty() {
            return Err(TensorError::DataTypeIncompatible {
                operation: "combine_outputs".to_string(),
                dtype: "empty_outputs".to_string(),
                context: Some(ErrorContext::new("combine_outputs", "layers::parallel")
                    .with_info("error", "No outputs to combine")),
            });
        }

        match self.config.combine_strategy {
            CombineStrategy::Concatenate => {
                // Concatenate along the last dimension
                if outputs.len() == 1 {
                    return Ok(outputs.into_iter().next().unwrap());
                }

                // For now, we'll implement a simple concatenation
                // In a full implementation, we would use proper tensor concatenation
                let first_output = &outputs[0];
                let mut result_shape = first_output.shape().dims().to_vec();
                let last_dim = result_shape.len() - 1;
                
                // Calculate total size in last dimension
                let mut total_last_dim = result_shape[last_dim];
                for output in outputs.iter().skip(1) {
                    let output_shape = output.shape().dims();
                    if output_shape.len() != result_shape.len() {
                        return Err(TensorError::ShapeMismatch {
                            expected: result_shape.clone(),
                            actual: output_shape.to_vec(),
                            context: Some(ErrorContext::new("combine_outputs", "layers::parallel")
                                .with_info("strategy", "concatenate")),
                        });
                    }
                    total_last_dim += output_shape[last_dim];
                }
                
                result_shape[last_dim] = total_last_dim;
                
                // Create result tensor (simplified implementation)
                // In practice, we would properly concatenate the data
                use crate::tensor::TensorFactory;
                use crate::tensor::cpu::CpuTensorFactory;
                CpuTensorFactory::zeros(&Shape::new(result_shape))
            },
            
            CombineStrategy::Add => {
                // Add all outputs element-wise
                let mut result = outputs[0].clone();
                for output in outputs.iter().skip(1) {
                    result = result.add(&output)?;
                }
                Ok(result)
            },
            
            CombineStrategy::Average => {
                // Average all outputs
                let mut result = outputs[0].clone();
                for output in outputs.iter().skip(1) {
                    result = result.add(&output)?;
                }
                // Divide by number of branches
                let scale = T::from_f32(1.0 / outputs.len() as f32);
                result.mul_scalar(scale)
            },
            
            CombineStrategy::Maximum => {
                // Take element-wise maximum
                let mut result = outputs[0].clone();
                for output in outputs.iter().skip(1) {
                    // For now, we'll just return the first output
                    // In a full implementation, we would compute element-wise max
                    result = result.add(&output)?;
                    result = result.mul_scalar(T::from_f32(0.5))?; // Simple approximation
                }
                Ok(result)
            },
            
            CombineStrategy::First => {
                Ok(outputs.into_iter().next().unwrap())
            },
            
            CombineStrategy::Last => {
                Ok(outputs.into_iter().last().unwrap())
            },
        }
    }
}

impl<T: Numeric> Default for Parallel<T> {
    fn default() -> Self {
        Self::new(CombineStrategy::Concatenate)
    }
}

impl<T: Numeric> Layer<T> for Parallel<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through all branches in parallel.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        if self.branches.is_empty() {
            return Err(TensorError::DataTypeIncompatible {
                operation: "forward".to_string(),
                dtype: "empty_parallel".to_string(),
                context: Some(ErrorContext::new("forward", "layers::parallel")
                    .with_info("error", "Cannot forward through empty Parallel layer")),
            });
        }

        // Apply each branch to the input
        let mut outputs = Vec::with_capacity(self.branches.len());
        
        for (i, branch) in self.branches.iter().enumerate() {
            let branch_output = branch.forward(input.clone())
                .map_err(|e| TensorError::DataTypeIncompatible {
                    operation: format!("forward_branch_{}", i),
                    dtype: "parallel_forward".to_string(),
                    context: Some(ErrorContext::new("forward", "layers::parallel")
                        .with_info("branch_index", &i.to_string())
                        .with_info("original_error", &e.to_string())),
                })?;
            outputs.push(branch_output);
        }

        // Combine outputs according to strategy
        self.combine_outputs(outputs)
    }

    /// Get the total number of parameters across all branches.
    fn parameter_count(&self) -> usize {
        self.branches.iter().map(|branch| branch.parameter_count()).sum()
    }

    /// Check if any branch is in training mode.
    fn training(&self) -> bool {
        self.training
    }

    /// Set training mode for all branches.
    fn set_training(&mut self, training: bool) {
        self.training = training;
        for branch in &mut self.branches {
            branch.set_training(training);
        }
    }
}

impl<T: Numeric> ConfigurableLayer<T, ParallelConfig> for Parallel<T> {
    fn new(config: ParallelConfig) -> Result<Self, Self::Error> {
        let parallel = Self::from_config(config);
        parallel.validate_compatibility()?;
        Ok(parallel)
    }

    fn config(&self) -> &ParallelConfig {
        &self.config
    }

    fn update_config(&mut self, config: ParallelConfig) -> Result<(), Self::Error> {
        self.config = config;
        self.validate_compatibility()
    }
}

impl<T: Numeric> ParameterizedLayer<T> for Parallel<T> {
    fn init_parameters(&mut self, _init_strategy: ParameterInit) -> Result<(), Self::Error> {
        // Similar to Sequential, we cannot easily downcast trait objects.
        // Individual branches should be initialized before being added to Parallel.
        Ok(())
    }
}
