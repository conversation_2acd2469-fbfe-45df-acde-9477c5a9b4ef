//! Activation functions.
//!
//! This module provides various activation functions commonly used in neural networks,
//! including basic activations (ReLU, GELU, Swish) and gated activations (GLU, SwiGLU).
//!
//! All activation functions support:
//! - Element-wise computation
//! - Configurable parameters where applicable
//! - Efficient implementation with numerical stability
//! - Training/inference mode switching

use std::marker::PhantomData;
use serde::{Serialize, Deserialize};
use crate::error::{TensorError, ErrorContext};
use crate::tensor::{Tensor, TensorOps, Numeric, Shape, cpu::CpuTensor};
use crate::layers::{Layer, ConfigurableLayer};

/// Activation function types.
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ActivationType {
    /// Rectified Linear Unit: max(0, x)
    ReLU,
    /// Gaussian Error Linear Unit
    GELU,
    /// Swish activation: x * sigmoid(x)
    Swish,
    /// Mish activation: x * tanh(softplus(x))
    Mish,
    /// Sigmoid activation: 1 / (1 + exp(-x))
    Sigmoid,
    /// Hyperbolic tangent: tanh(x)
    Tanh,
    /// Leaky ReLU: max(alpha * x, x)
    LeakyReLU { alpha: f32 },
    /// Exponential Linear Unit: x if x > 0, alpha * (exp(x) - 1) if x <= 0
    ELU { alpha: f32 },
    /// Composite activation: combination of multiple activations
    Composite { activations: Vec<ActivationType> },
    /// Custom activation: user-defined activation function
    Custom { name: String, params: std::collections::HashMap<String, f32> },
}

/// Configuration for activation layers.
#[derive(Debug, Clone)]
pub struct ActivationConfig {
    /// Type of activation function.
    pub activation_type: ActivationType,
    /// Whether to apply activation in-place (not implemented yet).
    pub inplace: bool,
}

impl ActivationConfig {
    /// Create a new activation configuration.
    pub fn new(activation_type: ActivationType) -> Self {
        Self {
            activation_type,
            inplace: false,
        }
    }

    /// Set whether to apply activation in-place.
    pub fn with_inplace(mut self, inplace: bool) -> Self {
        self.inplace = inplace;
        self
    }
}

/// Activation function layer.
///
/// Applies an element-wise activation function to the input tensor.
/// The activation function is specified by the `ActivationType`.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{Activation, ActivationConfig, ActivationType};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create ReLU activation
/// let config = ActivationConfig::new(ActivationType::ReLU);
/// let layer = Activation::new(config).unwrap();
///
/// // Forward pass
/// let input = CpuTensor::from_data(vec![-1.0, 0.0, 1.0, 2.0], Shape::new(vec![4])).unwrap();
/// let output = layer.forward(input).unwrap();
/// // Output: [0.0, 0.0, 1.0, 2.0]
/// ```
#[derive(Debug, Clone)]
pub struct Activation<T: Numeric> {
    /// Layer configuration.
    config: ActivationConfig,
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> Activation<T> {
    /// Create a new activation layer.
    pub fn new(config: ActivationConfig) -> Result<Self, TensorError> {
        Ok(Self {
            config,
            training: false,
            _phantom: PhantomData,
        })
    }

    /// Get the activation type.
    pub fn activation_type(&self) -> &ActivationType {
        &self.config.activation_type
    }

    /// Apply the activation function to a single value.
    fn apply_activation(&self, x: T) -> T {
        match &self.config.activation_type {
            ActivationType::ReLU => {
                if x > T::ZERO { x } else { T::ZERO }
            },
            ActivationType::GELU => {
                // GELU approximation: 0.5 * x * (1 + tanh(sqrt(2/π) * (x + 0.044715 * x^3)))
                let x_f32 = x.to_f32();
                let sqrt_2_over_pi = (2.0 / std::f32::consts::PI).sqrt();
                let x_cubed = x_f32 * x_f32 * x_f32;
                let inner = sqrt_2_over_pi * (x_f32 + 0.044715 * x_cubed);
                let result = 0.5 * x_f32 * (1.0 + inner.tanh());
                T::from_f32(result)
            },
            ActivationType::Swish => {
                // Swish: x * sigmoid(x) = x / (1 + exp(-x))
                let x_f32 = x.to_f32();
                let sigmoid = 1.0 / (1.0 + (-x_f32).exp());
                T::from_f32(x_f32 * sigmoid)
            },
            ActivationType::Mish => {
                // Mish: x * tanh(softplus(x)) = x * tanh(ln(1 + exp(x)))
                let x_f32 = x.to_f32();
                let softplus = (1.0 + x_f32.exp()).ln();
                let result = x_f32 * softplus.tanh();
                T::from_f32(result)
            },
            ActivationType::Sigmoid => {
                let x_f32 = x.to_f32();
                let result = 1.0 / (1.0 + (-x_f32).exp());
                T::from_f32(result)
            },
            ActivationType::Tanh => {
                let x_f32 = x.to_f32();
                T::from_f32(x_f32.tanh())
            },
            ActivationType::LeakyReLU { alpha } => {
                if x > T::ZERO {
                    x
                } else {
                    x * T::from_f32(*alpha)
                }
            },
            ActivationType::ELU { alpha } => {
                if x > T::ZERO {
                    x
                } else {
                    let x_f32 = x.to_f32();
                    T::from_f32(*alpha * (x_f32.exp() - 1.0))
                }
            },
            ActivationType::Composite { .. } => {
                // Composite activations should not be used in apply_activation
                // They should be handled by CompositeActivation layer
                panic!("Composite activations should not be used in basic Activation layer")
            },
            ActivationType::Custom { .. } => {
                // Custom activations should not be used in apply_activation
                // They should be handled by CustomActivation layer
                panic!("Custom activations should not be used in basic Activation layer")
            },
        }
    }
}

impl<T: Numeric> Layer<T> for Activation<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the activation layer.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        let input_data = input.data();
        let output_data: Vec<T> = input_data.iter()
            .map(|&x| self.apply_activation(x))
            .collect();

        CpuTensor::from_data(output_data, input.shape().clone())
    }

    fn parameter_count(&self) -> usize {
        0 // Activation functions have no learnable parameters
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

impl<T: Numeric> ConfigurableLayer<T, ActivationConfig> for Activation<T> {
    fn new(config: ActivationConfig) -> Result<Self, Self::Error> {
        Self::new(config)
    }

    fn config(&self) -> &ActivationConfig {
        &self.config
    }

    fn update_config(&mut self, config: ActivationConfig) -> Result<(), Self::Error> {
        self.config = config;
        Ok(())
    }
}

/// Gated Linear Unit (GLU) and variants.
///
/// GLU applies a gating mechanism where the input is split into two halves,
/// one half is passed through an activation function, and then element-wise
/// multiplied with the other half.
///
/// Formula: GLU(x) = (x[:, :d] ⊙ σ(x[:, d:]))
/// where ⊙ is element-wise multiplication and σ is the gate activation.
#[derive(Debug, Clone)]
pub struct GLU<T: Numeric> {
    /// Gate activation function.
    gate_activation: ActivationType,
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> GLU<T> {
    /// Create a new GLU layer with sigmoid gate activation.
    pub fn new() -> Self {
        Self {
            gate_activation: ActivationType::Sigmoid,
            training: false,
            _phantom: PhantomData,
        }
    }

    /// Create a new GLU layer with custom gate activation.
    pub fn with_gate_activation(gate_activation: ActivationType) -> Self {
        Self {
            gate_activation,
            training: false,
            _phantom: PhantomData,
        }
    }

    /// Apply gate activation to a single value.
    fn apply_gate_activation(&self, x: T) -> T {
        let activation = Activation::new(ActivationConfig::new(self.gate_activation.clone())).unwrap();
        activation.apply_activation(x)
    }
}

impl<T: Numeric> Layer<T> for GLU<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the GLU layer.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        let input_shape = input.shape();
        let input_dims = input_shape.dims();
        let last_dim = input_dims[input_dims.len() - 1];

        // Check that the last dimension is even (can be split into two halves)
        if last_dim % 2 != 0 {
            return Err(TensorError::InvalidDimension {
                dimension: last_dim,
                total_dims: 2,
                context: Some(ErrorContext::new("forward", "layers::activation::glu")
                    .with_info("reason", "last dimension must be even for GLU")),
            });
        }

        let half_dim = last_dim / 2;
        let input_data = input.data();
        let batch_size: usize = input_dims[..input_dims.len() - 1].iter().product();

        let mut output_data = Vec::with_capacity(batch_size * half_dim);

        for batch_idx in 0..batch_size {
            let start_idx = batch_idx * last_dim;

            for feat_idx in 0..half_dim {
                let value_idx = start_idx + feat_idx;
                let gate_idx = start_idx + half_dim + feat_idx;

                let value = input_data[value_idx];
                let gate = self.apply_gate_activation(input_data[gate_idx]);

                output_data.push(value * gate);
            }
        }

        // Create output shape with half the last dimension
        let mut output_dims = input_dims.to_vec();
        let last_idx = output_dims.len() - 1;
        output_dims[last_idx] = half_dim;
        let output_shape = Shape::new(output_dims);

        CpuTensor::from_data(output_data, output_shape)
    }

    fn parameter_count(&self) -> usize {
        0 // GLU has no learnable parameters
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::TensorOps;

    #[test]
    fn test_relu_activation() {
        let config = ActivationConfig::new(ActivationType::ReLU);
        let layer = Activation::<f32>::new(config).unwrap();

        let input_data = vec![-2.0, -1.0, 0.0, 1.0, 2.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![5])).unwrap();

        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        assert_eq!(output_data, vec![0.0, 0.0, 0.0, 1.0, 2.0]);
    }

    #[test]
    fn test_gelu_activation() {
        let config = ActivationConfig::new(ActivationType::GELU);
        let layer = Activation::<f32>::new(config).unwrap();

        let input_data = vec![-1.0, 0.0, 1.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![3])).unwrap();

        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        // GELU(0) should be 0
        assert!((output_data[1]).abs() < 1e-6);
        // GELU(-x) should be negative, GELU(x) should be positive
        assert!(output_data[0] < 0.0);
        assert!(output_data[2] > 0.0);
    }

    #[test]
    fn test_swish_activation() {
        let config = ActivationConfig::new(ActivationType::Swish);
        let layer = Activation::<f32>::new(config).unwrap();

        let input_data = vec![-1.0, 0.0, 1.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![3])).unwrap();

        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        // Swish(0) should be 0
        assert!((output_data[1]).abs() < 1e-6);
        // Swish is always non-negative for positive inputs
        assert!(output_data[2] > 0.0);
    }

    #[test]
    fn test_leaky_relu_activation() {
        let config = ActivationConfig::new(ActivationType::LeakyReLU { alpha: 0.1 });
        let layer = Activation::<f32>::new(config).unwrap();

        let input_data = vec![-2.0, -1.0, 0.0, 1.0, 2.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![5])).unwrap();

        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        assert_eq!(output_data, vec![-0.2, -0.1, 0.0, 1.0, 2.0]);
    }

    #[test]
    fn test_sigmoid_activation() {
        let config = ActivationConfig::new(ActivationType::Sigmoid);
        let layer = Activation::<f32>::new(config).unwrap();

        let input_data = vec![-1000.0, 0.0, 1000.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![3])).unwrap();

        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        // Sigmoid should be bounded between 0 and 1
        assert!(output_data[0] >= 0.0 && output_data[0] < 0.1); // Very close to 0 (can be exactly 0.0)
        assert!((output_data[1] - 0.5).abs() < 1e-6); // Sigmoid(0) = 0.5
        assert!(output_data[2] > 0.9 && output_data[2] <= 1.0); // Very close to 1 (can be exactly 1.0)
    }

    #[test]
    fn test_glu_layer() {
        let layer = GLU::<f32>::new();

        // Input with even last dimension (4)
        let input_data = vec![1.0, 2.0, 0.0, 1.0, 3.0, 4.0, -1.0, 2.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 4])).unwrap();

        let output = layer.forward(input).unwrap();

        // Output should have half the last dimension
        assert_eq!(output.shape().dims(), &[2, 2]);

        let output_data = output.to_vec();
        // GLU applies sigmoid to second half and multiplies with first half
        // First batch: [1.0, 2.0] ⊙ sigmoid([0.0, 1.0])
        // Second batch: [3.0, 4.0] ⊙ sigmoid([-1.0, 2.0])
        assert_eq!(output_data.len(), 4);
    }

    #[test]
    fn test_glu_odd_dimension_error() {
        let layer = GLU::<f32>::new();

        // Input with odd last dimension (3) - should fail
        let input_data = vec![1.0, 2.0, 3.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![3])).unwrap();

        let result = layer.forward(input);
        assert!(result.is_err());
    }

    #[test]
    fn test_activation_parameter_count() {
        let relu_config = ActivationConfig::new(ActivationType::ReLU);
        let relu_layer = Activation::<f32>::new(relu_config).unwrap();
        assert_eq!(relu_layer.parameter_count(), 0);

        let glu_layer = GLU::<f32>::new();
        assert_eq!(glu_layer.parameter_count(), 0);
    }

    #[test]
    fn test_swiglu_layer() {
        let layer = SwiGLU::<f32>::new();

        // Input with even last dimension (4)
        let input_data = vec![1.0, 2.0, 0.0, 1.0, 3.0, 4.0, -1.0, 2.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 4])).unwrap();

        let output = layer.forward(input).unwrap();

        // Output should have half the last dimension
        assert_eq!(output.shape().dims(), &[2, 2]);

        let output_data = output.to_vec();
        // SwiGLU applies Swish to second half and multiplies with first half
        assert_eq!(output_data.len(), 4);

        // All values should be finite
        for &val in &output_data {
            assert!(val.is_finite());
        }
    }

    #[test]
    fn test_geglu_layer() {
        let layer = GeGLU::<f32>::new();

        // Input with even last dimension (6)
        let input_data = vec![1.0, 2.0, 3.0, 0.0, 1.0, -1.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![1, 6])).unwrap();

        let output = layer.forward(input).unwrap();

        // Output should have half the last dimension
        assert_eq!(output.shape().dims(), &[1, 3]);

        let output_data = output.to_vec();
        // GeGLU applies GELU to second half and multiplies with first half
        assert_eq!(output_data.len(), 3);

        // All values should be finite
        for &val in &output_data {
            assert!(val.is_finite());
        }
    }
}

/// SwiGLU activation function.
///
/// SwiGLU is a variant of GLU that uses the Swish activation function as the gate.
/// It's commonly used in modern transformer architectures like PaLM and LLaMA.
///
/// Formula: SwiGLU(x) = (x[:, :d] ⊙ Swish(x[:, d:]))
/// where Swish(x) = x * sigmoid(x)
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{SwiGLU, Layer};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// let layer = SwiGLU::new();
/// let input = CpuTensor::randn(&Shape::new(vec![2, 8]), 0.0, 1.0).unwrap();
/// let output = layer.forward(input).unwrap();
/// assert_eq!(output.shape().dims(), &[2, 4]); // Half the last dimension
/// ```
#[derive(Debug, Clone)]
pub struct SwiGLU<T: Numeric> {
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> SwiGLU<T> {
    /// Create a new SwiGLU layer.
    pub fn new() -> Self {
        Self {
            training: false,
            _phantom: PhantomData,
        }
    }

    /// Apply Swish activation function.
    fn apply_swish(&self, x: T) -> T {
        let x_f32 = x.to_f32();
        let sigmoid = 1.0 / (1.0 + (-x_f32).exp());
        T::from_f32(x_f32 * sigmoid)
    }
}

impl<T: Numeric> Layer<T> for SwiGLU<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the SwiGLU layer.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        let input_shape = input.shape();
        let input_dims = input_shape.dims();
        let last_dim = input_dims[input_dims.len() - 1];

        // Check that the last dimension is even (can be split into two halves)
        if last_dim % 2 != 0 {
            return Err(TensorError::InvalidDimension {
                dimension: last_dim,
                total_dims: 2,
                context: Some(ErrorContext::new("forward", "layers::activation::swiglu")
                    .with_info("reason", "last dimension must be even for SwiGLU")),
            });
        }

        let half_dim = last_dim / 2;
        let input_data = input.data();
        let total_elements = input_data.len();
        let batch_elements = total_elements / last_dim;

        let mut output_data = Vec::with_capacity(batch_elements * half_dim);

        // Process each batch element
        for batch_idx in 0..batch_elements {
            let batch_offset = batch_idx * last_dim;

            // Split into two halves: first half and gate half
            for i in 0..half_dim {
                let first_half_val = input_data[batch_offset + i];
                let gate_half_val = input_data[batch_offset + half_dim + i];

                // Apply SwiGLU: first_half * Swish(gate_half)
                let gate_activated = self.apply_swish(gate_half_val);
                let output_val = first_half_val * gate_activated;
                output_data.push(output_val);
            }
        }

        // Create output shape with half the last dimension
        let mut output_dims = input_dims.to_vec();
        let last_idx = output_dims.len() - 1;
        output_dims[last_idx] = half_dim;
        let output_shape = Shape::new(output_dims);

        CpuTensor::from_data(output_data, output_shape)
    }

    fn parameter_count(&self) -> usize {
        0 // SwiGLU has no learnable parameters
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

/// GeGLU activation function.
///
/// GeGLU is a variant of GLU that uses the GELU activation function as the gate.
/// It's commonly used in transformer architectures and provides smooth gating.
///
/// Formula: GeGLU(x) = (x[:, :d] ⊙ GELU(x[:, d:]))
/// where GELU(x) = 0.5 * x * (1 + tanh(√(2/π) * (x + 0.044715 * x³)))
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{GeGLU, Layer};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// let layer = GeGLU::new();
/// let input = CpuTensor::randn(&Shape::new(vec![2, 8]), 0.0, 1.0).unwrap();
/// let output = layer.forward(input).unwrap();
/// assert_eq!(output.shape().dims(), &[2, 4]); // Half the last dimension
/// ```
#[derive(Debug, Clone)]
pub struct GeGLU<T: Numeric> {
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> GeGLU<T> {
    /// Create a new GeGLU layer.
    pub fn new() -> Self {
        Self {
            training: false,
            _phantom: PhantomData,
        }
    }

    /// Apply GELU activation function.
    fn apply_gelu(&self, x: T) -> T {
        let x_f32 = x.to_f32();
        let sqrt_2_over_pi = (2.0 / std::f32::consts::PI).sqrt();
        let x_cubed = x_f32 * x_f32 * x_f32;
        let inner = sqrt_2_over_pi * (x_f32 + 0.044715 * x_cubed);
        let result = 0.5 * x_f32 * (1.0 + inner.tanh());
        T::from_f32(result)
    }
}

impl<T: Numeric> Layer<T> for GeGLU<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the GeGLU layer.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        let input_shape = input.shape();
        let input_dims = input_shape.dims();
        let last_dim = input_dims[input_dims.len() - 1];

        // Check that the last dimension is even (can be split into two halves)
        if last_dim % 2 != 0 {
            return Err(TensorError::InvalidDimension {
                dimension: last_dim,
                total_dims: 2,
                context: Some(ErrorContext::new("forward", "layers::activation::geglu")
                    .with_info("reason", "last dimension must be even for GeGLU")),
            });
        }

        let half_dim = last_dim / 2;
        let input_data = input.data();
        let total_elements = input_data.len();
        let batch_elements = total_elements / last_dim;

        let mut output_data = Vec::with_capacity(batch_elements * half_dim);

        // Process each batch element
        for batch_idx in 0..batch_elements {
            let batch_offset = batch_idx * last_dim;

            // Split into two halves: first half and gate half
            for i in 0..half_dim {
                let first_half_val = input_data[batch_offset + i];
                let gate_half_val = input_data[batch_offset + half_dim + i];

                // Apply GeGLU: first_half * GELU(gate_half)
                let gate_activated = self.apply_gelu(gate_half_val);
                let output_val = first_half_val * gate_activated;
                output_data.push(output_val);
            }
        }

        // Create output shape with half the last dimension
        let mut output_dims = input_dims.to_vec();
        let last_idx = output_dims.len() - 1;
        output_dims[last_idx] = half_dim;
        let output_shape = Shape::new(output_dims);

        CpuTensor::from_data(output_data, output_shape)
    }

    fn parameter_count(&self) -> usize {
        0 // GeGLU has no learnable parameters
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

//=============================================================================
// Composite Activation Functions
//=============================================================================

/// Configuration for composite activation functions.
#[derive(Debug, Clone)]
pub struct CompositeActivationConfig {
    /// List of activation functions to apply in sequence
    pub activations: Vec<ActivationType>,
    /// Combination strategy for multiple activations
    pub combination_strategy: CombinationStrategy,
}

/// Strategy for combining multiple activation functions.
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CombinationStrategy {
    /// Apply activations sequentially (composition)
    Sequential,
    /// Add the outputs of all activations
    Sum,
    /// Multiply the outputs of all activations
    Product,
    /// Take the maximum of all activation outputs
    Max,
    /// Take the minimum of all activation outputs
    Min,
    /// Weighted combination of activations
    Weighted { weights: Vec<f32> },
}

impl CompositeActivationConfig {
    /// Create a new composite activation configuration with sequential strategy.
    pub fn new_sequential(activations: Vec<ActivationType>) -> Self {
        Self {
            activations,
            combination_strategy: CombinationStrategy::Sequential,
        }
    }

    /// Create a new composite activation configuration with sum strategy.
    pub fn new_sum(activations: Vec<ActivationType>) -> Self {
        Self {
            activations,
            combination_strategy: CombinationStrategy::Sum,
        }
    }

    /// Create a new composite activation configuration with weighted strategy.
    pub fn new_weighted(activations: Vec<ActivationType>, weights: Vec<f32>) -> Result<Self, TensorError> {
        if activations.len() != weights.len() {
            return Err(TensorError::ShapeMismatch {
                expected: vec![activations.len()],
                actual: vec![weights.len()],
                context: Some(ErrorContext::new("new_weighted", "layers::activation::composite")),
            });
        }

        Ok(Self {
            activations,
            combination_strategy: CombinationStrategy::Weighted { weights },
        })
    }
}

/// Composite activation layer that combines multiple activation functions.
#[derive(Debug, Clone)]
pub struct CompositeActivation<T: Numeric> {
    /// Configuration
    config: CompositeActivationConfig,
    /// Individual activation layers
    activation_layers: Vec<Activation<T>>,
    /// Training mode
    training: bool,
    /// Phantom data for type parameter
    _phantom: PhantomData<T>,
}

impl<T: Numeric> CompositeActivation<T> {
    /// Create a new composite activation layer.
    pub fn new(config: CompositeActivationConfig) -> Self {
        let activation_layers: Result<Vec<_>, _> = config.activations.iter()
            .map(|activation_type| {
                let activation_config = ActivationConfig::new(activation_type.clone());
                Activation::new(activation_config)
            })
            .collect();
        let activation_layers = activation_layers.unwrap(); // Safe because Activation::new doesn't fail for basic types

        Self {
            config,
            activation_layers,
            training: true,
            _phantom: PhantomData,
        }
    }

    /// Apply a single activation function to the input.
    fn apply_single_activation(&self, input: &CpuTensor<T>, activation_idx: usize) -> Result<CpuTensor<T>, TensorError> {
        if activation_idx >= self.activation_layers.len() {
            return Err(TensorError::ComputationError {
                message: format!("Activation index {} out of bounds", activation_idx),
                context: Some(ErrorContext::new("apply_single_activation", "layers::activation::composite")),
            });
        }

        self.activation_layers[activation_idx].forward(input.clone())
    }
}

impl<T: Numeric> Layer<T> for CompositeActivation<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        match &self.config.combination_strategy {
            CombinationStrategy::Sequential => {
                // Apply activations sequentially (composition)
                let mut result = input;
                for i in 0..self.activation_layers.len() {
                    result = self.apply_single_activation(&result, i)?;
                }
                Ok(result)
            },

            CombinationStrategy::Sum => {
                // Sum all activation outputs
                let mut result = self.apply_single_activation(&input, 0)?;
                for i in 1..self.activation_layers.len() {
                    let activation_output = self.apply_single_activation(&input, i)?;
                    result = result.add(&activation_output)?;
                }
                Ok(result)
            },

            CombinationStrategy::Product => {
                // Multiply all activation outputs
                let mut result = self.apply_single_activation(&input, 0)?;
                for i in 1..self.activation_layers.len() {
                    let activation_output = self.apply_single_activation(&input, i)?;
                    result = result.mul(&activation_output)?;
                }
                Ok(result)
            },

            CombinationStrategy::Max => {
                // Take element-wise maximum of all activation outputs
                let mut result = self.apply_single_activation(&input, 0)?;
                for i in 1..self.activation_layers.len() {
                    let activation_output = self.apply_single_activation(&input, i)?;
                    // Element-wise maximum
                    let result_data = result.to_vec();
                    let activation_data = activation_output.to_vec();
                    let max_data: Vec<T> = result_data.iter().zip(activation_data.iter())
                        .map(|(a, b)| if *a > *b { *a } else { *b })
                        .collect();
                    result = CpuTensor::from_data(max_data, result.shape().clone())?;
                }
                Ok(result)
            },

            CombinationStrategy::Min => {
                // Take element-wise minimum of all activation outputs
                let mut result = self.apply_single_activation(&input, 0)?;
                for i in 1..self.activation_layers.len() {
                    let activation_output = self.apply_single_activation(&input, i)?;
                    // Element-wise minimum
                    let result_data = result.to_vec();
                    let activation_data = activation_output.to_vec();
                    let min_data: Vec<T> = result_data.iter().zip(activation_data.iter())
                        .map(|(a, b)| if *a < *b { *a } else { *b })
                        .collect();
                    result = CpuTensor::from_data(min_data, result.shape().clone())?;
                }
                Ok(result)
            },

            CombinationStrategy::Weighted { weights } => {
                // Weighted combination of activation outputs
                if weights.len() != self.activation_layers.len() {
                    return Err(TensorError::ShapeMismatch {
                        expected: vec![self.activation_layers.len()],
                        actual: vec![weights.len()],
                        context: Some(ErrorContext::new("forward", "layers::activation::composite")),
                    });
                }

                let first_output = self.apply_single_activation(&input, 0)?;
                let first_data = first_output.to_vec();
                let mut result_data: Vec<T> = first_data.iter()
                    .map(|x| *x * T::from_f32(weights[0]))
                    .collect();

                for i in 1..self.activation_layers.len() {
                    let activation_output = self.apply_single_activation(&input, i)?;
                    let activation_data = activation_output.to_vec();
                    let weight = T::from_f32(weights[i]);

                    for (j, &value) in activation_data.iter().enumerate() {
                        result_data[j] = result_data[j] + value * weight;
                    }
                }

                CpuTensor::from_data(result_data, first_output.shape().clone())
            },
        }
    }

    fn parameter_count(&self) -> usize {
        // Sum of parameters from all individual activation layers
        self.activation_layers.iter()
            .map(|layer| layer.parameter_count())
            .sum()
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
        for layer in &mut self.activation_layers {
            layer.set_training(training);
        }
    }
}

impl<T: Numeric> ConfigurableLayer<T, CompositeActivationConfig> for CompositeActivation<T> {
    fn new(config: CompositeActivationConfig) -> Result<Self, Self::Error> {
        Ok(Self::new(config))
    }

    fn config(&self) -> &CompositeActivationConfig {
        &self.config
    }

    fn update_config(&mut self, config: CompositeActivationConfig) -> Result<(), Self::Error> {
        let activation_layers: Result<Vec<_>, _> = config.activations.iter()
            .map(|activation_type| {
                let activation_config = ActivationConfig::new(activation_type.clone());
                Activation::new(activation_config)
            })
            .collect();
        let activation_layers = activation_layers.unwrap();

        self.config = config;
        self.activation_layers = activation_layers;
        Ok(())
    }
}

//=============================================================================
// Predefined Composite Activation Functions
//=============================================================================

impl CompositeActivationConfig {
    /// Create a ReLU-Sigmoid composite activation (ReLU followed by Sigmoid).
    pub fn relu_sigmoid() -> Self {
        Self::new_sequential(vec![ActivationType::ReLU, ActivationType::Sigmoid])
    }

    /// Create a GELU-Tanh composite activation (GELU followed by Tanh).
    pub fn gelu_tanh() -> Self {
        Self::new_sequential(vec![ActivationType::GELU, ActivationType::Tanh])
    }

    /// Create a Swish-ReLU sum composite (Swish + ReLU).
    pub fn swish_relu_sum() -> Self {
        Self::new_sum(vec![ActivationType::Swish, ActivationType::ReLU])
    }

    /// Create a multi-activation ensemble with equal weights.
    pub fn ensemble(activations: Vec<ActivationType>) -> Result<Self, TensorError> {
        let num_activations = activations.len();
        if num_activations == 0 {
            return Err(TensorError::ComputationError {
                message: "Cannot create ensemble with zero activations".to_string(),
                context: Some(ErrorContext::new("ensemble", "layers::activation::composite")),
            });
        }

        let weight = 1.0 / num_activations as f32;
        let weights = vec![weight; num_activations];
        Self::new_weighted(activations, weights)
    }

    /// Create a gated activation: input * sigmoid(input) + activation(input).
    pub fn gated(activation: ActivationType) -> Self {
        Self::new_weighted(
            vec![ActivationType::Sigmoid, activation],
            vec![1.0, 1.0]
        ).unwrap() // Safe because we know the lengths match
    }
}

//=============================================================================
// Custom Activation Functions
//=============================================================================

/// Configuration for custom activation functions.
#[derive(Debug, Clone)]
pub struct CustomActivationConfig {
    /// Name of the custom activation
    pub name: String,
    /// Parameters for the custom activation
    pub params: std::collections::HashMap<String, f32>,
    /// Type of custom activation
    pub activation_type: CustomActivationType,
}

/// Types of custom activation functions.
#[derive(Debug, Clone, PartialEq)]
pub enum CustomActivationType {
    /// Parametric ReLU with configurable alpha
    ParametricReLU,
    /// Scaled activation with configurable scale factor
    ScaledActivation { base_activation: ActivationType },
}

/// Custom activation layer for user-defined activation functions.
#[derive(Debug)]
pub struct CustomActivation {
    /// Configuration
    config: CustomActivationConfig,
    /// Training mode
    training: bool,
}

impl CustomActivation {
    /// Create a new custom activation layer.
    pub fn new(config: CustomActivationConfig) -> Self {
        Self {
            config,
            training: true,
        }
    }

    /// Create a parametric ReLU custom activation.
    pub fn parametric_relu(alpha: f32) -> Self {
        let mut params = std::collections::HashMap::new();
        params.insert("alpha".to_string(), alpha);

        let config = CustomActivationConfig {
            name: "ParametricReLU".to_string(),
            params,
            activation_type: CustomActivationType::ParametricReLU,
        };

        Self::new(config)
    }

    /// Create a scaled activation: scale * activation(input).
    pub fn scaled(activation: ActivationType, scale: f32) -> Result<Self, TensorError> {
        let mut params = std::collections::HashMap::new();
        params.insert("scale".to_string(), scale);

        let config = CustomActivationConfig {
            name: format!("Scaled{:?}", activation),
            params,
            activation_type: CustomActivationType::ScaledActivation { base_activation: activation },
        };

        Ok(Self::new(config))
    }
}

impl Layer<f32> for CustomActivation {
    type Input = CpuTensor<f32>;
    type Output = CpuTensor<f32>;
    type Error = TensorError;

    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        match &self.config.activation_type {
            CustomActivationType::ParametricReLU => {
                let alpha = self.config.params.get("alpha").unwrap_or(&0.01);
                let input_data = input.to_vec();
                let output_data: Vec<f32> = input_data.iter()
                    .map(|&x| if x > 0.0 { x } else { alpha * x })
                    .collect();
                CpuTensor::from_data(output_data, input.shape().clone())
            },
            CustomActivationType::ScaledActivation { base_activation } => {
                let scale = self.config.params.get("scale").unwrap_or(&1.0);

                // Apply the base activation first
                let activation_layer = Activation::new(ActivationConfig::new(base_activation.clone()))?;
                let activated = activation_layer.forward(input)?;

                // Scale the result
                let activated_data = activated.to_vec();
                let output_data: Vec<f32> = activated_data.iter()
                    .map(|&x| x * scale)
                    .collect();
                CpuTensor::from_data(output_data, activated.shape().clone())
            },
        }
    }

    fn parameter_count(&self) -> usize {
        self.config.params.len()
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

impl ConfigurableLayer<f32, CustomActivationConfig> for CustomActivation {
    fn new(config: CustomActivationConfig) -> Result<Self, Self::Error> {
        Ok(Self::new(config))
    }

    fn config(&self) -> &CustomActivationConfig {
        &self.config
    }

    fn update_config(&mut self, config: CustomActivationConfig) -> Result<(), Self::Error> {
        self.config = config;
        Ok(())
    }
}

//=============================================================================
// Tests for Composite and Custom Activation Functions
//=============================================================================

#[cfg(test)]
mod composite_tests {
    use super::*;
    use crate::tensor::{cpu::CpuTensorFactory, Shape};

    #[test]
    fn test_composite_sequential_activation() {
        let config = CompositeActivationConfig::new_sequential(vec![
            ActivationType::ReLU,
            ActivationType::Sigmoid,
        ]);
        let layer = CompositeActivation::<f32>::new(config);

        let input = CpuTensorFactory::from_vec(vec![-1.0, 0.0, 1.0, 2.0], &Shape::new(vec![4])).unwrap();
        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        // First ReLU: [-1, 0, 1, 2] -> [0, 0, 1, 2]
        // Then Sigmoid: [0, 0, 1, 2] -> [0.5, 0.5, ~0.73, ~0.88]
        assert_eq!(output_data.len(), 4);
        assert!((output_data[0] - 0.5).abs() < 1e-6);
        assert!((output_data[1] - 0.5).abs() < 1e-6);
        assert!(output_data[2] > 0.7 && output_data[2] < 0.8);
        assert!(output_data[3] > 0.8 && output_data[3] < 0.9);
    }

    #[test]
    fn test_composite_sum_activation() {
        let config = CompositeActivationConfig::new_sum(vec![
            ActivationType::ReLU,
            ActivationType::Sigmoid,
        ]);
        let layer = CompositeActivation::<f32>::new(config);

        let input = CpuTensorFactory::from_vec(vec![0.0, 1.0], &Shape::new(vec![2])).unwrap();
        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        // ReLU(0) + Sigmoid(0) = 0 + 0.5 = 0.5
        // ReLU(1) + Sigmoid(1) = 1 + ~0.73 = ~1.73
        assert_eq!(output_data.len(), 2);
        assert!((output_data[0] - 0.5).abs() < 1e-6);
        assert!(output_data[1] > 1.7 && output_data[1] < 1.8);
    }

    #[test]
    fn test_composite_weighted_activation() {
        let config = CompositeActivationConfig::new_weighted(
            vec![ActivationType::ReLU, ActivationType::Sigmoid],
            vec![0.7, 0.3]
        ).unwrap();
        let layer = CompositeActivation::<f32>::new(config);

        let input = CpuTensorFactory::from_vec(vec![1.0], &Shape::new(vec![1])).unwrap();
        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        // 0.7 * ReLU(1) + 0.3 * Sigmoid(1) = 0.7 * 1 + 0.3 * ~0.73 = ~0.92
        assert_eq!(output_data.len(), 1);
        assert!(output_data[0] > 0.9 && output_data[0] < 1.0);
    }

    #[test]
    fn test_composite_max_activation() {
        let config = CompositeActivationConfig {
            activations: vec![ActivationType::ReLU, ActivationType::Sigmoid],
            combination_strategy: CombinationStrategy::Max,
        };
        let layer = CompositeActivation::<f32>::new(config);

        let input = CpuTensorFactory::from_vec(vec![-1.0, 2.0], &Shape::new(vec![2])).unwrap();
        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        // max(ReLU(-1), Sigmoid(-1)) = max(0, ~0.27) = ~0.27
        // max(ReLU(2), Sigmoid(2)) = max(2, ~0.88) = 2
        assert_eq!(output_data.len(), 2);
        assert!(output_data[0] > 0.2 && output_data[0] < 0.3);
        assert!((output_data[1] - 2.0).abs() < 1e-6);
    }

    #[test]
    fn test_predefined_composite_activations() {
        // Test ReLU-Sigmoid composite
        let config = CompositeActivationConfig::relu_sigmoid();
        let layer = CompositeActivation::<f32>::new(config);
        assert_eq!(layer.config.activations.len(), 2);
        assert_eq!(layer.config.activations[0], ActivationType::ReLU);
        assert_eq!(layer.config.activations[1], ActivationType::Sigmoid);

        // Test ensemble
        let config = CompositeActivationConfig::ensemble(vec![
            ActivationType::ReLU,
            ActivationType::GELU,
            ActivationType::Swish,
        ]).unwrap();
        let layer = CompositeActivation::<f32>::new(config);
        assert_eq!(layer.config.activations.len(), 3);

        if let CombinationStrategy::Weighted { weights } = &layer.config.combination_strategy {
            assert_eq!(weights.len(), 3);
            assert!((weights[0] - 1.0/3.0).abs() < 1e-6);
        } else {
            panic!("Expected weighted combination strategy");
        }
    }

    #[test]
    fn test_custom_parametric_relu() {
        let layer = CustomActivation::parametric_relu(0.1);

        let input = CpuTensorFactory::from_vec(vec![-2.0, -1.0, 0.0, 1.0, 2.0], &Shape::new(vec![5])).unwrap();
        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        // Parametric ReLU with alpha=0.1
        assert_eq!(output_data.len(), 5);
        assert!((output_data[0] - (-0.2)).abs() < 1e-6); // -2 * 0.1 = -0.2
        assert!((output_data[1] - (-0.1)).abs() < 1e-6); // -1 * 0.1 = -0.1
        assert!((output_data[2] - 0.0).abs() < 1e-6);    // 0
        assert!((output_data[3] - 1.0).abs() < 1e-6);    // 1
        assert!((output_data[4] - 2.0).abs() < 1e-6);    // 2
    }

    #[test]
    fn test_custom_scaled_activation() {
        let layer = CustomActivation::scaled(ActivationType::ReLU, 2.0).unwrap();

        let input = CpuTensorFactory::from_vec(vec![-1.0, 0.0, 1.0, 2.0], &Shape::new(vec![4])).unwrap();
        let output = layer.forward(input).unwrap();
        let output_data = output.to_vec();

        // Scaled ReLU with scale=2.0
        assert_eq!(output_data.len(), 4);
        assert!((output_data[0] - 0.0).abs() < 1e-6);  // ReLU(-1) * 2 = 0 * 2 = 0
        assert!((output_data[1] - 0.0).abs() < 1e-6);  // ReLU(0) * 2 = 0 * 2 = 0
        assert!((output_data[2] - 2.0).abs() < 1e-6);  // ReLU(1) * 2 = 1 * 2 = 2
        assert!((output_data[3] - 4.0).abs() < 1e-6);  // ReLU(2) * 2 = 2 * 2 = 4
    }

    #[test]
    fn test_composite_parameter_count() {
        let config = CompositeActivationConfig::new_sequential(vec![
            ActivationType::ReLU,
            ActivationType::GELU,
        ]);
        let layer = CompositeActivation::<f32>::new(config);

        // ReLU and GELU have no parameters
        assert_eq!(layer.parameter_count(), 0);
    }

    #[test]
    fn test_composite_training_mode() {
        let config = CompositeActivationConfig::new_sequential(vec![
            ActivationType::ReLU,
            ActivationType::GELU,
        ]);
        let mut layer = CompositeActivation::<f32>::new(config);

        assert!(layer.training());
        layer.set_training(false);
        assert!(!layer.training());

        // Check that all sub-layers are also set to the same training mode
        for activation_layer in &layer.activation_layers {
            assert!(!activation_layer.training());
        }
    }
}
