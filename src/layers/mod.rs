//! Neural network layers.
//!
//! This module provides implementations of common neural network layers
//! used in transformer architectures.

pub mod linear;
pub mod norm;
pub mod activation;
pub mod embedding;
pub mod feedforward;
pub mod dropout;
pub mod factory;
pub mod sequential;
pub mod parallel;

pub use linear::*;
pub use norm::*;
pub use activation::*;
pub use embedding::*;
pub use feedforward::*;
pub use factory::*;
pub use sequential::*;
pub use parallel::*;
// pub use dropout::*;

use crate::tensor::{Tensor, Numeric};

/// Base trait for all neural network layers.
pub trait Layer<T: Numeric>: Send + Sync {
    /// Input type for this layer.
    type Input;
    /// Output type for this layer.
    type Output;
    /// Error type for layer operations.
    type Error: std::error::Error + Send + Sync + 'static;
    
    /// Forward pass through the layer.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error>;
    
    /// Get the number of parameters in this layer.
    fn parameter_count(&self) -> usize;
    
    /// Check if the layer is in training mode.
    fn training(&self) -> bool;
    
    /// Set the training mode.
    fn set_training(&mut self, training: bool);
}

/// Trait for configurable layers.
pub trait ConfigurableLayer<T: Numeric, C>: Layer<T> {
    /// Create a new layer with the given configuration.
    fn new(config: C) -> Result<Self, Self::Error>
    where
        Self: Sized;
    
    /// Get the configuration of this layer.
    fn config(&self) -> &C;
    
    /// Update the configuration of this layer.
    fn update_config(&mut self, config: C) -> Result<(), Self::Error>;
}

/// Trait for layers with learnable parameters.
pub trait ParameterizedLayer<T: Numeric>: Layer<T> {
    /// Initialize parameters with the given initialization strategy.
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), Self::Error>;
}

/// Parameter initialization strategies.
#[derive(Debug, Clone)]
pub enum ParameterInit {
    /// Zero initialization.
    Zeros,
    /// Ones initialization.
    Ones,
    /// Xavier/Glorot uniform initialization.
    XavierUniform,
    /// Xavier/Glorot normal initialization.
    XavierNormal,
    /// Kaiming/He uniform initialization.
    KaimingUniform,
    /// Kaiming/He normal initialization.
    KaimingNormal,
    /// Custom initialization with mean and std.
    Normal { mean: f32, std: f32 },
    /// Custom uniform initialization.
    Uniform { low: f32, high: f32 },
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parameter_init() {
        let init = ParameterInit::XavierUniform;
        match init {
            ParameterInit::XavierUniform => assert!(true),
            _ => assert!(false),
        }
    }
}
