//! Normalization layers.
//!
//! This module provides various normalization layers commonly used in transformer
//! architectures, including LayerNorm, RMSNorm, BatchNorm, and GroupNorm.
//!
//! All normalization layers support:
//! - Learnable scale and shift parameters
//! - Configurable epsilon for numerical stability
//! - Efficient computation with proper broadcasting
//! - Training/inference mode switching

use std::marker::PhantomData;
use crate::error::{TensorError, ErrorContext};
use crate::tensor::{Tensor, TensorOps, TensorFactory, Numeric, Shape, cpu::{CpuTensor, CpuTensorFactory}};
use crate::layers::{Layer, ConfigurableLayer, ParameterizedLayer, ParameterInit};

/// Configuration for LayerNorm.
#[derive(Debug, Clone)]
pub struct LayerNormConfig {
    /// Shape of the normalized dimensions.
    pub normalized_shape: Vec<usize>,
    /// Small constant for numerical stability.
    pub eps: f32,
    /// Whether to include learnable scale parameter.
    pub elementwise_affine: bool,
    /// Whether to include bias term.
    pub bias: bool,
}

impl LayerNormConfig {
    /// Create a new LayerNorm configuration.
    pub fn new(normalized_shape: Vec<usize>) -> Self {
        Self {
            normalized_shape,
            eps: 1e-5,
            elementwise_affine: true,
            bias: true,
        }
    }

    /// Set epsilon value.
    pub fn with_eps(mut self, eps: f32) -> Self {
        self.eps = eps;
        self
    }

    /// Set whether to use elementwise affine transformation.
    pub fn with_elementwise_affine(mut self, elementwise_affine: bool) -> Self {
        self.elementwise_affine = elementwise_affine;
        self
    }

    /// Set whether to use bias.
    pub fn with_bias(mut self, bias: bool) -> Self {
        self.bias = bias;
        self
    }
}

/// Layer Normalization.
///
/// Applies layer normalization over the last dimensions of the input tensor.
/// The normalization is performed over the `normalized_shape` dimensions.
///
/// The formula is:
/// ```text
/// y = (x - mean) / sqrt(var + eps) * weight + bias
/// ```
///
/// where:
/// - `mean` and `var` are computed over the normalized dimensions
/// - `weight` and `bias` are learnable parameters (optional)
/// - `eps` is a small constant for numerical stability
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{LayerNorm, LayerNormConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create LayerNorm for the last dimension
/// let config = LayerNormConfig::new(vec![512]);
/// let mut layer = LayerNorm::new(config).unwrap();
///
/// // Initialize parameters
/// layer.init_parameters(ParameterInit::Ones).unwrap();
///
/// // Forward pass
/// let input = CpuTensor::randn(&Shape::new(vec![32, 128, 512]), 0.0, 1.0).unwrap();
/// let output = layer.forward(input).unwrap();
/// assert_eq!(output.shape().dims(), &[32, 128, 512]);
/// ```
#[derive(Debug, Clone)]
pub struct LayerNorm<T: Numeric> {
    /// Learnable scale parameter.
    weight: Option<CpuTensor<T>>,
    /// Learnable bias parameter.
    bias: Option<CpuTensor<T>>,
    /// Layer configuration.
    config: LayerNormConfig,
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> LayerNorm<T> {
    /// Create a new LayerNorm layer.
    pub fn new(config: LayerNormConfig) -> Result<Self, TensorError> {
        let param_shape = Shape::new(config.normalized_shape.clone());

        // Create weight parameter if elementwise affine is enabled
        let weight = if config.elementwise_affine {
            Some(CpuTensorFactory::ones(&param_shape)?)
        } else {
            None
        };

        // Create bias parameter if bias is enabled
        let bias = if config.bias && config.elementwise_affine {
            Some(CpuTensorFactory::zeros(&param_shape)?)
        } else {
            None
        };

        Ok(Self {
            weight,
            bias,
            config,
            training: false,
            _phantom: PhantomData,
        })
    }

    /// Get a reference to the weight parameter.
    pub fn weight(&self) -> Option<&CpuTensor<T>> {
        self.weight.as_ref()
    }

    /// Get a reference to the bias parameter.
    pub fn bias(&self) -> Option<&CpuTensor<T>> {
        self.bias.as_ref()
    }

    /// Get the normalized shape.
    pub fn normalized_shape(&self) -> &[usize] {
        &self.config.normalized_shape
    }

    /// Get the epsilon value.
    pub fn eps(&self) -> f32 {
        self.config.eps
    }

    /// Check if elementwise affine is enabled.
    pub fn elementwise_affine(&self) -> bool {
        self.config.elementwise_affine
    }

    /// Compute mean and variance over the normalized dimensions.
    fn compute_stats(&self, input: &CpuTensor<T>) -> Result<(CpuTensor<T>, CpuTensor<T>), TensorError> {
        let input_shape = input.shape();
        let input_dims = input_shape.dims();
        let input_rank = input_dims.len();
        let norm_dims = self.config.normalized_shape.len();

        // Validate that normalized_shape matches the last dimensions of input
        if norm_dims > input_rank {
            return Err(TensorError::ShapeMismatch {
                expected: self.config.normalized_shape.clone(),
                actual: input_dims.to_vec(),
                context: Some(ErrorContext::new("compute_stats", "layers::norm::layer_norm")),
            });
        }

        let start_dim = input_rank - norm_dims;
        for (i, &norm_dim) in self.config.normalized_shape.iter().enumerate() {
            if input_dims[start_dim + i] != norm_dim {
                return Err(TensorError::ShapeMismatch {
                    expected: self.config.normalized_shape.clone(),
                    actual: input_dims[start_dim..].to_vec(),
                    context: Some(ErrorContext::new("compute_stats", "layers::norm::layer_norm")),
                });
            }
        }

        // For now, implement a simplified version that works with the last dimension
        // TODO: Implement full multi-dimensional normalization
        if norm_dims != 1 {
            return Err(TensorError::DataTypeIncompatible {
                operation: "multi-dimensional layer norm".to_string(),
                dtype: "not yet implemented".to_string(),
                context: Some(ErrorContext::new("compute_stats", "layers::norm::layer_norm")),
            });
        }

        let last_dim = input_dims[input_rank - 1];
        let batch_size: usize = input_dims[..input_rank - 1].iter().product();

        // Compute mean and variance for each batch element
        let input_data = input.data();
        let mut means = Vec::with_capacity(batch_size);
        let mut vars = Vec::with_capacity(batch_size);

        for batch_idx in 0..batch_size {
            let start_idx = batch_idx * last_dim;
            let end_idx = start_idx + last_dim;
            let batch_data = &input_data[start_idx..end_idx];

            // Compute mean
            let sum: T = batch_data.iter().fold(T::ZERO, |acc, &x| acc + x);
            let mean = sum / T::from_f32(last_dim as f32);
            means.push(mean);

            // Compute variance
            let var_sum: T = batch_data.iter()
                .fold(T::ZERO, |acc, &x| acc + (x - mean) * (x - mean));
            let variance = var_sum / T::from_f32(last_dim as f32);
            vars.push(variance);
        }

        // Create mean and variance tensors
        let mean_shape = Shape::new(input_dims[..input_rank - 1].to_vec());
        let mean_tensor = CpuTensor::from_data(means, mean_shape.clone())?;
        let var_tensor = CpuTensor::from_data(vars, mean_shape)?;

        Ok((mean_tensor, var_tensor))
    }
}

impl<T: Numeric> Layer<T> for LayerNorm<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the LayerNorm layer.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        // Compute mean and variance
        let (mean, var) = self.compute_stats(&input)?;

        let input_shape = input.shape();
        let input_dims = input_shape.dims();
        let input_rank = input_dims.len();
        let last_dim = input_dims[input_rank - 1];
        let batch_size: usize = input_dims[..input_rank - 1].iter().product();

        // Normalize: (x - mean) / sqrt(var + eps)
        let input_data = input.data();
        let mean_data = mean.data();
        let var_data = var.data();
        let eps = T::from_f32(self.config.eps);

        let mut normalized_data = Vec::with_capacity(input_data.len());

        for batch_idx in 0..batch_size {
            let batch_mean = mean_data[batch_idx];
            let batch_var = var_data[batch_idx];
            let std_inv = (batch_var + eps).sqrt();

            let start_idx = batch_idx * last_dim;
            let end_idx = start_idx + last_dim;

            for &x in &input_data[start_idx..end_idx] {
                let normalized = (x - batch_mean) / std_inv;
                normalized_data.push(normalized);
            }
        }

        let mut output = CpuTensor::from_data(normalized_data, input_shape.clone())?;

        // Apply elementwise affine transformation if enabled
        if self.config.elementwise_affine {
            if let Some(ref weight) = self.weight {
                // Broadcast weight across batch dimensions
                let weight_data = weight.data();
                let output_data = output.data().to_vec();
                let mut scaled_data = Vec::with_capacity(output_data.len());

                for batch_idx in 0..batch_size {
                    let start_idx = batch_idx * last_dim;
                    for feat_idx in 0..last_dim {
                        let output_val = output_data[start_idx + feat_idx];
                        let weight_val = weight_data[feat_idx];
                        scaled_data.push(output_val * weight_val);
                    }
                }

                output = CpuTensor::from_data(scaled_data, input_shape.clone())?;
            }

            if let Some(ref bias) = self.bias {
                // Broadcast bias across batch dimensions
                let bias_data = bias.data();
                let output_data = output.data().to_vec();
                let mut biased_data = Vec::with_capacity(output_data.len());

                for batch_idx in 0..batch_size {
                    let start_idx = batch_idx * last_dim;
                    for feat_idx in 0..last_dim {
                        let output_val = output_data[start_idx + feat_idx];
                        let bias_val = bias_data[feat_idx];
                        biased_data.push(output_val + bias_val);
                    }
                }

                output = CpuTensor::from_data(biased_data, input_shape.clone())?;
            }
        }

        Ok(output)
    }

    fn parameter_count(&self) -> usize {
        let param_size: usize = self.config.normalized_shape.iter().product();
        let weight_params = if self.weight.is_some() { param_size } else { 0 };
        let bias_params = if self.bias.is_some() { param_size } else { 0 };
        weight_params + bias_params
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

impl<T: Numeric> ConfigurableLayer<T, LayerNormConfig> for LayerNorm<T> {
    fn new(config: LayerNormConfig) -> Result<Self, Self::Error> {
        Self::new(config)
    }

    fn config(&self) -> &LayerNormConfig {
        &self.config
    }

    fn update_config(&mut self, config: LayerNormConfig) -> Result<(), Self::Error> {
        // Check if the normalized shape is compatible
        if config.normalized_shape != self.config.normalized_shape {
            return Err(TensorError::ShapeMismatch {
                expected: self.config.normalized_shape.clone(),
                actual: config.normalized_shape.clone(),
                context: Some(ErrorContext::new("update_config", "layers::norm::layer_norm")
                    .with_info("reason", "normalized shape cannot be changed after initialization")),
            });
        }

        // Update configuration
        let old_config = self.config.clone();
        self.config = config;

        // Handle parameter changes
        let param_shape = Shape::new(self.config.normalized_shape.clone());

        // Handle weight parameter
        if self.config.elementwise_affine && self.weight.is_none() {
            self.weight = Some(CpuTensorFactory::ones(&param_shape)?);
        } else if !self.config.elementwise_affine && self.weight.is_some() {
            self.weight = None;
        }

        // Handle bias parameter
        if self.config.bias && self.config.elementwise_affine && self.bias.is_none() {
            self.bias = Some(CpuTensorFactory::zeros(&param_shape)?);
        } else if (!self.config.bias || !self.config.elementwise_affine) && self.bias.is_some() {
            self.bias = None;
        }

        Ok(())
    }
}

impl<T: Numeric> ParameterizedLayer<T> for LayerNorm<T> {
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), Self::Error> {
        // Initialize weight parameter
        if let Some(ref mut weight) = self.weight {
            match init_strategy {
                ParameterInit::Ones => {
                    // Weight should be initialized to ones for LayerNorm
                    let ones = CpuTensorFactory::ones(weight.shape())?;
                    *weight = ones;
                },
                _ => {
                    // For LayerNorm, weight is typically initialized to ones
                    let ones = CpuTensorFactory::ones(weight.shape())?;
                    *weight = ones;
                }
            }
        }

        // Initialize bias parameter
        if let Some(ref mut bias) = self.bias {
            // Bias is typically initialized to zeros for LayerNorm
            let zeros = CpuTensorFactory::zeros(bias.shape())?;
            *bias = zeros;
        }

        Ok(())
    }
}

/// Configuration for RMSNorm.
#[derive(Debug, Clone)]
pub struct RMSNormConfig {
    /// Shape of the normalized dimensions.
    pub normalized_shape: Vec<usize>,
    /// Small constant for numerical stability.
    pub eps: f32,
    /// Whether to include learnable scale parameter.
    pub elementwise_affine: bool,
}

impl RMSNormConfig {
    /// Create a new RMSNorm configuration.
    pub fn new(normalized_shape: Vec<usize>) -> Self {
        Self {
            normalized_shape,
            eps: 1e-6,
            elementwise_affine: true,
        }
    }

    /// Set epsilon value.
    pub fn with_eps(mut self, eps: f32) -> Self {
        self.eps = eps;
        self
    }

    /// Set whether to use elementwise affine transformation.
    pub fn with_elementwise_affine(mut self, elementwise_affine: bool) -> Self {
        self.elementwise_affine = elementwise_affine;
        self
    }
}

/// Root Mean Square Normalization (RMSNorm).
///
/// RMSNorm is a simpler and more efficient alternative to LayerNorm that only
/// uses the root mean square statistic for normalization, without centering.
///
/// The formula is:
/// ```text
/// y = x / sqrt(mean(x²) + eps) * weight
/// ```
///
/// where:
/// - `mean(x²)` is the mean of squared values over the normalized dimensions
/// - `weight` is a learnable scale parameter (optional)
/// - `eps` is a small constant for numerical stability
///
/// RMSNorm is commonly used in modern transformer architectures like LLaMA
/// and provides similar performance to LayerNorm with reduced computational cost.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{RMSNorm, RMSNormConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create RMSNorm for the last dimension
/// let config = RMSNormConfig::new(vec![512]);
/// let mut layer = RMSNorm::new(config).unwrap();
///
/// // Initialize parameters
/// layer.init_parameters(ParameterInit::Ones).unwrap();
///
/// // Forward pass
/// let input = CpuTensor::randn(&Shape::new(vec![32, 128, 512]), 0.0, 1.0).unwrap();
/// let output = layer.forward(input).unwrap();
/// assert_eq!(output.shape().dims(), &[32, 128, 512]);
/// ```
#[derive(Debug, Clone)]
pub struct RMSNorm<T: Numeric> {
    /// Learnable scale parameter.
    weight: Option<CpuTensor<T>>,
    /// Layer configuration.
    config: RMSNormConfig,
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> RMSNorm<T> {
    /// Create a new RMSNorm layer.
    pub fn new(config: RMSNormConfig) -> Result<Self, TensorError> {
        let param_shape = Shape::new(config.normalized_shape.clone());

        // Create weight parameter if elementwise affine is enabled
        let weight = if config.elementwise_affine {
            Some(CpuTensorFactory::ones(&param_shape)?)
        } else {
            None
        };

        Ok(Self {
            weight,
            config,
            training: false,
            _phantom: PhantomData,
        })
    }

    /// Get a reference to the weight parameter.
    pub fn weight(&self) -> Option<&CpuTensor<T>> {
        self.weight.as_ref()
    }

    /// Get the normalized shape.
    pub fn normalized_shape(&self) -> &[usize] {
        &self.config.normalized_shape
    }

    /// Get the epsilon value.
    pub fn eps(&self) -> f32 {
        self.config.eps
    }

    /// Check if elementwise affine transformation is enabled.
    pub fn elementwise_affine(&self) -> bool {
        self.config.elementwise_affine
    }

    /// Compute RMS (Root Mean Square) over the normalized dimensions.
    fn compute_rms(&self, input: &CpuTensor<T>) -> Result<CpuTensor<T>, TensorError> {
        let input_shape = input.shape();
        let input_dims = input_shape.dims();
        let input_rank = input_dims.len();
        let norm_dims = self.config.normalized_shape.len();

        // Validate that normalized_shape matches the last dimensions of input
        if norm_dims > input_rank {
            return Err(TensorError::ShapeMismatch {
                expected: self.config.normalized_shape.clone(),
                actual: input_dims.to_vec(),
                context: Some(ErrorContext::new("compute_rms", "layers::norm::rms_norm")),
            });
        }

        let start_dim = input_rank - norm_dims;
        for (i, &norm_dim) in self.config.normalized_shape.iter().enumerate() {
            if input_dims[start_dim + i] != norm_dim {
                return Err(TensorError::ShapeMismatch {
                    expected: self.config.normalized_shape.clone(),
                    actual: input_dims[start_dim..].to_vec(),
                    context: Some(ErrorContext::new("compute_rms", "layers::norm::rms_norm")),
                });
            }
        }

        // For now, implement a simplified version that works with the last dimension
        // TODO: Implement full multi-dimensional normalization
        if norm_dims != 1 {
            return Err(TensorError::DataTypeIncompatible {
                operation: "multi-dimensional RMS norm".to_string(),
                dtype: "not yet implemented".to_string(),
                context: Some(ErrorContext::new("compute_rms", "layers::norm::rms_norm")),
            });
        }

        let last_dim = input_dims[input_rank - 1];
        let batch_size: usize = input_dims[..input_rank - 1].iter().product();

        // Compute RMS for each batch element
        let input_data = input.data();
        let mut rms_values = Vec::with_capacity(batch_size);

        for batch_idx in 0..batch_size {
            let start_idx = batch_idx * last_dim;
            let end_idx = start_idx + last_dim;
            let batch_data = &input_data[start_idx..end_idx];

            // Compute mean of squared values
            let sum_squares: T = batch_data.iter()
                .fold(T::ZERO, |acc, &x| acc + x * x);
            let mean_square = sum_squares / T::from_f32(last_dim as f32);

            // Compute RMS: sqrt(mean_square + eps)
            let eps = T::from_f32(self.config.eps);
            let rms = (mean_square + eps).sqrt();
            rms_values.push(rms);
        }

        // Create RMS tensor
        let rms_shape = Shape::new(input_dims[..input_rank - 1].to_vec());
        let rms_tensor = CpuTensor::from_data(rms_values, rms_shape)?;

        Ok(rms_tensor)
    }
}



impl<T: Numeric> Layer<T> for RMSNorm<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the RMSNorm layer.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        // Compute RMS
        let rms = self.compute_rms(&input)?;

        let input_shape = input.shape();
        let input_dims = input_shape.dims();
        let input_rank = input_dims.len();
        let last_dim = input_dims[input_rank - 1];
        let batch_size: usize = input_dims[..input_rank - 1].iter().product();

        // Normalize: x / rms
        let input_data = input.data();
        let rms_data = rms.data();

        let mut normalized_data = Vec::with_capacity(input_data.len());

        for batch_idx in 0..batch_size {
            let batch_rms = rms_data[batch_idx];

            let start_idx = batch_idx * last_dim;
            let end_idx = start_idx + last_dim;

            for &x in &input_data[start_idx..end_idx] {
                let normalized = x / batch_rms;
                normalized_data.push(normalized);
            }
        }

        let mut output = CpuTensor::from_data(normalized_data, input_shape.clone())?;

        // Apply elementwise affine transformation if enabled
        if self.config.elementwise_affine {
            if let Some(ref weight) = self.weight {
                // Broadcast weight across batch dimensions
                let weight_data = weight.data();
                let output_data = output.data().to_vec();
                let mut scaled_data = Vec::with_capacity(output_data.len());

                for batch_idx in 0..batch_size {
                    let start_idx = batch_idx * last_dim;
                    for feat_idx in 0..last_dim {
                        let output_val = output_data[start_idx + feat_idx];
                        let weight_val = weight_data[feat_idx];
                        scaled_data.push(output_val * weight_val);
                    }
                }

                output = CpuTensor::from_data(scaled_data, input_shape.clone())?;
            }
        }

        Ok(output)
    }

    fn parameter_count(&self) -> usize {
        let param_size: usize = self.config.normalized_shape.iter().product();
        if self.weight.is_some() { param_size } else { 0 }
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

impl<T: Numeric> ParameterizedLayer<T> for RMSNorm<T> {
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), Self::Error> {
        // Initialize weight parameter
        if let Some(ref mut weight) = self.weight {
            match init_strategy {
                ParameterInit::Ones => {
                    // Weight should be initialized to ones for RMSNorm
                    let ones = CpuTensorFactory::ones(weight.shape())?;
                    *weight = ones;
                },
                _ => {
                    // For RMSNorm, weight is typically initialized to ones
                    let ones = CpuTensorFactory::ones(weight.shape())?;
                    *weight = ones;
                }
            }
        }

        Ok(())
    }
}

impl<T: Numeric> ConfigurableLayer<T, RMSNormConfig> for RMSNorm<T> {
    fn new(config: RMSNormConfig) -> Result<Self, Self::Error> {
        Self::new(config)
    }

    fn config(&self) -> &RMSNormConfig {
        &self.config
    }

    fn update_config(&mut self, config: RMSNormConfig) -> Result<(), Self::Error> {
        // Check if the normalized shape is compatible
        if config.normalized_shape != self.config.normalized_shape {
            return Err(TensorError::ShapeMismatch {
                expected: self.config.normalized_shape.clone(),
                actual: config.normalized_shape.clone(),
                context: Some(ErrorContext::new("update_config", "layers::norm::rms_norm")
                    .with_info("reason", "normalized shape cannot be changed after initialization")),
            });
        }

        // Update configuration
        self.config = config;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::TensorOps;

    #[test]
    fn test_layer_norm_creation() {
        let config = LayerNormConfig::new(vec![512]);
        let layer = LayerNorm::<f32>::new(config).unwrap();

        assert_eq!(layer.normalized_shape(), &[512]);
        assert_eq!(layer.eps(), 1e-5);
        assert!(layer.elementwise_affine());
        assert_eq!(layer.parameter_count(), 512 * 2); // weight + bias
    }

    #[test]
    fn test_layer_norm_no_affine() {
        let config = LayerNormConfig::new(vec![256])
            .with_elementwise_affine(false);
        let layer = LayerNorm::<f32>::new(config).unwrap();

        assert!(!layer.elementwise_affine());
        assert_eq!(layer.parameter_count(), 0);
        assert!(layer.weight().is_none());
        assert!(layer.bias().is_none());
    }

    #[test]
    fn test_layer_norm_forward() {
        let config = LayerNormConfig::new(vec![4]);
        let mut layer = LayerNorm::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::Ones).unwrap();

        // Create input: batch_size=2, features=4
        let input_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 4])).unwrap();

        let output = layer.forward(input).unwrap();

        // Check output shape
        assert_eq!(output.shape().dims(), &[2, 4]);

        // Check that output is normalized (mean ≈ 0, std ≈ 1)
        let output_data = output.to_vec();

        // First batch: [1, 2, 3, 4] -> mean=2.5, std≈1.29
        let first_batch = &output_data[0..4];
        let mean1: f32 = first_batch.iter().sum::<f32>() / 4.0;
        assert!((mean1).abs() < 1e-6); // Should be close to 0

        // Second batch: [5, 6, 7, 8] -> mean=6.5, std≈1.29
        let second_batch = &output_data[4..8];
        let mean2: f32 = second_batch.iter().sum::<f32>() / 4.0;
        assert!((mean2).abs() < 1e-6); // Should be close to 0
    }

    #[test]
    fn test_layer_norm_3d_input() {
        let config = LayerNormConfig::new(vec![3]);
        let mut layer = LayerNorm::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::Ones).unwrap();

        // Create 3D input: (batch=2, seq_len=2, features=3)
        let input_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 2, 3])).unwrap();

        let output = layer.forward(input).unwrap();

        // Check output shape
        assert_eq!(output.shape().dims(), &[2, 2, 3]);

        // Each sequence position should be normalized independently
        let output_data = output.to_vec();
    }

    #[test]
    fn test_rms_norm_creation() {
        let config = RMSNormConfig::new(vec![512]);
        let layer = RMSNorm::<f32>::new(config).unwrap();

        assert_eq!(layer.normalized_shape(), &[512]);
        assert_eq!(layer.eps(), 1e-6);
        assert!(layer.elementwise_affine());
        assert_eq!(layer.parameter_count(), 512); // only weight, no bias
    }

    #[test]
    fn test_rms_norm_no_affine() {
        let config = RMSNormConfig::new(vec![256])
            .with_elementwise_affine(false);
        let layer = RMSNorm::<f32>::new(config).unwrap();

        assert!(!layer.elementwise_affine());
        assert_eq!(layer.parameter_count(), 0);
        assert!(layer.weight().is_none());
    }

    #[test]
    fn test_rms_norm_forward() {
        let config = RMSNormConfig::new(vec![4]);
        let mut layer = RMSNorm::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::Ones).unwrap();

        // Create input: batch_size=2, features=4
        let input_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 4])).unwrap();

        let output = layer.forward(input).unwrap();

        // Check output shape
        assert_eq!(output.shape().dims(), &[2, 4]);

        // Check that output values are reasonable
        let output_data = output.to_vec();
        assert_eq!(output_data.len(), 8);

        // RMS normalization should scale values appropriately
        for &val in &output_data {
            assert!(val.is_finite());
            assert!(!val.is_nan());
        }
    }

    #[test]
    fn test_rms_norm_3d_input() {
        let config = RMSNormConfig::new(vec![3]);
        let mut layer = RMSNorm::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::Ones).unwrap();

        // Create 3D input: (batch=2, seq_len=2, features=3)
        let input_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 2, 3])).unwrap();

        let output = layer.forward(input).unwrap();

        // Check output shape
        assert_eq!(output.shape().dims(), &[2, 2, 3]);

        // Each sequence position should be normalized independently
        let output_data = output.to_vec();
        assert_eq!(output_data.len(), 12);

        // All values should be finite and not NaN
        for &val in &output_data {
            assert!(val.is_finite());
            assert!(!val.is_nan());
        }
    }

    #[test]
    fn test_rms_norm_vs_layer_norm() {
        // Compare RMSNorm and LayerNorm behavior on the same input
        let input_data = vec![1.0, 2.0, 3.0, 4.0];
        let input = CpuTensor::from_data(input_data.clone(), Shape::new(vec![1, 4])).unwrap();

        // RMSNorm
        let rms_config = RMSNormConfig::new(vec![4]).with_elementwise_affine(false);
        let rms_layer = RMSNorm::<f32>::new(rms_config).unwrap();
        let rms_output = rms_layer.forward(input.clone()).unwrap();

        // LayerNorm without affine
        let ln_config = LayerNormConfig::new(vec![4]).with_elementwise_affine(false);
        let ln_layer = LayerNorm::<f32>::new(ln_config).unwrap();
        let ln_output = ln_layer.forward(input).unwrap();

        let rms_data = rms_output.to_vec();
        let ln_data = ln_output.to_vec();

        // Both should produce valid outputs but with different values
        // RMSNorm doesn't center the data, so results will be different
        assert_eq!(rms_data.len(), 4);
        assert_eq!(ln_data.len(), 4);

        // All values should be finite
        for (&rms_val, &ln_val) in rms_data.iter().zip(ln_data.iter()) {
            assert!(rms_val.is_finite());
            assert!(ln_val.is_finite());
            assert!(!rms_val.is_nan());
            assert!(!ln_val.is_nan());
        }
    }

    #[test]
    fn test_layer_norm_parameter_initialization() {
        let config = LayerNormConfig::new(vec![10]);
        let mut layer = LayerNorm::<f32>::new(config).unwrap();

        // Test parameter initialization
        layer.init_parameters(ParameterInit::Ones).unwrap();

        // Weight should be ones
        if let Some(weight) = layer.weight() {
            let weight_data = weight.to_vec();
            assert!(weight_data.iter().all(|&x| (x - 1.0).abs() < 1e-6));
        }

        // Bias should be zeros
        if let Some(bias) = layer.bias() {
            let bias_data = bias.to_vec();
            assert!(bias_data.iter().all(|&x| x.abs() < 1e-6));
        }
    }
}

/// Configuration for BatchNorm.
#[derive(Debug, Clone)]
pub struct BatchNormConfig {
    /// Number of features (channels).
    pub num_features: usize,
    /// Small constant for numerical stability.
    pub eps: f32,
    /// Momentum for running statistics update.
    pub momentum: f32,
    /// Whether to include learnable scale parameter.
    pub affine: bool,
    /// Whether to track running statistics.
    pub track_running_stats: bool,
}

impl BatchNormConfig {
    /// Create a new BatchNorm configuration.
    pub fn new(num_features: usize) -> Self {
        Self {
            num_features,
            eps: 1e-5,
            momentum: 0.1,
            affine: true,
            track_running_stats: true,
        }
    }

    /// Set epsilon value.
    pub fn with_eps(mut self, eps: f32) -> Self {
        self.eps = eps;
        self
    }

    /// Set momentum for running statistics.
    pub fn with_momentum(mut self, momentum: f32) -> Self {
        self.momentum = momentum;
        self
    }

    /// Set whether to use affine transformation.
    pub fn with_affine(mut self, affine: bool) -> Self {
        self.affine = affine;
        self
    }

    /// Set whether to track running statistics.
    pub fn with_track_running_stats(mut self, track_running_stats: bool) -> Self {
        self.track_running_stats = track_running_stats;
        self
    }
}

/// Batch Normalization layer.
///
/// BatchNorm normalizes the input across the batch dimension, computing
/// statistics over the batch and spatial dimensions while keeping the
/// channel dimension separate.
///
/// The formula is:
/// ```text
/// y = (x - mean) / sqrt(var + eps) * weight + bias
/// ```
///
/// where:
/// - `mean` and `var` are computed across batch and spatial dimensions
/// - `weight` and `bias` are learnable parameters (optional)
/// - `eps` is a small constant for numerical stability
///
/// During training, BatchNorm uses batch statistics and updates running statistics.
/// During inference, it uses the running statistics computed during training.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{BatchNorm, BatchNormConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create BatchNorm for 64 channels
/// let config = BatchNormConfig::new(64);
/// let mut layer = BatchNorm::new(config).unwrap();
///
/// // Initialize parameters
/// layer.init_parameters(ParameterInit::Ones).unwrap();
///
/// // Forward pass with NCHW format: [batch, channels, height, width]
/// let input = CpuTensor::randn(&Shape::new(vec![32, 64, 28, 28]), 0.0, 1.0).unwrap();
/// let output = layer.forward(input).unwrap();
/// assert_eq!(output.shape().dims(), &[32, 64, 28, 28]);
/// ```
#[derive(Debug, Clone)]
pub struct BatchNorm<T: Numeric> {
    /// Learnable scale parameter.
    weight: Option<CpuTensor<T>>,
    /// Learnable shift parameter.
    bias: Option<CpuTensor<T>>,
    /// Running mean for inference.
    running_mean: Option<CpuTensor<T>>,
    /// Running variance for inference.
    running_var: Option<CpuTensor<T>>,
    /// Number of batches tracked.
    num_batches_tracked: usize,
    /// Layer configuration.
    config: BatchNormConfig,
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> BatchNorm<T> {
    /// Create a new BatchNorm layer.
    pub fn new(config: BatchNormConfig) -> Result<Self, TensorError> {
        let feature_shape = Shape::new(vec![config.num_features]);

        // Create affine parameters if enabled
        let (weight, bias) = if config.affine {
            let weight = Some(CpuTensorFactory::ones(&feature_shape)?);
            let bias = Some(CpuTensorFactory::zeros(&feature_shape)?);
            (weight, bias)
        } else {
            (None, None)
        };

        // Create running statistics if tracking is enabled
        let (running_mean, running_var) = if config.track_running_stats {
            let running_mean = Some(CpuTensorFactory::zeros(&feature_shape)?);
            let running_var = Some(CpuTensorFactory::ones(&feature_shape)?);
            (running_mean, running_var)
        } else {
            (None, None)
        };

        Ok(Self {
            weight,
            bias,
            running_mean,
            running_var,
            num_batches_tracked: 0,
            config,
            training: true,
            _phantom: PhantomData,
        })
    }

    /// Get a reference to the weight parameter.
    pub fn weight(&self) -> Option<&CpuTensor<T>> {
        self.weight.as_ref()
    }

    /// Get a reference to the bias parameter.
    pub fn bias(&self) -> Option<&CpuTensor<T>> {
        self.bias.as_ref()
    }

    /// Get a reference to the running mean.
    pub fn running_mean(&self) -> Option<&CpuTensor<T>> {
        self.running_mean.as_ref()
    }

    /// Get a reference to the running variance.
    pub fn running_var(&self) -> Option<&CpuTensor<T>> {
        self.running_var.as_ref()
    }

    /// Get the number of features.
    pub fn num_features(&self) -> usize {
        self.config.num_features
    }

    /// Get the epsilon value.
    pub fn eps(&self) -> f32 {
        self.config.eps
    }

    /// Get the momentum value.
    pub fn momentum(&self) -> f32 {
        self.config.momentum
    }

    /// Check if affine transformation is enabled.
    pub fn affine(&self) -> bool {
        self.config.affine
    }

    /// Check if running statistics tracking is enabled.
    pub fn track_running_stats(&self) -> bool {
        self.config.track_running_stats
    }

    /// Compute batch statistics for training mode.
    fn compute_batch_stats(&self, input: &CpuTensor<T>) -> Result<(CpuTensor<T>, CpuTensor<T>), TensorError> {
        let input_shape = input.shape();
        let input_dims = input_shape.dims();

        // BatchNorm expects input in format [N, C, ...] where:
        // N = batch size, C = channels, ... = spatial dimensions
        if input_dims.len() < 2 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, self.config.num_features], // placeholder for batch size
                actual: input_dims.to_vec(),
                context: Some(ErrorContext::new("compute_batch_stats", "layers::norm::batch_norm")
                    .with_info("reason", "input must have at least 2 dimensions [N, C, ...]")),
            });
        }

        let batch_size = input_dims[0];
        let num_channels = input_dims[1];

        if num_channels != self.config.num_features {
            return Err(TensorError::ShapeMismatch {
                expected: vec![batch_size, self.config.num_features],
                actual: input_dims.to_vec(),
                context: Some(ErrorContext::new("compute_batch_stats", "layers::norm::batch_norm")
                    .with_info("reason", "channel dimension mismatch")),
            });
        }

        // Calculate total elements per channel (batch_size * spatial_dims)
        let spatial_size: usize = input_dims[2..].iter().product();
        let elements_per_channel = batch_size * spatial_size;

        let input_data = input.data();
        let mut channel_means = Vec::with_capacity(num_channels);
        let mut channel_vars = Vec::with_capacity(num_channels);

        // Compute mean and variance for each channel
        for c in 0..num_channels {
            let mut sum = T::ZERO;
            let mut sum_sq = T::ZERO;

            // Iterate over all elements in this channel across batch and spatial dims
            for n in 0..batch_size {
                for s in 0..spatial_size {
                    let idx = n * (num_channels * spatial_size) + c * spatial_size + s;
                    let val = input_data[idx];
                    sum = sum + val;
                    sum_sq = sum_sq + val * val;
                }
            }

            let count = T::from_f32(elements_per_channel as f32);
            let mean = sum / count;
            let var = (sum_sq / count) - (mean * mean);

            channel_means.push(mean);
            channel_vars.push(var);
        }

        let mean_tensor = CpuTensor::from_data(channel_means, Shape::new(vec![num_channels]))?;
        let var_tensor = CpuTensor::from_data(channel_vars, Shape::new(vec![num_channels]))?;

        Ok((mean_tensor, var_tensor))
    }

    /// Update running statistics during training.
    fn update_running_stats(&mut self, batch_mean: &CpuTensor<T>, batch_var: &CpuTensor<T>) -> Result<(), TensorError> {
        if !self.config.track_running_stats {
            return Ok(());
        }

        let momentum = T::from_f32(self.config.momentum);
        let one_minus_momentum = T::from_f32(1.0 - self.config.momentum);

        // Update running mean: running_mean = (1 - momentum) * running_mean + momentum * batch_mean
        if let Some(ref mut running_mean) = self.running_mean {
            let running_mean_data = running_mean.data().to_vec();
            let batch_mean_data = batch_mean.data();

            let updated_mean: Vec<T> = running_mean_data.iter().zip(batch_mean_data.iter())
                .map(|(&rm, &bm)| one_minus_momentum * rm + momentum * bm)
                .collect();

            let shape = running_mean.shape().clone();
            *running_mean = CpuTensor::from_data(updated_mean, shape)?;
        }

        // Update running variance: running_var = (1 - momentum) * running_var + momentum * batch_var
        if let Some(ref mut running_var) = self.running_var {
            let running_var_data = running_var.data().to_vec();
            let batch_var_data = batch_var.data();

            let updated_var: Vec<T> = running_var_data.iter().zip(batch_var_data.iter())
                .map(|(&rv, &bv)| one_minus_momentum * rv + momentum * bv)
                .collect();

            let shape = running_var.shape().clone();
            *running_var = CpuTensor::from_data(updated_var, shape)?;
        }

        self.num_batches_tracked += 1;
        Ok(())
    }
}

impl<T: Numeric> Layer<T> for BatchNorm<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the BatchNorm layer.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        let input_shape = input.shape();
        let input_dims = input_shape.dims();

        if input_dims.len() < 2 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, self.config.num_features],
                actual: input_dims.to_vec(),
                context: Some(ErrorContext::new("forward", "layers::norm::batch_norm")),
            });
        }

        let batch_size = input_dims[0];
        let num_channels = input_dims[1];
        let spatial_size: usize = input_dims[2..].iter().product();

        // Get mean and variance based on training mode
        let (mean, var) = if self.training {
            // Use batch statistics during training
            let (batch_mean, batch_var) = self.compute_batch_stats(&input)?;
            (batch_mean, batch_var)
        } else {
            // Use running statistics during inference
            if let (Some(ref running_mean), Some(ref running_var)) = (&self.running_mean, &self.running_var) {
                (running_mean.clone(), running_var.clone())
            } else {
                // Fallback to batch statistics if running stats not available
                let (batch_mean, batch_var) = self.compute_batch_stats(&input)?;
                (batch_mean, batch_var)
            }
        };

        // Normalize: (x - mean) / sqrt(var + eps)
        let input_data = input.data();
        let mean_data = mean.data();
        let var_data = var.data();
        let eps = T::from_f32(self.config.eps);

        let mut normalized_data = Vec::with_capacity(input_data.len());

        for n in 0..batch_size {
            for c in 0..num_channels {
                let channel_mean = mean_data[c];
                let channel_var = var_data[c];
                let channel_std = (channel_var + eps).sqrt();

                for s in 0..spatial_size {
                    let idx = n * (num_channels * spatial_size) + c * spatial_size + s;
                    let val = input_data[idx];
                    let normalized = (val - channel_mean) / channel_std;
                    normalized_data.push(normalized);
                }
            }
        }

        let mut output = CpuTensor::from_data(normalized_data, input_shape.clone())?;

        // Apply affine transformation if enabled
        if self.config.affine {
            if let (Some(ref weight), Some(ref bias)) = (&self.weight, &self.bias) {
                let weight_data = weight.data();
                let bias_data = bias.data();
                let output_data = output.data().to_vec();
                let mut scaled_data = Vec::with_capacity(output_data.len());

                for n in 0..batch_size {
                    for c in 0..num_channels {
                        let channel_weight = weight_data[c];
                        let channel_bias = bias_data[c];

                        for s in 0..spatial_size {
                            let idx = n * (num_channels * spatial_size) + c * spatial_size + s;
                            let val = output_data[idx];
                            let scaled = val * channel_weight + channel_bias;
                            scaled_data.push(scaled);
                        }
                    }
                }

                output = CpuTensor::from_data(scaled_data, input_shape.clone())?;
            }
        }

        Ok(output)
    }

    fn parameter_count(&self) -> usize {
        let feature_count = self.config.num_features;
        let mut count = 0;

        if self.weight.is_some() { count += feature_count; }
        if self.bias.is_some() { count += feature_count; }

        count
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

impl<T: Numeric> ParameterizedLayer<T> for BatchNorm<T> {
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), Self::Error> {
        // Initialize weight parameter
        if let Some(ref mut weight) = self.weight {
            match init_strategy {
                ParameterInit::Ones => {
                    let ones = CpuTensorFactory::ones(weight.shape())?;
                    *weight = ones;
                },
                ParameterInit::Zeros => {
                    let zeros = CpuTensorFactory::zeros(weight.shape())?;
                    *weight = zeros;
                },
                _ => {
                    // For BatchNorm, weight is typically initialized to ones
                    let ones = CpuTensorFactory::ones(weight.shape())?;
                    *weight = ones;
                }
            }
        }

        // Initialize bias parameter
        if let Some(ref mut bias) = self.bias {
            let zeros = CpuTensorFactory::zeros(bias.shape())?;
            *bias = zeros;
        }

        Ok(())
    }
}

impl<T: Numeric> ConfigurableLayer<T, BatchNormConfig> for BatchNorm<T> {
    fn new(config: BatchNormConfig) -> Result<Self, Self::Error> {
        Self::new(config)
    }

    fn config(&self) -> &BatchNormConfig {
        &self.config
    }

    fn update_config(&mut self, config: BatchNormConfig) -> Result<(), Self::Error> {
        // Check if the number of features is compatible
        if config.num_features != self.config.num_features {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.config.num_features],
                actual: vec![config.num_features],
                context: Some(ErrorContext::new("update_config", "layers::norm::batch_norm")
                    .with_info("reason", "num_features cannot be changed after initialization")),
            });
        }

        // Update configuration
        self.config = config;
        Ok(())
    }
}

/// Configuration for GroupNorm.
#[derive(Debug, Clone)]
pub struct GroupNormConfig {
    /// Number of groups to divide channels into.
    pub num_groups: usize,
    /// Number of channels.
    pub num_channels: usize,
    /// Small constant for numerical stability.
    pub eps: f32,
    /// Whether to include learnable scale and shift parameters.
    pub affine: bool,
}

impl GroupNormConfig {
    /// Create a new GroupNorm configuration.
    pub fn new(num_groups: usize, num_channels: usize) -> Result<Self, TensorError> {
        if num_channels % num_groups != 0 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![num_groups],
                actual: vec![num_channels],
                context: Some(ErrorContext::new("new", "layers::norm::group_norm")
                    .with_info("reason", "num_channels must be divisible by num_groups")),
            });
        }

        Ok(Self {
            num_groups,
            num_channels,
            eps: 1e-5,
            affine: true,
        })
    }

    /// Set epsilon value.
    pub fn with_eps(mut self, eps: f32) -> Self {
        self.eps = eps;
        self
    }

    /// Set whether to use affine transformation.
    pub fn with_affine(mut self, affine: bool) -> Self {
        self.affine = affine;
        self
    }
}

/// Group Normalization layer.
///
/// GroupNorm divides channels into groups and normalizes within each group.
/// It's a compromise between LayerNorm (group_size = 1) and InstanceNorm (num_groups = num_channels).
///
/// The formula is:
/// ```text
/// y = (x - mean) / sqrt(var + eps) * weight + bias
/// ```
///
/// where:
/// - `mean` and `var` are computed within each group across spatial dimensions
/// - `weight` and `bias` are learnable parameters (optional)
/// - `eps` is a small constant for numerical stability
///
/// GroupNorm is particularly useful for small batch sizes where BatchNorm
/// statistics become unreliable.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{GroupNorm, GroupNormConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create GroupNorm with 8 groups for 32 channels
/// let config = GroupNormConfig::new(8, 32).unwrap();
/// let mut layer = GroupNorm::new(config).unwrap();
///
/// // Initialize parameters
/// layer.init_parameters(ParameterInit::Ones).unwrap();
///
/// // Forward pass with NCHW format: [batch, channels, height, width]
/// let input = CpuTensor::randn(&Shape::new(vec![4, 32, 16, 16]), 0.0, 1.0).unwrap();
/// let output = layer.forward(input).unwrap();
/// assert_eq!(output.shape().dims(), &[4, 32, 16, 16]);
/// ```
#[derive(Debug, Clone)]
pub struct GroupNorm<T: Numeric> {
    /// Learnable scale parameter.
    weight: Option<CpuTensor<T>>,
    /// Learnable shift parameter.
    bias: Option<CpuTensor<T>>,
    /// Layer configuration.
    config: GroupNormConfig,
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> GroupNorm<T> {
    /// Create a new GroupNorm layer.
    pub fn new(config: GroupNormConfig) -> Result<Self, TensorError> {
        let channel_shape = Shape::new(vec![config.num_channels]);

        // Create affine parameters if enabled
        let (weight, bias) = if config.affine {
            let weight = Some(CpuTensorFactory::ones(&channel_shape)?);
            let bias = Some(CpuTensorFactory::zeros(&channel_shape)?);
            (weight, bias)
        } else {
            (None, None)
        };

        Ok(Self {
            weight,
            bias,
            config,
            training: false,
            _phantom: PhantomData,
        })
    }

    /// Get a reference to the weight parameter.
    pub fn weight(&self) -> Option<&CpuTensor<T>> {
        self.weight.as_ref()
    }

    /// Get a reference to the bias parameter.
    pub fn bias(&self) -> Option<&CpuTensor<T>> {
        self.bias.as_ref()
    }

    /// Get the number of groups.
    pub fn num_groups(&self) -> usize {
        self.config.num_groups
    }

    /// Get the number of channels.
    pub fn num_channels(&self) -> usize {
        self.config.num_channels
    }

    /// Get the epsilon value.
    pub fn eps(&self) -> f32 {
        self.config.eps
    }

    /// Check if affine transformation is enabled.
    pub fn affine(&self) -> bool {
        self.config.affine
    }

    /// Get the number of channels per group.
    pub fn channels_per_group(&self) -> usize {
        self.config.num_channels / self.config.num_groups
    }

    /// Compute group statistics.
    fn compute_group_stats(&self, input: &CpuTensor<T>) -> Result<(CpuTensor<T>, CpuTensor<T>), TensorError> {
        let input_shape = input.shape();
        let input_dims = input_shape.dims();

        // GroupNorm expects input in format [N, C, ...] where:
        // N = batch size, C = channels, ... = spatial dimensions
        if input_dims.len() < 2 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, self.config.num_channels],
                actual: input_dims.to_vec(),
                context: Some(ErrorContext::new("compute_group_stats", "layers::norm::group_norm")
                    .with_info("reason", "input must have at least 2 dimensions [N, C, ...]")),
            });
        }

        let batch_size = input_dims[0];
        let num_channels = input_dims[1];

        if num_channels != self.config.num_channels {
            return Err(TensorError::ShapeMismatch {
                expected: vec![batch_size, self.config.num_channels],
                actual: input_dims.to_vec(),
                context: Some(ErrorContext::new("compute_group_stats", "layers::norm::group_norm")
                    .with_info("reason", "channel dimension mismatch")),
            });
        }

        let spatial_size: usize = input_dims[2..].iter().product();
        let channels_per_group = self.channels_per_group();
        let input_data = input.data();

        let mut group_means = Vec::with_capacity(batch_size * self.config.num_groups);
        let mut group_vars = Vec::with_capacity(batch_size * self.config.num_groups);

        // Compute mean and variance for each group in each batch
        for n in 0..batch_size {
            for g in 0..self.config.num_groups {
                let mut sum = T::ZERO;
                let mut sum_sq = T::ZERO;
                let mut count = 0;

                // Iterate over channels in this group
                for c_in_group in 0..channels_per_group {
                    let c = g * channels_per_group + c_in_group;

                    // Iterate over spatial dimensions
                    for s in 0..spatial_size {
                        let idx = n * (num_channels * spatial_size) + c * spatial_size + s;
                        let val = input_data[idx];
                        sum = sum + val;
                        sum_sq = sum_sq + val * val;
                        count += 1;
                    }
                }

                let count_t = T::from_f32(count as f32);
                let mean = sum / count_t;
                let var = (sum_sq / count_t) - (mean * mean);

                group_means.push(mean);
                group_vars.push(var);
            }
        }

        let mean_shape = Shape::new(vec![batch_size, self.config.num_groups]);
        let var_shape = Shape::new(vec![batch_size, self.config.num_groups]);

        let mean_tensor = CpuTensor::from_data(group_means, mean_shape)?;
        let var_tensor = CpuTensor::from_data(group_vars, var_shape)?;

        Ok((mean_tensor, var_tensor))
    }
}

impl<T: Numeric> Layer<T> for GroupNorm<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the GroupNorm layer.
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        let input_shape = input.shape();
        let input_dims = input_shape.dims();

        if input_dims.len() < 2 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![0, self.config.num_channels],
                actual: input_dims.to_vec(),
                context: Some(ErrorContext::new("forward", "layers::norm::group_norm")),
            });
        }

        let batch_size = input_dims[0];
        let num_channels = input_dims[1];
        let spatial_size: usize = input_dims[2..].iter().product();
        let channels_per_group = self.channels_per_group();

        // Compute group statistics
        let (group_means, group_vars) = self.compute_group_stats(&input)?;
        let mean_data = group_means.data();
        let var_data = group_vars.data();

        // Normalize: (x - group_mean) / sqrt(group_var + eps)
        let input_data = input.data();
        let eps = T::from_f32(self.config.eps);
        let mut normalized_data = Vec::with_capacity(input_data.len());

        for n in 0..batch_size {
            for c in 0..num_channels {
                let group_idx = c / channels_per_group;
                let stats_idx = n * self.config.num_groups + group_idx;

                let group_mean = mean_data[stats_idx];
                let group_var = var_data[stats_idx];
                let group_std = (group_var + eps).sqrt();

                for s in 0..spatial_size {
                    let idx = n * (num_channels * spatial_size) + c * spatial_size + s;
                    let val = input_data[idx];
                    let normalized = (val - group_mean) / group_std;
                    normalized_data.push(normalized);
                }
            }
        }

        let mut output = CpuTensor::from_data(normalized_data, input_shape.clone())?;

        // Apply affine transformation if enabled
        if self.config.affine {
            if let (Some(ref weight), Some(ref bias)) = (&self.weight, &self.bias) {
                let weight_data = weight.data();
                let bias_data = bias.data();
                let output_data = output.data().to_vec();
                let mut scaled_data = Vec::with_capacity(output_data.len());

                for n in 0..batch_size {
                    for c in 0..num_channels {
                        let channel_weight = weight_data[c];
                        let channel_bias = bias_data[c];

                        for s in 0..spatial_size {
                            let idx = n * (num_channels * spatial_size) + c * spatial_size + s;
                            let val = output_data[idx];
                            let scaled = val * channel_weight + channel_bias;
                            scaled_data.push(scaled);
                        }
                    }
                }

                output = CpuTensor::from_data(scaled_data, input_shape.clone())?;
            }
        }

        Ok(output)
    }

    fn parameter_count(&self) -> usize {
        let channel_count = self.config.num_channels;
        let mut count = 0;

        if self.weight.is_some() { count += channel_count; }
        if self.bias.is_some() { count += channel_count; }

        count
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

impl<T: Numeric> ParameterizedLayer<T> for GroupNorm<T> {
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), Self::Error> {
        // Initialize weight parameter
        if let Some(ref mut weight) = self.weight {
            match init_strategy {
                ParameterInit::Ones => {
                    let ones = CpuTensorFactory::ones(weight.shape())?;
                    *weight = ones;
                },
                ParameterInit::Zeros => {
                    let zeros = CpuTensorFactory::zeros(weight.shape())?;
                    *weight = zeros;
                },
                _ => {
                    // For GroupNorm, weight is typically initialized to ones
                    let ones = CpuTensorFactory::ones(weight.shape())?;
                    *weight = ones;
                }
            }
        }

        // Initialize bias parameter
        if let Some(ref mut bias) = self.bias {
            let zeros = CpuTensorFactory::zeros(bias.shape())?;
            *bias = zeros;
        }

        Ok(())
    }
}

impl<T: Numeric> ConfigurableLayer<T, GroupNormConfig> for GroupNorm<T> {
    fn new(config: GroupNormConfig) -> Result<Self, Self::Error> {
        Self::new(config)
    }

    fn config(&self) -> &GroupNormConfig {
        &self.config
    }

    fn update_config(&mut self, config: GroupNormConfig) -> Result<(), Self::Error> {
        // Check if the configuration is compatible
        if config.num_channels != self.config.num_channels {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.config.num_channels],
                actual: vec![config.num_channels],
                context: Some(ErrorContext::new("update_config", "layers::norm::group_norm")
                    .with_info("reason", "num_channels cannot be changed after initialization")),
            });
        }

        if config.num_groups != self.config.num_groups {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.config.num_groups],
                actual: vec![config.num_groups],
                context: Some(ErrorContext::new("update_config", "layers::norm::group_norm")
                    .with_info("reason", "num_groups cannot be changed after initialization")),
            });
        }

        // Update configuration
        self.config = config;
        Ok(())
    }
}

#[cfg(test)]
mod batch_group_norm_tests {
    use super::*;
    use crate::tensor::TensorOps;

    #[test]
    fn test_batch_norm_creation() {
        let config = BatchNormConfig::new(64);
        let layer = BatchNorm::<f32>::new(config).unwrap();

        assert_eq!(layer.num_features(), 64);
        assert_eq!(layer.eps(), 1e-5);
        assert_eq!(layer.momentum(), 0.1);
        assert!(layer.affine());
        assert!(layer.track_running_stats());
        assert_eq!(layer.parameter_count(), 128); // weight + bias
    }

    #[test]
    fn test_batch_norm_no_affine() {
        let config = BatchNormConfig::new(32)
            .with_affine(false);
        let layer = BatchNorm::<f32>::new(config).unwrap();

        assert!(!layer.affine());
        assert_eq!(layer.parameter_count(), 0);
        assert!(layer.weight().is_none());
        assert!(layer.bias().is_none());
    }

    #[test]
    fn test_batch_norm_forward() {
        let config = BatchNormConfig::new(3);
        let mut layer = BatchNorm::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::Ones).unwrap();

        // Create input: [batch=2, channels=3, height=2, width=2]
        let input_data = vec![
            1.0, 2.0, 3.0, 4.0,  // channel 0, batch 0
            5.0, 6.0, 7.0, 8.0,  // channel 1, batch 0
            9.0, 10.0, 11.0, 12.0, // channel 2, batch 0
            13.0, 14.0, 15.0, 16.0, // channel 0, batch 1
            17.0, 18.0, 19.0, 20.0, // channel 1, batch 1
            21.0, 22.0, 23.0, 24.0, // channel 2, batch 1
        ];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 3, 2, 2])).unwrap();

        let output = layer.forward(input).unwrap();

        // Check output shape
        assert_eq!(output.shape().dims(), &[2, 3, 2, 2]);

        // Check that output values are reasonable
        let output_data = output.to_vec();
        assert_eq!(output_data.len(), 24);

        // All values should be finite and not NaN
        for &val in &output_data {
            assert!(val.is_finite());
            assert!(!val.is_nan());
        }
    }

    #[test]
    fn test_group_norm_creation() {
        let config = GroupNormConfig::new(4, 16).unwrap();
        let layer = GroupNorm::<f32>::new(config).unwrap();

        assert_eq!(layer.num_groups(), 4);
        assert_eq!(layer.num_channels(), 16);
        assert_eq!(layer.channels_per_group(), 4);
        assert_eq!(layer.eps(), 1e-5);
        assert!(layer.affine());
        assert_eq!(layer.parameter_count(), 32); // weight + bias
    }

    #[test]
    fn test_group_norm_invalid_config() {
        // num_channels not divisible by num_groups
        let result = GroupNormConfig::new(3, 16);
        assert!(result.is_err());
    }

    #[test]
    fn test_group_norm_forward() {
        let config = GroupNormConfig::new(2, 4).unwrap();
        let mut layer = GroupNorm::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::Ones).unwrap();

        // Create input: [batch=1, channels=4, height=2, width=2]
        let input_data = vec![
            1.0, 2.0, 3.0, 4.0,   // channel 0
            5.0, 6.0, 7.0, 8.0,   // channel 1
            9.0, 10.0, 11.0, 12.0, // channel 2
            13.0, 14.0, 15.0, 16.0, // channel 3
        ];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![1, 4, 2, 2])).unwrap();

        let output = layer.forward(input).unwrap();

        // Check output shape
        assert_eq!(output.shape().dims(), &[1, 4, 2, 2]);

        // Check that output values are reasonable
        let output_data = output.to_vec();
        assert_eq!(output_data.len(), 16);

        // All values should be finite and not NaN
        for &val in &output_data {
            assert!(val.is_finite());
            assert!(!val.is_nan());
        }
    }

    #[test]
    fn test_group_norm_no_affine() {
        let config = GroupNormConfig::new(2, 8).unwrap()
            .with_affine(false);
        let layer = GroupNorm::<f32>::new(config).unwrap();

        assert!(!layer.affine());
        assert_eq!(layer.parameter_count(), 0);
        assert!(layer.weight().is_none());
        assert!(layer.bias().is_none());
    }
}
