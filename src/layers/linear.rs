//! Linear layer implementation.
//!
//! This module provides a linear (fully connected) layer implementation that performs
//! the transformation y = xW^T + b, where:
//! - x is the input tensor
//! - W is the weight matrix
//! - b is the optional bias vector
//! - y is the output tensor
//!
//! The layer supports various weight initialization strategies and efficient
//! batch processing for transformer architectures.

use std::marker::PhantomData;
use crate::error::{TensorError, ErrorContext};
use crate::tensor::{Tensor, TensorOps, TensorFactory, TensorView, Numeric, Shape, cpu::{CpuTensor, CpuTensorFactory}};
use crate::layers::{Layer, ConfigurableLayer, ParameterizedLayer, ParameterInit};

/// Configuration for linear layers.
#[derive(Debug, Clone)]
pub struct LinearConfig {
    /// Number of input features.
    pub in_features: usize,
    /// Number of output features.
    pub out_features: usize,
    /// Whether to include bias term.
    pub bias: bool,
    /// Weight initialization strategy.
    pub weight_init: ParameterInit,
    /// Bias initialization strategy (if bias is enabled).
    pub bias_init: ParameterInit,
}

impl LinearConfig {
    /// Create a new linear layer configuration.
    pub fn new(in_features: usize, out_features: usize) -> Self {
        Self {
            in_features,
            out_features,
            bias: true,
            weight_init: ParameterInit::XavierUniform,
            bias_init: ParameterInit::Zeros,
        }
    }

    /// Set whether to use bias.
    pub fn with_bias(mut self, bias: bool) -> Self {
        self.bias = bias;
        self
    }

    /// Set weight initialization strategy.
    pub fn with_weight_init(mut self, init: ParameterInit) -> Self {
        self.weight_init = init;
        self
    }

    /// Set bias initialization strategy.
    pub fn with_bias_init(mut self, init: ParameterInit) -> Self {
        self.bias_init = init;
        self
    }
}

/// Linear (fully connected) layer.
///
/// Performs the linear transformation y = xW^T + b where:
/// - Input shape: (..., in_features)
/// - Weight shape: (out_features, in_features)
/// - Bias shape: (out_features,) [optional]
/// - Output shape: (..., out_features)
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{Linear, LinearConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create a linear layer: 128 -> 64 features
/// let config = LinearConfig::new(128, 64);
/// let mut layer = Linear::new(config).unwrap();
///
/// // Initialize parameters
/// layer.init_parameters(ParameterInit::XavierUniform).unwrap();
///
/// // Forward pass
/// let input = CpuTensor::randn(&Shape::new(vec![32, 128]), 0.0, 1.0).unwrap();
/// let output = layer.forward(&input).unwrap();
/// assert_eq!(output.shape().dims(), &[32, 64]);
/// ```
#[derive(Debug, Clone)]
pub struct Linear<T: Numeric> {
    /// Weight matrix (out_features, in_features).
    weight: CpuTensor<T>,
    /// Optional bias vector (out_features,).
    bias: Option<CpuTensor<T>>,
    /// Layer configuration.
    config: LinearConfig,
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> Linear<T> {
    /// Create a new linear layer with uninitialized parameters.
    pub fn new(config: LinearConfig) -> Result<Self, TensorError> {
        // Create weight tensor (out_features, in_features)
        let weight_shape = Shape::new(vec![config.out_features, config.in_features]);
        let weight = CpuTensorFactory::zeros(&weight_shape)?;

        // Create bias tensor if enabled
        let bias = if config.bias {
            let bias_shape = Shape::new(vec![config.out_features]);
            Some(CpuTensorFactory::zeros(&bias_shape)?)
        } else {
            None
        };

        Ok(Self {
            weight,
            bias,
            config,
            training: false,
            _phantom: PhantomData,
        })
    }

    /// Create a linear layer from existing weight and bias tensors.
    pub fn from_weights(
        weight: CpuTensor<T>,
        bias: Option<CpuTensor<T>>,
    ) -> Result<Self, TensorError> {
        // Validate weight shape
        if weight.rank() != 2 {
            return Err(TensorError::InvalidDimension {
                dimension: weight.rank(),
                total_dims: 2,
                context: Some(ErrorContext::new("from_weights", "layers::linear")),
            });
        }

        let [out_features, in_features] = [weight.shape().dims()[0], weight.shape().dims()[1]];

        // Validate bias shape if present
        if let Some(ref bias_tensor) = bias {
            if bias_tensor.rank() != 1 || bias_tensor.shape().dims()[0] != out_features {
                return Err(TensorError::ShapeMismatch {
                    expected: vec![out_features],
                    actual: bias_tensor.shape().dims().to_vec(),
                    context: Some(ErrorContext::new("from_weights", "layers::linear")),
                });
            }
        }

        let config = LinearConfig {
            in_features,
            out_features,
            bias: bias.is_some(),
            weight_init: ParameterInit::Zeros, // Not used for pre-initialized weights
            bias_init: ParameterInit::Zeros,   // Not used for pre-initialized weights
        };

        Ok(Self {
            weight,
            bias,
            config,
            training: false,
            _phantom: PhantomData,
        })
    }

    /// Get a reference to the weight tensor.
    pub fn weight(&self) -> &CpuTensor<T> {
        &self.weight
    }

    /// Get a reference to the bias tensor (if present).
    pub fn bias(&self) -> Option<&CpuTensor<T>> {
        self.bias.as_ref()
    }

    /// Get the input feature count.
    pub fn in_features(&self) -> usize {
        self.config.in_features
    }

    /// Get the output feature count.
    pub fn out_features(&self) -> usize {
        self.config.out_features
    }

    /// Check if bias is enabled.
    pub fn has_bias(&self) -> bool {
        self.bias.is_some()
    }

    /// Initialize parameters using the specified strategy.
    fn init_tensor(tensor: &mut CpuTensor<T>, strategy: &ParameterInit, fan_in: usize, fan_out: usize) -> Result<(), TensorError> {
        let shape = tensor.shape();
        let size = shape.size();

        match strategy {
            ParameterInit::Zeros => {
                // Already initialized to zeros
                Ok(())
            },
            ParameterInit::Ones => {
                let ones = CpuTensorFactory::ones(shape)?;
                *tensor = ones;
                Ok(())
            },
            ParameterInit::XavierUniform => {
                let limit = (6.0 / (fan_in + fan_out) as f32).sqrt();
                // Use a better pseudo-random pattern for initialization
                let data: Vec<T> = (0..size)
                    .map(|i| {
                        let x = (i as f32 * 0.618033988749) % 1.0; // Golden ratio for better distribution
                        T::from_f32((x * 2.0 - 1.0) * limit)
                    })
                    .collect();
                *tensor = CpuTensor::from_data(data, shape.clone())?;
                Ok(())
            },
            ParameterInit::XavierNormal => {
                let std = (2.0 / (fan_in + fan_out) as f32).sqrt();
                // Use Box-Muller transform approximation for normal distribution
                let data: Vec<T> = (0..size)
                    .map(|i| {
                        let u1 = ((i as f32 * 0.618033988749) % 1.0).max(1e-8);
                        let u2 = ((i as f32 * 0.414213562373) % 1.0).max(1e-8);
                        let z = (-2.0 * u1.ln()).sqrt() * (2.0 * std::f32::consts::PI * u2).cos();
                        T::from_f32(z * std)
                    })
                    .collect();
                *tensor = CpuTensor::from_data(data, shape.clone())?;
                Ok(())
            },
            ParameterInit::KaimingUniform => {
                let limit = (6.0 / fan_in as f32).sqrt();
                let data: Vec<T> = (0..size)
                    .map(|i| {
                        let x = (i as f32 * 0.618033988749) % 1.0;
                        T::from_f32((x * 2.0 - 1.0) * limit)
                    })
                    .collect();
                *tensor = CpuTensor::from_data(data, shape.clone())?;
                Ok(())
            },
            ParameterInit::KaimingNormal => {
                let std = (2.0 / fan_in as f32).sqrt();
                let data: Vec<T> = (0..size)
                    .map(|i| {
                        let u1 = ((i as f32 * 0.618033988749) % 1.0).max(1e-8);
                        let u2 = ((i as f32 * 0.414213562373) % 1.0).max(1e-8);
                        let z = (-2.0 * u1.ln()).sqrt() * (2.0 * std::f32::consts::PI * u2).cos();
                        T::from_f32(z * std)
                    })
                    .collect();
                *tensor = CpuTensor::from_data(data, shape.clone())?;
                Ok(())
            },
            ParameterInit::Normal { mean, std } => {
                let data: Vec<T> = (0..size)
                    .map(|i| {
                        let u1 = ((i as f32 * 0.618033988749) % 1.0).max(1e-8);
                        let u2 = ((i as f32 * 0.414213562373) % 1.0).max(1e-8);
                        let z = (-2.0 * u1.ln()).sqrt() * (2.0 * std::f32::consts::PI * u2).cos();
                        T::from_f32(*mean + z * *std)
                    })
                    .collect();
                *tensor = CpuTensor::from_data(data, shape.clone())?;
                Ok(())
            },
            ParameterInit::Uniform { low, high } => {
                let data: Vec<T> = (0..size)
                    .map(|i| {
                        let x = (i as f32 * 0.618033988749) % 1.0;
                        T::from_f32(*low + x * (*high - *low))
                    })
                    .collect();
                *tensor = CpuTensor::from_data(data, shape.clone())?;
                Ok(())
            },
        }
    }
}

impl<T: Numeric> Layer<T> for Linear<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the linear layer.
    ///
    /// Computes y = xW^T + b where:
    /// - x: input tensor with shape (..., in_features)
    /// - W: weight matrix with shape (out_features, in_features)
    /// - b: bias vector with shape (out_features,) [optional]
    /// - y: output tensor with shape (..., out_features)
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        // Validate input shape
        if input.rank() == 0 {
            return Err(TensorError::InvalidDimension {
                dimension: 0,
                total_dims: 1,
                context: Some(ErrorContext::new("forward", "layers::linear")),
            });
        }

        let input_dims = input.shape().dims();
        let last_dim = input_dims[input_dims.len() - 1];

        if last_dim != self.config.in_features {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.config.in_features],
                actual: vec![last_dim],
                context: Some(ErrorContext::new("forward", "layers::linear")
                    .with_info("operation", "input feature dimension check")),
            });
        }

        // Reshape input for matrix multiplication if needed
        let (reshaped_input, original_shape) = if input.rank() > 2 {
            // Flatten all dimensions except the last one
            let batch_size: usize = input_dims[..input_dims.len() - 1].iter().product();
            let new_shape = Shape::new(vec![batch_size, last_dim]);
            let reshaped = TensorView::view_as(&input, &new_shape)?;
            (reshaped, Some(input.shape().clone()))
        } else {
            (input, None)
        };

        // Perform matrix multiplication: input @ weight.T
        // input: (batch_size, in_features)
        // weight: (out_features, in_features)
        // weight.T: (in_features, out_features)
        // result: (batch_size, out_features)
        let weight_transposed = Tensor::transpose(&self.weight, 1, 0)?;
        let mut output = reshaped_input.matmul(&weight_transposed)?;

        // Add bias if present
        if let Some(ref bias) = self.bias {
            // Manual broadcasting for bias addition
            // output shape: (batch_size, out_features)
            // bias shape: (out_features,)
            let output_data = output.data().to_vec();
            let bias_data = bias.data();
            let batch_size = output.shape().dims()[0];
            let out_features = output.shape().dims()[1];

            let mut result_data = Vec::with_capacity(output_data.len());
            for batch_idx in 0..batch_size {
                for feat_idx in 0..out_features {
                    let output_idx = batch_idx * out_features + feat_idx;
                    result_data.push(output_data[output_idx] + bias_data[feat_idx]);
                }
            }

            output = CpuTensor::from_data(result_data, output.shape().clone())?;
        }

        // Reshape output back to original batch dimensions if needed
        if let Some(orig_shape) = original_shape {
            let mut output_dims = orig_shape.dims().to_vec();
            let last_idx = output_dims.len() - 1;
            output_dims[last_idx] = self.config.out_features;
            let output_shape = Shape::new(output_dims);
            output = TensorView::view_as(&output, &output_shape)?;
        }

        Ok(output)
    }

    fn parameter_count(&self) -> usize {
        let weight_params = self.config.in_features * self.config.out_features;
        let bias_params = if self.config.bias { self.config.out_features } else { 0 };
        weight_params + bias_params
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

impl<T: Numeric> ConfigurableLayer<T, LinearConfig> for Linear<T> {
    fn new(config: LinearConfig) -> Result<Self, Self::Error> {
        Self::new(config)
    }

    fn config(&self) -> &LinearConfig {
        &self.config
    }

    fn update_config(&mut self, config: LinearConfig) -> Result<(), Self::Error> {
        // Check if the new configuration is compatible
        if config.in_features != self.config.in_features ||
           config.out_features != self.config.out_features {
            return Err(TensorError::InvalidDimension {
                dimension: config.in_features,
                total_dims: self.config.in_features,
                context: Some(ErrorContext::new("update_config", "layers::linear")
                    .with_info("reason", "feature dimensions cannot be changed after initialization")),
            });
        }

        // Update configuration
        self.config = config;

        // Handle bias changes
        if self.config.bias && self.bias.is_none() {
            // Add bias
            let bias_shape = Shape::new(vec![self.config.out_features]);
            self.bias = Some(CpuTensorFactory::zeros(&bias_shape)?);
        } else if !self.config.bias && self.bias.is_some() {
            // Remove bias
            self.bias = None;
        }

        Ok(())
    }
}

impl<T: Numeric> ParameterizedLayer<T> for Linear<T> {
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), Self::Error> {
        let fan_in = self.config.in_features;
        let fan_out = self.config.out_features;

        // Initialize weights
        Self::init_tensor(&mut self.weight, &init_strategy, fan_in, fan_out)?;

        // Initialize bias if present
        if let Some(ref mut bias) = self.bias {
            Self::init_tensor(bias, &self.config.bias_init, fan_in, fan_out)?;
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::{Shape, TensorOps};

    #[test]
    fn test_linear_creation() {
        let config = LinearConfig::new(10, 5);
        let layer = Linear::<f32>::new(config).unwrap();

        assert_eq!(layer.in_features(), 10);
        assert_eq!(layer.out_features(), 5);
        assert!(layer.has_bias());
        assert_eq!(layer.parameter_count(), 10 * 5 + 5); // weights + bias
    }

    #[test]
    fn test_linear_forward() {
        let config = LinearConfig::new(4, 3);
        let mut layer = Linear::<f32>::new(config).unwrap();

        // Initialize with simple values for testing
        layer.init_parameters(ParameterInit::Ones).unwrap();

        // Create input: batch_size=2, features=4
        let input_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 4])).unwrap();

        let output = layer.forward(input).unwrap();

        // Check output shape
        assert_eq!(output.shape().dims(), &[2, 3]);

        // With all weights=1 and bias=0, output should be sum of input features
        let output_data = output.to_vec();
        assert_eq!(output_data[0], 10.0); // 1+2+3+4 = 10
        assert_eq!(output_data[3], 26.0); // 5+6+7+8 = 26
    }

    #[test]
    fn test_linear_no_bias() {
        let config = LinearConfig::new(3, 2).with_bias(false);
        let layer = Linear::<f32>::new(config).unwrap();

        assert!(!layer.has_bias());
        assert_eq!(layer.parameter_count(), 3 * 2); // only weights
    }

    #[test]
    fn test_linear_3d_input() {
        let config = LinearConfig::new(4, 2);
        let mut layer = Linear::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::Zeros).unwrap();

        // Create 3D input: (batch=2, seq_len=3, features=4)
        let input_data = vec![1.0; 24]; // 2 * 3 * 4 = 24
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 3, 4])).unwrap();

        let output = layer.forward(input).unwrap();

        // Check output shape: (batch=2, seq_len=3, features=2)
        assert_eq!(output.shape().dims(), &[2, 3, 2]);
    }

    #[test]
    fn test_parameter_initialization() {
        let config = LinearConfig::new(10, 5);
        let mut layer = Linear::<f32>::new(config).unwrap();

        // Test Xavier uniform initialization
        layer.init_parameters(ParameterInit::XavierUniform).unwrap();

        // Weights should not be all zeros after initialization
        let weight_data = layer.weight().to_vec();
        assert!(weight_data.iter().any(|&x| x != 0.0));
    }
}
