//! Embedding layers.
//!
//! This module provides embedding layers for converting discrete tokens to dense vectors,
//! including token embeddings, positional encodings, and rotary position embeddings (RoPE).
//!
//! All embedding layers support:
//! - Efficient lookup operations
//! - Configurable embedding dimensions
//! - Various initialization strategies
//! - Gradient scaling and dropout (when implemented)

use std::marker::PhantomData;
use serde::{Serialize, Deserialize};
use crate::error::{TensorError, ErrorContext};
use crate::tensor::{Tensor, TensorOps, TensorFactory, Numeric, Shape, cpu::{CpuTensor, CpuTensorFactory}};
use crate::layers::{Layer, ConfigurableLayer, ParameterizedLayer, ParameterInit};

/// Configuration for token embedding layers.
#[derive(Debug, Clone)]
pub struct EmbeddingConfig {
    /// Size of the vocabulary.
    pub vocab_size: usize,
    /// Dimension of the embedding vectors.
    pub embedding_dim: usize,
    /// Index of the padding token (if any).
    pub padding_idx: Option<usize>,
    /// Maximum norm for embedding vectors.
    pub max_norm: Option<f32>,
    /// Norm type for max_norm constraint.
    pub norm_type: f32,
    /// Whether to scale gradients by frequency.
    pub scale_grad_by_freq: bool,
    /// Whether embeddings are sparse.
    pub sparse: bool,
}

impl EmbeddingConfig {
    /// Create a new embedding configuration.
    pub fn new(vocab_size: usize, embedding_dim: usize) -> Self {
        Self {
            vocab_size,
            embedding_dim,
            padding_idx: None,
            max_norm: None,
            norm_type: 2.0,
            scale_grad_by_freq: false,
            sparse: false,
        }
    }

    /// Set the padding token index.
    pub fn with_padding_idx(mut self, padding_idx: Option<usize>) -> Self {
        self.padding_idx = padding_idx;
        self
    }

    /// Set the maximum norm constraint.
    pub fn with_max_norm(mut self, max_norm: Option<f32>) -> Self {
        self.max_norm = max_norm;
        self
    }

    /// Set whether to scale gradients by frequency.
    pub fn with_scale_grad_by_freq(mut self, scale_grad_by_freq: bool) -> Self {
        self.scale_grad_by_freq = scale_grad_by_freq;
        self
    }
}

/// Token embedding layer.
///
/// Converts integer token indices to dense embedding vectors through a lookup table.
/// The embedding matrix has shape (vocab_size, embedding_dim).
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{Embedding, EmbeddingConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create embedding layer: vocab_size=1000, embedding_dim=512
/// let config = EmbeddingConfig::new(1000, 512);
/// let mut layer = Embedding::new(config).unwrap();
///
/// // Initialize parameters
/// layer.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.1 }).unwrap();
///
/// // Forward pass with token indices
/// let tokens = CpuTensor::from_data(vec![1, 5, 10, 2], Shape::new(vec![4])).unwrap();
/// let embeddings = layer.forward(tokens).unwrap();
/// assert_eq!(embeddings.shape().dims(), &[4, 512]);
/// ```
#[derive(Debug, Clone)]
pub struct Embedding<T: Numeric> {
    /// Embedding weight matrix (vocab_size, embedding_dim).
    weight: CpuTensor<T>,
    /// Layer configuration.
    config: EmbeddingConfig,
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> Embedding<T> {
    /// Create a new embedding layer.
    pub fn new(config: EmbeddingConfig) -> Result<Self, TensorError> {
        // Create embedding weight matrix
        let weight_shape = Shape::new(vec![config.vocab_size, config.embedding_dim]);
        let weight = CpuTensorFactory::zeros(&weight_shape)?;

        Ok(Self {
            weight,
            config,
            training: false,
            _phantom: PhantomData,
        })
    }

    /// Create an embedding layer from existing weights.
    pub fn from_weights(weight: CpuTensor<T>) -> Result<Self, TensorError> {
        // Validate weight shape
        if weight.rank() != 2 {
            return Err(TensorError::InvalidDimension {
                dimension: weight.rank(),
                total_dims: 2,
                context: Some(ErrorContext::new("from_weights", "layers::embedding")),
            });
        }

        let [vocab_size, embedding_dim] = [weight.shape().dims()[0], weight.shape().dims()[1]];
        let config = EmbeddingConfig::new(vocab_size, embedding_dim);

        Ok(Self {
            weight,
            config,
            training: false,
            _phantom: PhantomData,
        })
    }

    /// Get a reference to the embedding weight matrix.
    pub fn weight(&self) -> &CpuTensor<T> {
        &self.weight
    }

    /// Get the vocabulary size.
    pub fn vocab_size(&self) -> usize {
        self.config.vocab_size
    }

    /// Get the embedding dimension.
    pub fn embedding_dim(&self) -> usize {
        self.config.embedding_dim
    }

    /// Get the padding token index.
    pub fn padding_idx(&self) -> Option<usize> {
        self.config.padding_idx
    }
}

impl<T: Numeric> Layer<T> for Embedding<T> {
    type Input = Vec<usize>; // Token indices as usize vector
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the embedding layer.
    ///
    /// Takes token indices and returns corresponding embedding vectors.
    /// Input: Vec<usize> - flat vector of token indices
    /// Output shape: (num_tokens, embedding_dim)
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        let weight_data = self.weight.data();

        // Validate token indices
        for &token_idx in &input {
            if token_idx >= self.config.vocab_size {
                return Err(TensorError::IndexOutOfBounds {
                    index: token_idx,
                    size: self.config.vocab_size,
                    context: Some(ErrorContext::new("forward", "layers::embedding")),
                });
            }
        }

        // Perform embedding lookup
        let mut output_data = Vec::with_capacity(input.len() * self.config.embedding_dim);

        for &token_idx in &input {
            let start_idx = token_idx * self.config.embedding_dim;
            let end_idx = start_idx + self.config.embedding_dim;
            output_data.extend_from_slice(&weight_data[start_idx..end_idx]);
        }

        // Create output shape: (num_tokens, embedding_dim)
        let output_shape = Shape::new(vec![input.len(), self.config.embedding_dim]);

        CpuTensor::from_data(output_data, output_shape)
    }

    fn parameter_count(&self) -> usize {
        self.config.vocab_size * self.config.embedding_dim
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

impl<T: Numeric> ConfigurableLayer<T, EmbeddingConfig> for Embedding<T> {
    fn new(config: EmbeddingConfig) -> Result<Self, Self::Error> {
        Self::new(config)
    }

    fn config(&self) -> &EmbeddingConfig {
        &self.config
    }

    fn update_config(&mut self, config: EmbeddingConfig) -> Result<(), Self::Error> {
        // Check if the new configuration is compatible
        if config.vocab_size != self.config.vocab_size ||
           config.embedding_dim != self.config.embedding_dim {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.config.vocab_size, self.config.embedding_dim],
                actual: vec![config.vocab_size, config.embedding_dim],
                context: Some(ErrorContext::new("update_config", "layers::embedding")
                    .with_info("reason", "vocab_size and embedding_dim cannot be changed after initialization")),
            });
        }

        self.config = config;
        Ok(())
    }
}

impl<T: Numeric> ParameterizedLayer<T> for Embedding<T> {
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), Self::Error> {
        let fan_in = self.config.vocab_size;
        let fan_out = self.config.embedding_dim;

        // Initialize embedding weights
        let shape = self.weight.shape().clone();
        let size = shape.size();

        let data: Vec<T> = match init_strategy {
            ParameterInit::Normal { mean, std } => {
                (0..size).map(|i| {
                    let u1 = ((i as f32 * 0.618033988749) % 1.0).max(1e-8);
                    let u2 = ((i as f32 * 0.414213562373) % 1.0).max(1e-8);
                    let z = (-2.0 * u1.ln()).sqrt() * (2.0 * std::f32::consts::PI * u2).cos();
                    T::from_f32(mean + z * std)
                }).collect()
            },
            ParameterInit::XavierUniform => {
                let limit = (6.0 / (fan_in + fan_out) as f32).sqrt();
                (0..size).map(|i| {
                    let x = (i as f32 * 0.618033988749) % 1.0;
                    T::from_f32((x * 2.0 - 1.0) * limit)
                }).collect()
            },
            ParameterInit::XavierNormal => {
                let std = (2.0 / (fan_in + fan_out) as f32).sqrt();
                (0..size).map(|i| {
                    let u1 = ((i as f32 * 0.618033988749) % 1.0).max(1e-8);
                    let u2 = ((i as f32 * 0.414213562373) % 1.0).max(1e-8);
                    let z = (-2.0 * u1.ln()).sqrt() * (2.0 * std::f32::consts::PI * u2).cos();
                    T::from_f32(z * std)
                }).collect()
            },
            _ => {
                // Default to normal initialization for embeddings
                let std = 0.1;
                (0..size).map(|i| {
                    let u1 = ((i as f32 * 0.618033988749) % 1.0).max(1e-8);
                    let u2 = ((i as f32 * 0.414213562373) % 1.0).max(1e-8);
                    let z = (-2.0 * u1.ln()).sqrt() * (2.0 * std::f32::consts::PI * u2).cos();
                    T::from_f32(z * std)
                }).collect()
            }
        };

        self.weight = CpuTensor::from_data(data, shape)?;

        // Zero out padding token embedding if specified
        if let Some(padding_idx) = self.config.padding_idx {
            if padding_idx < self.config.vocab_size {
                let weight_data = self.weight.data().to_vec();
                let mut new_data = weight_data;
                let start_idx = padding_idx * self.config.embedding_dim;
                let end_idx = start_idx + self.config.embedding_dim;
                for i in start_idx..end_idx {
                    new_data[i] = T::ZERO;
                }
                let weight_shape = self.weight.shape().clone();
                self.weight = CpuTensor::from_data(new_data, weight_shape)?;
            }
        }

        Ok(())
    }
}

/// Position encoding types.
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum PositionEncodingType {
    /// Sinusoidal position encoding (Transformer original).
    Sinusoidal,
    /// Learnable position embedding.
    Learnable,
    /// Rotary Position Embedding (RoPE).
    Rotary,
}

/// Configuration for positional encoding.
#[derive(Debug, Clone)]
pub struct PositionalEncodingConfig {
    /// Type of position encoding.
    pub encoding_type: PositionEncodingType,
    /// Maximum sequence length.
    pub max_length: usize,
    /// Model dimension (embedding dimension).
    pub d_model: usize,
    /// Dropout probability (not implemented yet).
    pub dropout: f32,
}

impl PositionalEncodingConfig {
    /// Create a new positional encoding configuration.
    pub fn new(encoding_type: PositionEncodingType, max_length: usize, d_model: usize) -> Self {
        Self {
            encoding_type,
            max_length,
            d_model,
            dropout: 0.0,
        }
    }

    /// Set dropout probability.
    pub fn with_dropout(mut self, dropout: f32) -> Self {
        self.dropout = dropout;
        self
    }
}

/// Positional encoding layer.
///
/// Adds positional information to input embeddings using various encoding schemes.
/// The positional encoding is added to the input embeddings element-wise.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::layers::{PositionalEncoding, PositionalEncodingConfig, PositionEncodingType};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create sinusoidal positional encoding
/// let config = PositionalEncodingConfig::new(PositionEncodingType::Sinusoidal, 512, 256);
/// let layer = PositionalEncoding::new(config).unwrap();
///
/// // Forward pass
/// let input = CpuTensor::randn(&Shape::new(vec![32, 128, 256]), 0.0, 1.0).unwrap();
/// let output = layer.forward(input).unwrap();
/// assert_eq!(output.shape().dims(), &[32, 128, 256]);
/// ```
#[derive(Debug, Clone)]
pub struct PositionalEncoding<T: Numeric> {
    /// Positional encoding matrix or parameters.
    encoding: CpuTensor<T>,
    /// Layer configuration.
    config: PositionalEncodingConfig,
    /// Training mode flag.
    training: bool,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> PositionalEncoding<T> {
    /// Create a new positional encoding layer.
    pub fn new(config: PositionalEncodingConfig) -> Result<Self, TensorError> {
        let encoding = match config.encoding_type {
            PositionEncodingType::Sinusoidal => {
                Self::create_sinusoidal_encoding(config.max_length, config.d_model)?
            },
            PositionEncodingType::Learnable => {
                // Create learnable position embeddings
                let shape = Shape::new(vec![config.max_length, config.d_model]);
                CpuTensorFactory::zeros(&shape)?
            },
            PositionEncodingType::Rotary => {
                // RoPE doesn't use a fixed encoding matrix
                let shape = Shape::new(vec![config.max_length, config.d_model]);
                CpuTensorFactory::zeros(&shape)?
            },
        };

        Ok(Self {
            encoding,
            config,
            training: false,
            _phantom: PhantomData,
        })
    }

    /// Create sinusoidal positional encoding.
    fn create_sinusoidal_encoding(max_length: usize, d_model: usize) -> Result<CpuTensor<T>, TensorError> {
        let mut encoding_data = Vec::with_capacity(max_length * d_model);

        for pos in 0..max_length {
            for i in 0..d_model {
                let angle = pos as f32 / 10000.0_f32.powf(2.0 * (i / 2) as f32 / d_model as f32);
                let value = if i % 2 == 0 {
                    angle.sin()
                } else {
                    angle.cos()
                };
                encoding_data.push(T::from_f32(value));
            }
        }

        let shape = Shape::new(vec![max_length, d_model]);
        CpuTensor::from_data(encoding_data, shape)
    }

    /// Get the encoding type.
    pub fn encoding_type(&self) -> &PositionEncodingType {
        &self.config.encoding_type
    }

    /// Get the maximum sequence length.
    pub fn max_length(&self) -> usize {
        self.config.max_length
    }

    /// Get the model dimension.
    pub fn d_model(&self) -> usize {
        self.config.d_model
    }
}

impl<T: Numeric> Layer<T> for PositionalEncoding<T> {
    type Input = CpuTensor<T>;
    type Output = CpuTensor<T>;
    type Error = TensorError;

    /// Forward pass through the positional encoding layer.
    ///
    /// Adds positional encoding to the input embeddings.
    /// Input shape: (batch_size, seq_len, d_model)
    /// Output shape: (batch_size, seq_len, d_model)
    fn forward(&self, input: Self::Input) -> Result<Self::Output, Self::Error> {
        let input_shape = input.shape();
        let input_dims = input_shape.dims();

        // Validate input shape
        if input_dims.len() != 3 {
            return Err(TensorError::InvalidDimension {
                dimension: input_dims.len(),
                total_dims: 3,
                context: Some(ErrorContext::new("forward", "layers::embedding::positional_encoding")
                    .with_info("expected", "3D tensor (batch_size, seq_len, d_model)")),
            });
        }

        let [batch_size, seq_len, d_model] = [input_dims[0], input_dims[1], input_dims[2]];

        // Validate dimensions
        if d_model != self.config.d_model {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.config.d_model],
                actual: vec![d_model],
                context: Some(ErrorContext::new("forward", "layers::embedding::positional_encoding")
                    .with_info("dimension", "d_model")),
            });
        }

        if seq_len > self.config.max_length {
            return Err(TensorError::IndexOutOfBounds {
                index: seq_len,
                size: self.config.max_length,
                context: Some(ErrorContext::new("forward", "layers::embedding::positional_encoding")
                    .with_info("dimension", "sequence length exceeds max_length")),
            });
        }

        match self.config.encoding_type {
            PositionEncodingType::Sinusoidal | PositionEncodingType::Learnable => {
                // Add positional encoding to input
                let input_data = input.data();
                let encoding_data = self.encoding.data();
                let mut output_data = Vec::with_capacity(input_data.len());

                for batch_idx in 0..batch_size {
                    for pos_idx in 0..seq_len {
                        for dim_idx in 0..d_model {
                            let input_idx = batch_idx * seq_len * d_model + pos_idx * d_model + dim_idx;
                            let encoding_idx = pos_idx * d_model + dim_idx;

                            let input_val = input_data[input_idx];
                            let encoding_val = encoding_data[encoding_idx];
                            output_data.push(input_val + encoding_val);
                        }
                    }
                }

                CpuTensor::from_data(output_data, input_shape.clone())
            },
            PositionEncodingType::Rotary => {
                // RoPE is typically applied in attention layers, not as a separate layer
                // For now, just return the input unchanged
                // TODO: Implement RoPE application
                Ok(input)
            },
        }
    }

    fn parameter_count(&self) -> usize {
        match self.config.encoding_type {
            PositionEncodingType::Learnable => self.config.max_length * self.config.d_model,
            _ => 0, // Sinusoidal and RoPE have no learnable parameters
        }
    }

    fn training(&self) -> bool {
        self.training
    }

    fn set_training(&mut self, training: bool) {
        self.training = training;
    }
}

impl<T: Numeric> ConfigurableLayer<T, PositionalEncodingConfig> for PositionalEncoding<T> {
    fn new(config: PositionalEncodingConfig) -> Result<Self, Self::Error> {
        Self::new(config)
    }

    fn config(&self) -> &PositionalEncodingConfig {
        &self.config
    }

    fn update_config(&mut self, config: PositionalEncodingConfig) -> Result<(), Self::Error> {
        // Check if the new configuration is compatible
        if config.max_length != self.config.max_length ||
           config.d_model != self.config.d_model ||
           config.encoding_type != self.config.encoding_type {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.config.max_length, self.config.d_model],
                actual: vec![config.max_length, config.d_model],
                context: Some(ErrorContext::new("update_config", "layers::embedding::positional_encoding")
                    .with_info("reason", "core parameters cannot be changed after initialization")),
            });
        }

        self.config = config;
        Ok(())
    }
}

impl<T: Numeric> ParameterizedLayer<T> for PositionalEncoding<T> {
    fn init_parameters(&mut self, init_strategy: ParameterInit) -> Result<(), Self::Error> {
        match self.config.encoding_type {
            PositionEncodingType::Sinusoidal => {
                // Sinusoidal encoding is fixed, no initialization needed
                Ok(())
            },
            PositionEncodingType::Learnable => {
                // Initialize learnable position embeddings
                let shape = self.encoding.shape();
                let size = shape.size();

                let data: Vec<T> = match init_strategy {
                    ParameterInit::Normal { mean, std } => {
                        (0..size).map(|i| {
                            let u1 = ((i as f32 * 0.618033988749) % 1.0).max(1e-8);
                            let u2 = ((i as f32 * 0.414213562373) % 1.0).max(1e-8);
                            let z = (-2.0 * u1.ln()).sqrt() * (2.0 * std::f32::consts::PI * u2).cos();
                            T::from_f32(mean + z * std)
                        }).collect()
                    },
                    _ => {
                        // Default to small normal initialization
                        let std = 0.02;
                        (0..size).map(|i| {
                            let u1 = ((i as f32 * 0.618033988749) % 1.0).max(1e-8);
                            let u2 = ((i as f32 * 0.414213562373) % 1.0).max(1e-8);
                            let z = (-2.0 * u1.ln()).sqrt() * (2.0 * std::f32::consts::PI * u2).cos();
                            T::from_f32(z * std)
                        }).collect()
                    }
                };

                self.encoding = CpuTensor::from_data(data, shape.clone())?;
                Ok(())
            },
            PositionEncodingType::Rotary => {
                // RoPE doesn't have learnable parameters
                Ok(())
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::TensorOps;

    #[test]
    fn test_embedding_creation() {
        let config = EmbeddingConfig::new(1000, 512);
        let layer = Embedding::<f32>::new(config).unwrap();

        assert_eq!(layer.vocab_size(), 1000);
        assert_eq!(layer.embedding_dim(), 512);
        assert_eq!(layer.padding_idx(), None);
        assert_eq!(layer.parameter_count(), 1000 * 512);
    }

    #[test]
    fn test_embedding_forward() {
        let config = EmbeddingConfig::new(10, 4);
        let mut layer = Embedding::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.1 }).unwrap();

        // Create token indices
        let tokens = vec![0, 1, 2, 3];
        let embeddings = layer.forward(tokens).unwrap();

        // Check output shape
        assert_eq!(embeddings.shape().dims(), &[4, 4]);

        // Check that different tokens have different embeddings
        let embedding_data = embeddings.to_vec();
        let first_embedding = &embedding_data[0..4];
        let second_embedding = &embedding_data[4..8];
        assert_ne!(first_embedding, second_embedding);
    }

    #[test]
    fn test_embedding_batch_input() {
        let config = EmbeddingConfig::new(5, 3);
        let mut layer = Embedding::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.1 }).unwrap();

        // Create batch of token indices
        let tokens = vec![0, 1, 2, 3, 4, 0];
        let embeddings = layer.forward(tokens).unwrap();

        // Check output shape: (num_tokens=6, embedding_dim=3)
        assert_eq!(embeddings.shape().dims(), &[6, 3]);
    }

    #[test]
    fn test_embedding_out_of_bounds() {
        let config = EmbeddingConfig::new(5, 3);
        let mut layer = Embedding::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.1 }).unwrap();

        // Token index out of bounds
        let tokens = vec![0, 1, 5];
        let result = layer.forward(tokens);
        assert!(result.is_err());
    }

    #[test]
    fn test_embedding_padding_idx() {
        let config = EmbeddingConfig::new(10, 4).with_padding_idx(Some(0));
        let mut layer = Embedding::<f32>::new(config).unwrap();
        layer.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.1 }).unwrap();

        // Check that padding token embedding is zero
        let tokens = vec![0];
        let embeddings = layer.forward(tokens).unwrap();
        let embedding_data = embeddings.to_vec();

        // Padding token embedding should be all zeros
        assert!(embedding_data.iter().all(|&x| x.abs() < 1e-6));
    }

    #[test]
    fn test_sinusoidal_positional_encoding() {
        let config = PositionalEncodingConfig::new(PositionEncodingType::Sinusoidal, 100, 64);
        let layer = PositionalEncoding::<f32>::new(config).unwrap();

        assert_eq!(layer.encoding_type(), &PositionEncodingType::Sinusoidal);
        assert_eq!(layer.max_length(), 100);
        assert_eq!(layer.d_model(), 64);
        assert_eq!(layer.parameter_count(), 0); // No learnable parameters
    }

    #[test]
    fn test_positional_encoding_forward() {
        let config = PositionalEncodingConfig::new(PositionEncodingType::Sinusoidal, 10, 8);
        let layer = PositionalEncoding::<f32>::new(config).unwrap();

        // Create input: (batch_size=2, seq_len=5, d_model=8)
        let input_data = vec![0.1; 2 * 5 * 8];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![2, 5, 8])).unwrap();

        let output = layer.forward(input).unwrap();

        // Check output shape
        assert_eq!(output.shape().dims(), &[2, 5, 8]);

        // Output should be different from input (positional encoding added)
        let output_data = output.to_vec();
        assert!(output_data.iter().any(|&x| (x - 0.1).abs() > 1e-6));
    }

    #[test]
    fn test_learnable_positional_encoding() {
        let config = PositionalEncodingConfig::new(PositionEncodingType::Learnable, 50, 32);
        let mut layer = PositionalEncoding::<f32>::new(config).unwrap();

        assert_eq!(layer.encoding_type(), &PositionEncodingType::Learnable);
        assert_eq!(layer.parameter_count(), 50 * 32); // Has learnable parameters

        // Initialize parameters
        layer.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.02 }).unwrap();

        // Test forward pass
        let input_data = vec![0.0; 1 * 10 * 32];
        let input = CpuTensor::from_data(input_data, Shape::new(vec![1, 10, 32])).unwrap();

        let output = layer.forward(input).unwrap();
        assert_eq!(output.shape().dims(), &[1, 10, 32]);
    }

    #[test]
    fn test_positional_encoding_sequence_length_error() {
        let config = PositionalEncodingConfig::new(PositionEncodingType::Sinusoidal, 5, 4);
        let layer = PositionalEncoding::<f32>::new(config).unwrap();

        // Sequence length exceeds max_length
        let input_data = vec![0.1; 1 * 10 * 4]; // seq_len=10 > max_length=5
        let input = CpuTensor::from_data(input_data, Shape::new(vec![1, 10, 4])).unwrap();

        let result = layer.forward(input);
        assert!(result.is_err());
    }
}
