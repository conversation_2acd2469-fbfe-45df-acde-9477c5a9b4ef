//! Performance Analysis and Benchmarking Module
//!
//! This module provides comprehensive performance analysis tools for the Qilin inference engine.
//! It includes utilities for measuring, analyzing, and reporting performance characteristics
//! of neural network layers, tensor operations, and complete inference pipelines.

pub mod analyzer;
pub mod profiler;
pub mod reporter;
pub mod metrics;

use crate::error::TensorError;
use std::time::{Duration, Instant};
use std::collections::HashMap;

/// Performance measurement result for a single operation
#[derive(Debug, Clone)]
pub struct PerformanceMeasurement {
    /// Name of the operation being measured
    pub operation_name: String,
    /// Configuration or parameters used
    pub configuration: String,
    /// Number of iterations performed
    pub iterations: usize,
    /// Total time taken for all iterations
    pub total_duration: Duration,
    /// Average time per iteration
    pub avg_duration: Duration,
    /// Minimum time observed
    pub min_duration: Duration,
    /// Maximum time observed
    pub max_duration: Duration,
    /// Standard deviation of measurements
    pub std_deviation: Duration,
    /// Throughput (operations per second)
    pub throughput: f64,
    /// Memory usage (if measured)
    pub memory_usage: Option<usize>,
    /// Additional metrics
    pub additional_metrics: HashMap<String, f64>,
}

impl PerformanceMeasurement {
    /// Create a new performance measurement
    pub fn new(
        operation_name: String,
        configuration: String,
        durations: Vec<Duration>,
    ) -> Self {
        let iterations = durations.len();
        let total_duration = durations.iter().sum();
        let avg_duration: Duration = total_duration / iterations as u32;
        let min_duration = durations.iter().min().copied().unwrap_or_default();
        let max_duration = durations.iter().max().copied().unwrap_or_default();
        
        // Calculate standard deviation
        let avg_nanos = avg_duration.as_nanos() as f64;
        let variance = durations.iter()
            .map(|d| {
                let diff = d.as_nanos() as f64 - avg_nanos;
                diff * diff
            })
            .sum::<f64>() / iterations as f64;
        let std_deviation = Duration::from_nanos(variance.sqrt() as u64);
        
        // Calculate throughput (operations per second)
        let throughput = if avg_duration.as_secs_f64() > 0.0 {
            1.0 / avg_duration.as_secs_f64()
        } else {
            0.0
        };
        
        Self {
            operation_name,
            configuration,
            iterations,
            total_duration,
            avg_duration,
            min_duration,
            max_duration,
            std_deviation,
            throughput,
            memory_usage: None,
            additional_metrics: HashMap::new(),
        }
    }
    
    /// Add memory usage information
    pub fn with_memory_usage(mut self, memory_usage: usize) -> Self {
        self.memory_usage = Some(memory_usage);
        self
    }
    
    /// Add an additional metric
    pub fn with_metric(mut self, name: String, value: f64) -> Self {
        self.additional_metrics.insert(name, value);
        self
    }
    
    /// Get coefficient of variation (std_dev / mean)
    pub fn coefficient_of_variation(&self) -> f64 {
        if self.avg_duration.as_nanos() > 0 {
            self.std_deviation.as_nanos() as f64 / self.avg_duration.as_nanos() as f64
        } else {
            0.0
        }
    }
    
    /// Check if the measurement is stable (low coefficient of variation)
    pub fn is_stable(&self, threshold: f64) -> bool {
        self.coefficient_of_variation() < threshold
    }
}

/// Performance benchmark configuration
#[derive(Debug, Clone)]
pub struct BenchmarkConfig {
    /// Number of warmup iterations
    pub warmup_iterations: usize,
    /// Number of measurement iterations
    pub measurement_iterations: usize,
    /// Maximum time to spend on benchmarking
    pub max_duration: Duration,
    /// Minimum number of iterations for stable results
    pub min_iterations: usize,
    /// Coefficient of variation threshold for stability
    pub stability_threshold: f64,
    /// Whether to measure memory usage
    pub measure_memory: bool,
}

impl Default for BenchmarkConfig {
    fn default() -> Self {
        Self {
            warmup_iterations: 10,
            measurement_iterations: 100,
            max_duration: Duration::from_secs(30),
            min_iterations: 10,
            stability_threshold: 0.1, // 10% coefficient of variation
            measure_memory: false,
        }
    }
}

/// Simple performance benchmarker
pub struct PerformanceBenchmarker {
    config: BenchmarkConfig,
}

impl PerformanceBenchmarker {
    /// Create a new benchmarker with default configuration
    pub fn new() -> Self {
        Self {
            config: BenchmarkConfig::default(),
        }
    }
    
    /// Create a new benchmarker with custom configuration
    pub fn with_config(config: BenchmarkConfig) -> Self {
        Self { config }
    }
    
    /// Benchmark a closure that returns a Result
    pub fn benchmark<F, T, E>(
        &self,
        operation_name: &str,
        configuration: &str,
        mut operation: F,
    ) -> Result<PerformanceMeasurement, TensorError>
    where
        F: FnMut() -> Result<T, E>,
        E: std::fmt::Debug,
    {
        // Warmup phase
        for _ in 0..self.config.warmup_iterations {
            operation().map_err(|e| TensorError::ComputationError {
                message: format!("Warmup failed: {:?}", e),
                context: None,
            })?;
        }
        
        let mut durations = Vec::new();
        let benchmark_start = Instant::now();
        
        // Measurement phase
        for i in 0..self.config.measurement_iterations {
            // Check if we've exceeded max duration
            if benchmark_start.elapsed() > self.config.max_duration {
                break;
            }
            
            let start = Instant::now();
            operation().map_err(|e| TensorError::ComputationError {
                message: format!("Measurement failed: {:?}", e),
                context: None,
            })?;
            let duration = start.elapsed();
            durations.push(duration);
            
            // Check for early stability
            if i >= self.config.min_iterations && i % 10 == 0 {
                let temp_measurement = PerformanceMeasurement::new(
                    operation_name.to_string(),
                    configuration.to_string(),
                    durations.clone(),
                );
                if temp_measurement.is_stable(self.config.stability_threshold) {
                    break;
                }
            }
        }
        
        if durations.is_empty() {
            return Err(TensorError::ComputationError {
                message: "No measurements collected".to_string(),
                context: None,
            });
        }
        
        Ok(PerformanceMeasurement::new(
            operation_name.to_string(),
            configuration.to_string(),
            durations,
        ))
    }
    
    /// Benchmark a closure that doesn't return a Result
    pub fn benchmark_simple<F, T>(
        &self,
        operation_name: &str,
        configuration: &str,
        mut operation: F,
    ) -> PerformanceMeasurement
    where
        F: FnMut() -> T,
    {
        // Warmup phase
        for _ in 0..self.config.warmup_iterations {
            operation();
        }
        
        let mut durations = Vec::new();
        let benchmark_start = Instant::now();
        
        // Measurement phase
        for i in 0..self.config.measurement_iterations {
            // Check if we've exceeded max duration
            if benchmark_start.elapsed() > self.config.max_duration {
                break;
            }
            
            let start = Instant::now();
            operation();
            let duration = start.elapsed();
            durations.push(duration);
            
            // Check for early stability
            if i >= self.config.min_iterations && i % 10 == 0 {
                let temp_measurement = PerformanceMeasurement::new(
                    operation_name.to_string(),
                    configuration.to_string(),
                    durations.clone(),
                );
                if temp_measurement.is_stable(self.config.stability_threshold) {
                    break;
                }
            }
        }
        
        PerformanceMeasurement::new(
            operation_name.to_string(),
            configuration.to_string(),
            durations,
        )
    }
}

impl Default for PerformanceBenchmarker {
    fn default() -> Self {
        Self::new()
    }
}

/// Collection of performance measurements for analysis
#[derive(Debug, Clone)]
pub struct PerformanceReport {
    /// All measurements in this report
    pub measurements: Vec<PerformanceMeasurement>,
    /// Report generation timestamp
    pub timestamp: std::time::SystemTime,
    /// Report metadata
    pub metadata: HashMap<String, String>,
}

impl PerformanceReport {
    /// Create a new empty performance report
    pub fn new() -> Self {
        Self {
            measurements: Vec::new(),
            timestamp: std::time::SystemTime::now(),
            metadata: HashMap::new(),
        }
    }
    
    /// Add a measurement to the report
    pub fn add_measurement(&mut self, measurement: PerformanceMeasurement) {
        self.measurements.push(measurement);
    }
    
    /// Add metadata to the report
    pub fn add_metadata(&mut self, key: String, value: String) {
        self.metadata.insert(key, value);
    }
    
    /// Get measurements by operation name
    pub fn get_measurements_by_operation(&self, operation_name: &str) -> Vec<&PerformanceMeasurement> {
        self.measurements
            .iter()
            .filter(|m| m.operation_name == operation_name)
            .collect()
    }
    
    /// Get the fastest measurement for an operation
    pub fn get_fastest_measurement(&self, operation_name: &str) -> Option<&PerformanceMeasurement> {
        self.get_measurements_by_operation(operation_name)
            .into_iter()
            .min_by_key(|m| m.avg_duration)
    }
    
    /// Get the slowest measurement for an operation
    pub fn get_slowest_measurement(&self, operation_name: &str) -> Option<&PerformanceMeasurement> {
        self.get_measurements_by_operation(operation_name)
            .into_iter()
            .max_by_key(|m| m.avg_duration)
    }
    
    /// Calculate speedup between two configurations
    pub fn calculate_speedup(&self, operation_name: &str, baseline_config: &str, optimized_config: &str) -> Option<f64> {
        let baseline = self.measurements
            .iter()
            .find(|m| m.operation_name == operation_name && m.configuration == baseline_config)?;
        
        let optimized = self.measurements
            .iter()
            .find(|m| m.operation_name == operation_name && m.configuration == optimized_config)?;
        
        Some(baseline.avg_duration.as_secs_f64() / optimized.avg_duration.as_secs_f64())
    }
}

impl Default for PerformanceReport {
    fn default() -> Self {
        Self::new()
    }
}
