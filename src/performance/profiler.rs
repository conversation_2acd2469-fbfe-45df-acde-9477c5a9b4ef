//! Performance Profiler
//!
//! This module provides a comprehensive profiler for analyzing performance
//! characteristics of neural network operations and inference pipelines.

use super::{PerformanceMeasurement, PerformanceReport, BenchmarkConfig, PerformanceBenchmarker};
use super::metrics::{MetricsCollector, MemoryTracker, PerformanceRegistry};
use crate::layers::*;
use crate::tensor::{Tensor, Shape, TensorOps, TensorFactory, cpu::{CpuTensor, CpuTensorFactory}};
use crate::error::TensorError;
use std::time::{Duration, Instant};
use std::collections::HashMap;

/// Neural network layer profiler
pub struct LayerProfiler {
    benchmarker: PerformanceBenchmarker,
    memory_tracker: MemoryTracker,
    performance_registry: PerformanceRegistry,
}

impl LayerProfiler {
    /// Create a new layer profiler
    pub fn new() -> Self {
        Self {
            benchmarker: PerformanceBenchmarker::new(),
            memory_tracker: MemoryTracker::new(),
            performance_registry: PerformanceRegistry::new(),
        }
    }
    
    /// Create a profiler with custom benchmark configuration
    pub fn with_config(config: BenchmarkConfig) -> Self {
        Self {
            benchmarker: PerformanceBenchmarker::with_config(config),
            memory_tracker: MemoryTracker::new(),
            performance_registry: PerformanceRegistry::new(),
        }
    }
    
    /// Profile a linear layer across different configurations
    pub fn profile_linear_layer(&mut self, input_dims: &[usize], hidden_dims: &[usize]) -> Result<PerformanceReport, TensorError> {
        let mut report = PerformanceReport::new();
        report.add_metadata("profiler".to_string(), "LayerProfiler".to_string());
        report.add_metadata("layer_type".to_string(), "Linear".to_string());
        
        for &input_dim in input_dims {
            for &hidden_dim in hidden_dims {
                let batch_sizes = [1, 4, 16, 64];
                let seq_lens = [128, 512, 1024];
                
                for &batch_size in &batch_sizes {
                    for &seq_len in &seq_lens {
                        let config_name = format!("{}x{}x{}_to_{}", batch_size, seq_len, input_dim, hidden_dim);
                        
                        // Create input tensor
                        let input_shape = Shape::new(vec![batch_size, seq_len, input_dim]);
                        let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1)?;
                        
                        // Create and initialize linear layer
                        let linear_config = LinearConfig::new(input_dim, hidden_dim);
                        let mut linear: Linear<f32> = Linear::new(linear_config)?;
                        linear.init_parameters(ParameterInit::XavierUniform)?;
                        
                        // Benchmark forward pass
                        let measurement = self.benchmarker.benchmark(
                            "linear_forward",
                            &config_name,
                            || -> Result<CpuTensor<f32>, TensorError> {
                                linear.forward(input.clone())
                            },
                        )?;
                        
                        // Add throughput metrics
                        let elements_processed = batch_size * seq_len * input_dim;
                        let enhanced_measurement = measurement
                            .with_metric("elements_processed".to_string(), elements_processed as f64)
                            .with_metric("parameters".to_string(), linear.parameter_count() as f64)
                            .with_metric("flops".to_string(), (2 * input_dim * hidden_dim * batch_size * seq_len) as f64);
                        
                        report.add_measurement(enhanced_measurement);
                    }
                }
            }
        }
        
        Ok(report)
    }
    
    /// Profile activation functions
    pub fn profile_activation_functions(&mut self, input_shapes: &[Vec<usize>]) -> Result<PerformanceReport, TensorError> {
        let mut report = PerformanceReport::new();
        report.add_metadata("profiler".to_string(), "LayerProfiler".to_string());
        report.add_metadata("layer_type".to_string(), "Activation".to_string());
        
        let activation_types = [
            ActivationType::ReLU,
            ActivationType::GELU,
            ActivationType::Swish,
            ActivationType::Mish,
            ActivationType::Sigmoid,
            ActivationType::Tanh,
        ];
        
        for shape_dims in input_shapes {
            let shape = Shape::new(shape_dims.clone());
            let input = CpuTensorFactory::randn(&shape, 0.0, 1.0)?;
            let elements = shape_dims.iter().product::<usize>();
            
            for activation_type in &activation_types {
                let activation_name = format!("{:?}", activation_type).to_lowercase();
                let config_name = format!("{}_{}", activation_name, shape_dims.iter().map(|d| d.to_string()).collect::<Vec<_>>().join("x"));
                
                let activation_config = ActivationConfig::new(activation_type.clone());
                let activation: Activation<f32> = Activation::new(activation_config)?;
                
                let measurement = self.benchmarker.benchmark(
                    "activation_forward",
                    &config_name,
                    || -> Result<CpuTensor<f32>, TensorError> {
                        activation.forward(input.clone())
                    },
                )?;
                
                let enhanced_measurement = measurement
                    .with_metric("elements_processed".to_string(), elements as f64)
                    .with_metric("activation_type".to_string(), match activation_type {
                        ActivationType::ReLU => 0.0,
                        ActivationType::GELU => 1.0,
                        ActivationType::Swish => 2.0,
                        ActivationType::Mish => 3.0,
                        ActivationType::Sigmoid => 4.0,
                        ActivationType::Tanh => 5.0,
                        ActivationType::LeakyReLU { .. } => 6.0,
                        ActivationType::ELU { .. } => 7.0,
                        ActivationType::Composite { .. } => 8.0,
                        ActivationType::Custom { .. } => 9.0,
                    });
                
                report.add_measurement(enhanced_measurement);
            }
        }
        
        Ok(report)
    }
    
    /// Profile normalization layers
    pub fn profile_normalization_layers(&mut self, input_shapes: &[Vec<usize>]) -> Result<PerformanceReport, TensorError> {
        let mut report = PerformanceReport::new();
        report.add_metadata("profiler".to_string(), "LayerProfiler".to_string());
        report.add_metadata("layer_type".to_string(), "Normalization".to_string());
        
        for shape_dims in input_shapes {
            let shape = Shape::new(shape_dims.clone());
            let input = CpuTensorFactory::randn(&shape, 0.0, 1.0)?;
            let elements = shape_dims.iter().product::<usize>();
            let config_name = shape_dims.iter().map(|d| d.to_string()).collect::<Vec<_>>().join("x");
            
            // LayerNorm
            if shape_dims.len() >= 2 {
                let norm_dims = vec![shape_dims[shape_dims.len() - 1]]; // Normalize over last dimension
                let layernorm_config = LayerNormConfig::new(norm_dims);
                let mut layernorm: LayerNorm<f32> = LayerNorm::new(layernorm_config)?;
                layernorm.init_parameters(ParameterInit::Ones)?;
                
                let measurement = self.benchmarker.benchmark(
                    "layernorm_forward",
                    &config_name,
                    || -> Result<CpuTensor<f32>, TensorError> {
                        layernorm.forward(input.clone())
                    },
                )?;
                
                let enhanced_measurement = measurement
                    .with_metric("elements_processed".to_string(), elements as f64)
                    .with_metric("parameters".to_string(), layernorm.parameter_count() as f64);
                
                report.add_measurement(enhanced_measurement);
            }
            
            // RMSNorm
            if shape_dims.len() >= 2 {
                let norm_dims = vec![shape_dims[shape_dims.len() - 1]];
                let rmsnorm_config = RMSNormConfig::new(norm_dims);
                let mut rmsnorm: RMSNorm<f32> = RMSNorm::new(rmsnorm_config)?;
                rmsnorm.init_parameters(ParameterInit::Ones)?;
                
                let measurement = self.benchmarker.benchmark(
                    "rmsnorm_forward",
                    &config_name,
                    || -> Result<CpuTensor<f32>, TensorError> {
                        rmsnorm.forward(input.clone())
                    },
                )?;
                
                let enhanced_measurement = measurement
                    .with_metric("elements_processed".to_string(), elements as f64)
                    .with_metric("parameters".to_string(), rmsnorm.parameter_count() as f64);
                
                report.add_measurement(enhanced_measurement);
            }
        }
        
        Ok(report)
    }
    
    /// Profile feedforward networks
    pub fn profile_feedforward_networks(&mut self, input_dims: &[usize], hidden_dims: &[usize]) -> Result<PerformanceReport, TensorError> {
        let mut report = PerformanceReport::new();
        report.add_metadata("profiler".to_string(), "LayerProfiler".to_string());
        report.add_metadata("layer_type".to_string(), "FeedForward".to_string());
        
        for &input_dim in input_dims {
            for &hidden_dim in hidden_dims {
                let batch_sizes = [1, 8, 32];
                let seq_lens = [128, 512];
                
                for &batch_size in &batch_sizes {
                    for &seq_len in &seq_lens {
                        let input_shape = Shape::new(vec![batch_size, seq_len, input_dim]);
                        let input = CpuTensorFactory::randn(&input_shape, 0.0, 0.1)?;
                        
                        // Standard FFN
                        let ffn_config = FeedForwardConfig::new(input_dim, hidden_dim)
                            .with_activation(ActivationType::GELU);
                        let mut ffn: FeedForward<f32> = FeedForward::new(ffn_config)?;
                        ffn.init_parameters(ParameterInit::XavierUniform)?;
                        
                        let config_name = format!("standard_{}x{}x{}_to_{}", batch_size, seq_len, input_dim, hidden_dim);
                        
                        let measurement = self.benchmarker.benchmark(
                            "feedforward_forward",
                            &config_name,
                            || -> Result<CpuTensor<f32>, TensorError> {
                                ffn.forward(input.clone())
                            },
                        )?;
                        
                        let elements_processed = batch_size * seq_len * input_dim;
                        let flops = 2 * input_dim * hidden_dim * batch_size * seq_len + // First linear
                                   2 * hidden_dim * input_dim * batch_size * seq_len;   // Second linear
                        
                        let enhanced_measurement = measurement
                            .with_metric("elements_processed".to_string(), elements_processed as f64)
                            .with_metric("parameters".to_string(), ffn.parameter_count() as f64)
                            .with_metric("flops".to_string(), flops as f64);
                        
                        report.add_measurement(enhanced_measurement);
                        
                        // Gated FFN (SwiGLU)
                        let gated_ffn_config = FeedForwardConfig::new(input_dim, hidden_dim)
                            .with_gated(true)
                            .with_activation(ActivationType::Swish);
                        let mut gated_ffn: FeedForward<f32> = FeedForward::new(gated_ffn_config)?;
                        gated_ffn.init_parameters(ParameterInit::XavierUniform)?;
                        
                        let gated_config_name = format!("gated_{}x{}x{}_to_{}", batch_size, seq_len, input_dim, hidden_dim);
                        
                        let gated_measurement = self.benchmarker.benchmark(
                            "feedforward_forward",
                            &gated_config_name,
                            || -> Result<CpuTensor<f32>, TensorError> {
                                gated_ffn.forward(input.clone())
                            },
                        )?;
                        
                        let gated_flops = 3 * input_dim * hidden_dim * batch_size * seq_len + // Gate and up projections
                                         2 * hidden_dim * input_dim * batch_size * seq_len;   // Down projection
                        
                        let enhanced_gated_measurement = gated_measurement
                            .with_metric("elements_processed".to_string(), elements_processed as f64)
                            .with_metric("parameters".to_string(), gated_ffn.parameter_count() as f64)
                            .with_metric("flops".to_string(), gated_flops as f64);
                        
                        report.add_measurement(enhanced_gated_measurement);
                    }
                }
            }
        }
        
        Ok(report)
    }
    
    /// Profile embedding layers
    pub fn profile_embedding_layers(&mut self, vocab_sizes: &[usize], embed_dims: &[usize]) -> Result<PerformanceReport, TensorError> {
        let mut report = PerformanceReport::new();
        report.add_metadata("profiler".to_string(), "LayerProfiler".to_string());
        report.add_metadata("layer_type".to_string(), "Embedding".to_string());
        
        for &vocab_size in vocab_sizes {
            for &embed_dim in embed_dims {
                let batch_sizes = [1, 8, 32];
                let seq_lens = [128, 512, 1024];
                
                for &batch_size in &batch_sizes {
                    for &seq_len in &seq_lens {
                        let num_tokens = batch_size * seq_len;
                        let token_ids: Vec<usize> = (0..num_tokens).map(|i| i % vocab_size).collect();
                        
                        let embedding_config = EmbeddingConfig::new(vocab_size, embed_dim);
                        let mut embedding: Embedding<f32> = Embedding::new(embedding_config)?;
                        embedding.init_parameters(ParameterInit::Normal { mean: 0.0, std: 0.02 })?;
                        
                        let config_name = format!("vocab_{}_embed_{}_{}x{}", vocab_size, embed_dim, batch_size, seq_len);
                        
                        let measurement = self.benchmarker.benchmark(
                            "embedding_forward",
                            &config_name,
                            || -> Result<CpuTensor<f32>, TensorError> {
                                embedding.forward(token_ids.clone())
                            },
                        )?;
                        
                        let enhanced_measurement = measurement
                            .with_metric("tokens_processed".to_string(), num_tokens as f64)
                            .with_metric("vocab_size".to_string(), vocab_size as f64)
                            .with_metric("embed_dim".to_string(), embed_dim as f64)
                            .with_metric("parameters".to_string(), embedding.parameter_count() as f64);
                        
                        report.add_measurement(enhanced_measurement);
                    }
                }
            }
        }
        
        Ok(report)
    }
    
    /// Generate a comprehensive performance report for all layer types
    pub fn profile_all_layers(&mut self) -> Result<PerformanceReport, TensorError> {
        let mut combined_report = PerformanceReport::new();
        combined_report.add_metadata("profiler".to_string(), "LayerProfiler".to_string());
        combined_report.add_metadata("profile_type".to_string(), "comprehensive".to_string());
        
        // Profile linear layers
        let linear_report = self.profile_linear_layer(&[256, 512, 768], &[512, 1024, 2048])?;
        for measurement in linear_report.measurements {
            combined_report.add_measurement(measurement);
        }
        
        // Profile activation functions
        let activation_shapes = vec![
            vec![32, 512],
            vec![16, 1024, 768],
            vec![8, 2048, 1024],
        ];
        let activation_report = self.profile_activation_functions(&activation_shapes)?;
        for measurement in activation_report.measurements {
            combined_report.add_measurement(measurement);
        }
        
        // Profile normalization layers
        let norm_report = self.profile_normalization_layers(&activation_shapes)?;
        for measurement in norm_report.measurements {
            combined_report.add_measurement(measurement);
        }
        
        // Profile feedforward networks
        let ffn_report = self.profile_feedforward_networks(&[256, 512, 768], &[1024, 2048, 3072])?;
        for measurement in ffn_report.measurements {
            combined_report.add_measurement(measurement);
        }
        
        // Profile embedding layers
        let embedding_report = self.profile_embedding_layers(&[10000, 50000], &[256, 512, 768])?;
        for measurement in embedding_report.measurements {
            combined_report.add_measurement(measurement);
        }
        
        Ok(combined_report)
    }
}

impl Default for LayerProfiler {
    fn default() -> Self {
        Self::new()
    }
}
