//! Performance Metrics Collection
//!
//! This module provides utilities for collecting various performance metrics
//! including memory usage, CPU utilization, and custom application metrics.

use std::time::{Duration, Instant};
use std::collections::HashMap;

/// System performance metrics
#[derive(Debug, <PERSON>lone)]
pub struct SystemMetrics {
    /// Memory usage in bytes
    pub memory_usage: usize,
    /// Peak memory usage in bytes
    pub peak_memory_usage: usize,
    /// CPU time used
    pub cpu_time: Duration,
    /// Wall clock time
    pub wall_time: Duration,
    /// Number of allocations
    pub allocation_count: usize,
    /// Total bytes allocated
    pub total_allocated: usize,
    /// Custom metrics
    pub custom_metrics: HashMap<String, f64>,
}

impl Default for SystemMetrics {
    fn default() -> Self {
        Self {
            memory_usage: 0,
            peak_memory_usage: 0,
            cpu_time: Duration::default(),
            wall_time: Duration::default(),
            allocation_count: 0,
            total_allocated: 0,
            custom_metrics: HashMap::new(),
        }
    }
}

/// Performance metrics collector
pub struct MetricsCollector {
    start_time: Instant,
    start_memory: usize,
    peak_memory: usize,
    allocation_count: usize,
    total_allocated: usize,
    custom_metrics: HashMap<String, f64>,
}

impl MetricsCollector {
    /// Create a new metrics collector
    pub fn new() -> Self {
        Self {
            start_time: Instant::now(),
            start_memory: Self::get_current_memory_usage(),
            peak_memory: 0,
            allocation_count: 0,
            total_allocated: 0,
            custom_metrics: HashMap::new(),
        }
    }
    
    /// Start collecting metrics
    pub fn start(&mut self) {
        self.start_time = Instant::now();
        self.start_memory = Self::get_current_memory_usage();
        self.peak_memory = self.start_memory;
        self.allocation_count = 0;
        self.total_allocated = 0;
        self.custom_metrics.clear();
    }
    
    /// Record an allocation
    pub fn record_allocation(&mut self, size: usize) {
        self.allocation_count += 1;
        self.total_allocated += size;
        
        let current_memory = Self::get_current_memory_usage();
        if current_memory > self.peak_memory {
            self.peak_memory = current_memory;
        }
    }
    
    /// Add a custom metric
    pub fn add_custom_metric(&mut self, name: String, value: f64) {
        self.custom_metrics.insert(name, value);
    }
    
    /// Update peak memory if current usage is higher
    pub fn update_peak_memory(&mut self) {
        let current_memory = Self::get_current_memory_usage();
        if current_memory > self.peak_memory {
            self.peak_memory = current_memory;
        }
    }
    
    /// Finish collecting and return metrics
    pub fn finish(self) -> SystemMetrics {
        let wall_time = self.start_time.elapsed();
        let current_memory = Self::get_current_memory_usage();
        
        SystemMetrics {
            memory_usage: current_memory.saturating_sub(self.start_memory),
            peak_memory_usage: self.peak_memory.saturating_sub(self.start_memory),
            cpu_time: Duration::default(), // Would need platform-specific implementation
            wall_time,
            allocation_count: self.allocation_count,
            total_allocated: self.total_allocated,
            custom_metrics: self.custom_metrics,
        }
    }
    
    /// Get current memory usage (simplified implementation)
    fn get_current_memory_usage() -> usize {
        // This is a simplified implementation
        // In a real implementation, you would use platform-specific APIs
        // or crates like `memory-stats` to get actual memory usage
        0
    }
}

impl Default for MetricsCollector {
    fn default() -> Self {
        Self::new()
    }
}

/// Memory usage tracker for tensor operations
pub struct MemoryTracker {
    allocations: HashMap<*const u8, usize>,
    total_allocated: usize,
    peak_usage: usize,
    current_usage: usize,
}

impl MemoryTracker {
    /// Create a new memory tracker
    pub fn new() -> Self {
        Self {
            allocations: HashMap::new(),
            total_allocated: 0,
            peak_usage: 0,
            current_usage: 0,
        }
    }
    
    /// Track an allocation
    pub fn track_allocation(&mut self, ptr: *const u8, size: usize) {
        self.allocations.insert(ptr, size);
        self.total_allocated += size;
        self.current_usage += size;
        
        if self.current_usage > self.peak_usage {
            self.peak_usage = self.current_usage;
        }
    }
    
    /// Track a deallocation
    pub fn track_deallocation(&mut self, ptr: *const u8) {
        if let Some(size) = self.allocations.remove(&ptr) {
            self.current_usage = self.current_usage.saturating_sub(size);
        }
    }
    
    /// Get current memory usage
    pub fn current_usage(&self) -> usize {
        self.current_usage
    }
    
    /// Get peak memory usage
    pub fn peak_usage(&self) -> usize {
        self.peak_usage
    }
    
    /// Get total allocated memory
    pub fn total_allocated(&self) -> usize {
        self.total_allocated
    }
    
    /// Get number of active allocations
    pub fn active_allocations(&self) -> usize {
        self.allocations.len()
    }
    
    /// Reset all tracking
    pub fn reset(&mut self) {
        self.allocations.clear();
        self.total_allocated = 0;
        self.peak_usage = 0;
        self.current_usage = 0;
    }
}

impl Default for MemoryTracker {
    fn default() -> Self {
        Self::new()
    }
}

/// Performance counter for operations
#[derive(Debug, Clone)]
pub struct PerformanceCounter {
    /// Operation name
    pub name: String,
    /// Number of times the operation was called
    pub call_count: usize,
    /// Total time spent in the operation
    pub total_time: Duration,
    /// Minimum time observed
    pub min_time: Duration,
    /// Maximum time observed
    pub max_time: Duration,
    /// Total bytes processed
    pub total_bytes: usize,
}

impl PerformanceCounter {
    /// Create a new performance counter
    pub fn new(name: String) -> Self {
        Self {
            name,
            call_count: 0,
            total_time: Duration::default(),
            min_time: Duration::MAX,
            max_time: Duration::default(),
            total_bytes: 0,
        }
    }
    
    /// Record an operation
    pub fn record_operation(&mut self, duration: Duration, bytes_processed: usize) {
        self.call_count += 1;
        self.total_time += duration;
        self.total_bytes += bytes_processed;
        
        if duration < self.min_time {
            self.min_time = duration;
        }
        if duration > self.max_time {
            self.max_time = duration;
        }
    }
    
    /// Get average time per operation
    pub fn avg_time(&self) -> Duration {
        if self.call_count > 0 {
            self.total_time / self.call_count as u32
        } else {
            Duration::default()
        }
    }
    
    /// Get throughput in bytes per second
    pub fn throughput(&self) -> f64 {
        if self.total_time.as_secs_f64() > 0.0 {
            self.total_bytes as f64 / self.total_time.as_secs_f64()
        } else {
            0.0
        }
    }
    
    /// Get operations per second
    pub fn ops_per_second(&self) -> f64 {
        if self.total_time.as_secs_f64() > 0.0 {
            self.call_count as f64 / self.total_time.as_secs_f64()
        } else {
            0.0
        }
    }
    
    /// Reset the counter
    pub fn reset(&mut self) {
        self.call_count = 0;
        self.total_time = Duration::default();
        self.min_time = Duration::MAX;
        self.max_time = Duration::default();
        self.total_bytes = 0;
    }
}

/// Global performance counters registry
pub struct PerformanceRegistry {
    counters: HashMap<String, PerformanceCounter>,
}

impl PerformanceRegistry {
    /// Create a new performance registry
    pub fn new() -> Self {
        Self {
            counters: HashMap::new(),
        }
    }
    
    /// Get or create a counter
    pub fn get_counter(&mut self, name: &str) -> &mut PerformanceCounter {
        self.counters.entry(name.to_string())
            .or_insert_with(|| PerformanceCounter::new(name.to_string()))
    }
    
    /// Record an operation
    pub fn record_operation(&mut self, name: &str, duration: Duration, bytes_processed: usize) {
        self.get_counter(name).record_operation(duration, bytes_processed);
    }
    
    /// Get all counters
    pub fn get_all_counters(&self) -> &HashMap<String, PerformanceCounter> {
        &self.counters
    }
    
    /// Reset all counters
    pub fn reset_all(&mut self) {
        for counter in self.counters.values_mut() {
            counter.reset();
        }
    }
    
    /// Generate a summary report
    pub fn generate_summary(&self) -> String {
        let mut output = String::new();
        output.push_str("Performance Registry Summary:\n");
        output.push_str(&"=".repeat(40));
        output.push('\n');
        
        for (name, counter) in &self.counters {
            output.push_str(&format!(
                "{}: {} calls, avg: {:.2}μs, throughput: {:.2} MB/s\n",
                name,
                counter.call_count,
                counter.avg_time().as_micros(),
                counter.throughput() / 1_000_000.0
            ));
        }
        
        output
    }
}

impl Default for PerformanceRegistry {
    fn default() -> Self {
        Self::new()
    }
}
