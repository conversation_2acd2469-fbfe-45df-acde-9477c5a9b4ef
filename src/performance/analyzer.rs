//! Performance Analysis Tools
//!
//! This module provides advanced performance analysis capabilities including
//! statistical analysis, trend detection, and performance regression analysis.

use super::{PerformanceMeasurement, PerformanceReport};
use std::collections::HashMap;
use std::time::Duration;

/// Statistical summary of performance measurements
#[derive(Debug, Clone)]
pub struct PerformanceStatistics {
    /// Number of measurements
    pub count: usize,
    /// Mean duration
    pub mean: Duration,
    /// Median duration
    pub median: Duration,
    /// Standard deviation
    pub std_dev: Duration,
    /// Minimum duration
    pub min: Duration,
    /// Maximum duration
    pub max: Duration,
    /// 95th percentile
    pub p95: Duration,
    /// 99th percentile
    pub p99: Duration,
    /// Coefficient of variation
    pub coefficient_of_variation: f64,
}

/// Performance trend analysis result
#[derive(Debug, Clone)]
pub struct TrendAnalysis {
    /// Linear regression slope (change per measurement)
    pub slope: f64,
    /// R-squared correlation coefficient
    pub r_squared: f64,
    /// Trend direction
    pub trend: TrendDirection,
    /// Statistical significance
    pub is_significant: bool,
}

/// Direction of performance trend
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum TrendDirection {
    Improving,    // Getting faster
    Degrading,    // Getting slower
    Stable,       // No significant change
}

/// Performance comparison result
#[derive(Debug, Clone)]
pub struct ComparisonResult {
    /// Speedup factor (baseline_time / optimized_time)
    pub speedup: f64,
    /// Percentage improvement
    pub improvement_percent: f64,
    /// Statistical significance of the difference
    pub is_significant: bool,
    /// Confidence interval for the speedup
    pub confidence_interval: (f64, f64),
}

/// Advanced performance analyzer
pub struct PerformanceAnalyzer;

impl PerformanceAnalyzer {
    /// Calculate comprehensive statistics for a set of measurements
    pub fn calculate_statistics(measurements: &[PerformanceMeasurement]) -> Option<PerformanceStatistics> {
        if measurements.is_empty() {
            return None;
        }
        
        let mut durations: Vec<Duration> = measurements.iter().map(|m| m.avg_duration).collect();
        durations.sort();
        
        let count = durations.len();
        let sum: Duration = durations.iter().sum();
        let mean = sum / count as u32;
        
        let median = if count % 2 == 0 {
            (durations[count / 2 - 1] + durations[count / 2]) / 2
        } else {
            durations[count / 2]
        };
        
        let min = durations[0];
        let max = durations[count - 1];
        
        let p95_index = ((count as f64) * 0.95) as usize;
        let p95 = durations[p95_index.min(count - 1)];
        
        let p99_index = ((count as f64) * 0.99) as usize;
        let p99 = durations[p99_index.min(count - 1)];
        
        // Calculate standard deviation
        let mean_nanos = mean.as_nanos() as f64;
        let variance = durations.iter()
            .map(|d| {
                let diff = d.as_nanos() as f64 - mean_nanos;
                diff * diff
            })
            .sum::<f64>() / count as f64;
        let std_dev = Duration::from_nanos(variance.sqrt() as u64);
        
        let coefficient_of_variation = if mean_nanos > 0.0 {
            variance.sqrt() / mean_nanos
        } else {
            0.0
        };
        
        Some(PerformanceStatistics {
            count,
            mean,
            median,
            std_dev,
            min,
            max,
            p95,
            p99,
            coefficient_of_variation,
        })
    }
    
    /// Analyze performance trends over time
    pub fn analyze_trend(measurements: &[PerformanceMeasurement]) -> Option<TrendAnalysis> {
        if measurements.len() < 3 {
            return None;
        }
        
        let n = measurements.len() as f64;
        let x_values: Vec<f64> = (0..measurements.len()).map(|i| i as f64).collect();
        let y_values: Vec<f64> = measurements.iter().map(|m| m.avg_duration.as_nanos() as f64).collect();
        
        // Calculate linear regression
        let x_mean = x_values.iter().sum::<f64>() / n;
        let y_mean = y_values.iter().sum::<f64>() / n;
        
        let numerator: f64 = x_values.iter().zip(y_values.iter())
            .map(|(x, y)| (x - x_mean) * (y - y_mean))
            .sum();
        
        let denominator: f64 = x_values.iter()
            .map(|x| (x - x_mean).powi(2))
            .sum();
        
        if denominator == 0.0 {
            return None;
        }
        
        let slope = numerator / denominator;
        
        // Calculate R-squared
        let y_pred: Vec<f64> = x_values.iter().map(|x| y_mean + slope * (x - x_mean)).collect();
        let ss_res: f64 = y_values.iter().zip(y_pred.iter())
            .map(|(y, y_p)| (y - y_p).powi(2))
            .sum();
        let ss_tot: f64 = y_values.iter()
            .map(|y| (y - y_mean).powi(2))
            .sum();
        
        let r_squared = if ss_tot > 0.0 { 1.0 - (ss_res / ss_tot) } else { 0.0 };
        
        // Determine trend direction
        let trend = if slope.abs() < y_mean * 0.01 { // Less than 1% change per measurement
            TrendDirection::Stable
        } else if slope > 0.0 {
            TrendDirection::Degrading // Increasing time = worse performance
        } else {
            TrendDirection::Improving // Decreasing time = better performance
        };
        
        // Check statistical significance (simple threshold for R-squared)
        let is_significant = r_squared > 0.5 && slope.abs() > y_mean * 0.05;
        
        Some(TrendAnalysis {
            slope,
            r_squared,
            trend,
            is_significant,
        })
    }
    
    /// Compare performance between two configurations
    pub fn compare_performance(
        baseline: &[PerformanceMeasurement],
        optimized: &[PerformanceMeasurement],
    ) -> Option<ComparisonResult> {
        if baseline.is_empty() || optimized.is_empty() {
            return None;
        }
        
        let baseline_stats = Self::calculate_statistics(baseline)?;
        let optimized_stats = Self::calculate_statistics(optimized)?;
        
        let baseline_time = baseline_stats.mean.as_secs_f64();
        let optimized_time = optimized_stats.mean.as_secs_f64();
        
        if optimized_time == 0.0 {
            return None;
        }
        
        let speedup = baseline_time / optimized_time;
        let improvement_percent = ((baseline_time - optimized_time) / baseline_time) * 100.0;
        
        // Simple statistical significance test (Welch's t-test approximation)
        let baseline_variance = baseline_stats.std_dev.as_secs_f64().powi(2);
        let optimized_variance = optimized_stats.std_dev.as_secs_f64().powi(2);
        
        let pooled_se = (baseline_variance / baseline_stats.count as f64 + 
                        optimized_variance / optimized_stats.count as f64).sqrt();
        
        let t_statistic = (baseline_time - optimized_time).abs() / pooled_se;
        let is_significant = t_statistic > 2.0; // Rough approximation for p < 0.05
        
        // Simple confidence interval (95%)
        let margin_of_error = 1.96 * pooled_se / baseline_time;
        let confidence_interval = (
            speedup - margin_of_error,
            speedup + margin_of_error,
        );
        
        Some(ComparisonResult {
            speedup,
            improvement_percent,
            is_significant,
            confidence_interval,
        })
    }
    
    /// Identify performance outliers using IQR method
    pub fn identify_outliers(measurements: &[PerformanceMeasurement]) -> Vec<usize> {
        if measurements.len() < 4 {
            return Vec::new();
        }
        
        let mut durations: Vec<(Duration, usize)> = measurements.iter()
            .enumerate()
            .map(|(i, m)| (m.avg_duration, i))
            .collect();
        durations.sort_by_key(|(d, _)| *d);
        
        let n = durations.len();
        let q1_index = n / 4;
        let q3_index = 3 * n / 4;
        
        let q1 = durations[q1_index].0;
        let q3 = durations[q3_index].0;
        let iqr = q3 - q1;
        
        let lower_bound = q1 - iqr * 3 / 2; // 1.5 * IQR
        let upper_bound = q3 + iqr * 3 / 2;
        
        durations.iter()
            .filter_map(|(duration, index)| {
                if *duration < lower_bound || *duration > upper_bound {
                    Some(*index)
                } else {
                    None
                }
            })
            .collect()
    }
    
    /// Generate performance insights from a report
    pub fn generate_insights(report: &PerformanceReport) -> Vec<String> {
        let mut insights = Vec::new();
        
        // Group measurements by operation
        let mut operations: HashMap<String, Vec<&PerformanceMeasurement>> = HashMap::new();
        for measurement in &report.measurements {
            operations.entry(measurement.operation_name.clone())
                .or_insert_with(Vec::new)
                .push(measurement);
        }
        
        for (operation_name, measurements) in operations {
            let measurements_vec: Vec<PerformanceMeasurement> = measurements.iter().map(|m| (*m).clone()).collect();
            if let Some(stats) = Self::calculate_statistics(&measurements_vec) {
                // Stability insight
                if stats.coefficient_of_variation < 0.05 {
                    insights.push(format!("{}: Very stable performance (CV: {:.2}%)", 
                                        operation_name, stats.coefficient_of_variation * 100.0));
                } else if stats.coefficient_of_variation > 0.2 {
                    insights.push(format!("{}: High performance variability (CV: {:.2}%)", 
                                        operation_name, stats.coefficient_of_variation * 100.0));
                }
                
                // Performance level insight
                if stats.mean < Duration::from_millis(1) {
                    insights.push(format!("{}: Excellent performance (avg: {:.2}μs)", 
                                        operation_name, stats.mean.as_micros()));
                } else if stats.mean > Duration::from_millis(100) {
                    insights.push(format!("{}: Consider optimization (avg: {:.2}ms)", 
                                        operation_name, stats.mean.as_millis()));
                }
                
                // Outlier insight
                let outliers = Self::identify_outliers(&measurements_vec);
                if !outliers.is_empty() {
                    insights.push(format!("{}: {} outlier measurements detected", 
                                        operation_name, outliers.len()));
                }
            }
            
            // Trend analysis
            if let Some(trend) = Self::analyze_trend(&measurements_vec) {
                if trend.is_significant {
                    match trend.trend {
                        TrendDirection::Improving => {
                            insights.push(format!("{}: Performance improving over time (R²: {:.3})", 
                                                operation_name, trend.r_squared));
                        }
                        TrendDirection::Degrading => {
                            insights.push(format!("{}: Performance degrading over time (R²: {:.3})", 
                                                operation_name, trend.r_squared));
                        }
                        TrendDirection::Stable => {
                            insights.push(format!("{}: Stable performance over time", operation_name));
                        }
                    }
                }
            }
        }
        
        insights
    }
}
