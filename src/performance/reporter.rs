//! Performance Reporting and Visualization
//!
//! This module provides tools for generating comprehensive performance reports
//! in various formats including console output, JSON, and HTML.

use super::{PerformanceMeasurement, PerformanceReport};
use super::analyzer::{PerformanceAnalyzer, PerformanceStatistics, TrendAnalysis, ComparisonResult};
use std::collections::HashMap;
use std::fmt::Write;
use std::time::Duration;

/// Performance report formatter
pub struct PerformanceReporter;

impl PerformanceReporter {
    /// Generate a console-friendly performance report
    pub fn generate_console_report(report: &PerformanceReport) -> String {
        let mut output = String::new();
        
        writeln!(output, "🚀 Qilin Inference Engine Performance Report").unwrap();
        writeln!(output, "{}", "=".repeat(60)).unwrap();
        writeln!(output, "Generated: {:?}", report.timestamp).unwrap();
        writeln!(output).unwrap();
        
        // Metadata section
        if !report.metadata.is_empty() {
            writeln!(output, "📋 Report Metadata:").unwrap();
            for (key, value) in &report.metadata {
                writeln!(output, "  • {}: {}", key, value).unwrap();
            }
            writeln!(output).unwrap();
        }
        
        // Group measurements by operation
        let mut operations: HashMap<String, Vec<&PerformanceMeasurement>> = HashMap::new();
        for measurement in &report.measurements {
            operations.entry(measurement.operation_name.clone())
                .or_insert_with(Vec::new)
                .push(measurement);
        }
        
        // Performance summary for each operation
        writeln!(output, "📊 Performance Summary:").unwrap();
        writeln!(output, "{}", "-".repeat(60)).unwrap();
        
        for (operation_name, measurements) in &operations {
            writeln!(output, "\n🔧 Operation: {}", operation_name).unwrap();
            
            let measurements_vec: Vec<PerformanceMeasurement> = measurements.iter().map(|m| (*m).clone()).collect();
            if let Some(stats) = PerformanceAnalyzer::calculate_statistics(&measurements_vec) {
                Self::write_statistics_summary(&mut output, &stats);
            }
            
            // Configuration comparison
            if measurements.len() > 1 {
                writeln!(output, "\n  Configuration Comparison:").unwrap();
                let mut sorted_measurements = measurements.clone();
                sorted_measurements.sort_by_key(|m| m.avg_duration);
                
                let fastest = sorted_measurements[0];
                writeln!(output, "    🏆 Fastest: {} ({:.2}μs)", 
                        fastest.configuration, fastest.avg_duration.as_micros()).unwrap();
                
                if sorted_measurements.len() > 1 {
                    let slowest = sorted_measurements.last().unwrap();
                    let speedup = slowest.avg_duration.as_secs_f64() / fastest.avg_duration.as_secs_f64();
                    writeln!(output, "    🐌 Slowest: {} ({:.2}μs, {:.2}x slower)", 
                            slowest.configuration, slowest.avg_duration.as_micros(), speedup).unwrap();
                }
            }
        }
        
        // Performance insights
        let insights = PerformanceAnalyzer::generate_insights(report);
        if !insights.is_empty() {
            writeln!(output, "\n💡 Performance Insights:").unwrap();
            writeln!(output, "{}", "-".repeat(60)).unwrap();
            for insight in insights {
                writeln!(output, "  • {}", insight).unwrap();
            }
        }
        
        // Top performers
        writeln!(output, "\n🏅 Top Performers:").unwrap();
        writeln!(output, "{}", "-".repeat(60)).unwrap();
        
        let mut all_measurements = report.measurements.clone();
        all_measurements.sort_by_key(|m| m.avg_duration);
        
        for (i, measurement) in all_measurements.iter().take(5).enumerate() {
            let rank_emoji = match i {
                0 => "🥇",
                1 => "🥈", 
                2 => "🥉",
                _ => "🏅",
            };
            writeln!(output, "  {} {}: {} ({:.2}μs, {:.0} ops/sec)", 
                    rank_emoji, 
                    measurement.operation_name,
                    measurement.configuration,
                    measurement.avg_duration.as_micros(),
                    measurement.throughput).unwrap();
        }
        
        writeln!(output, "\n{}", "=".repeat(60)).unwrap();
        writeln!(output, "Report completed successfully! 🎉").unwrap();
        
        output
    }
    
    /// Generate a detailed JSON report
    pub fn generate_json_report(report: &PerformanceReport) -> Result<String, serde_json::Error> {
        #[derive(serde::Serialize)]
        struct JsonReport {
            timestamp: std::time::SystemTime,
            metadata: HashMap<String, String>,
            measurements: Vec<JsonMeasurement>,
            statistics: HashMap<String, JsonStatistics>,
            insights: Vec<String>,
        }
        
        #[derive(serde::Serialize)]
        struct JsonMeasurement {
            operation_name: String,
            configuration: String,
            iterations: usize,
            avg_duration_ns: u64,
            min_duration_ns: u64,
            max_duration_ns: u64,
            std_deviation_ns: u64,
            throughput: f64,
            memory_usage: Option<usize>,
            additional_metrics: HashMap<String, f64>,
        }
        
        #[derive(serde::Serialize)]
        struct JsonStatistics {
            count: usize,
            mean_ns: u64,
            median_ns: u64,
            std_dev_ns: u64,
            min_ns: u64,
            max_ns: u64,
            p95_ns: u64,
            p99_ns: u64,
            coefficient_of_variation: f64,
        }
        
        let json_measurements: Vec<JsonMeasurement> = report.measurements.iter().map(|m| {
            JsonMeasurement {
                operation_name: m.operation_name.clone(),
                configuration: m.configuration.clone(),
                iterations: m.iterations,
                avg_duration_ns: m.avg_duration.as_nanos() as u64,
                min_duration_ns: m.min_duration.as_nanos() as u64,
                max_duration_ns: m.max_duration.as_nanos() as u64,
                std_deviation_ns: m.std_deviation.as_nanos() as u64,
                throughput: m.throughput,
                memory_usage: m.memory_usage,
                additional_metrics: m.additional_metrics.clone(),
            }
        }).collect();
        
        // Calculate statistics for each operation
        let mut operations: HashMap<String, Vec<&PerformanceMeasurement>> = HashMap::new();
        for measurement in &report.measurements {
            operations.entry(measurement.operation_name.clone())
                .or_insert_with(Vec::new)
                .push(measurement);
        }
        
        let mut json_statistics = HashMap::new();
        for (operation_name, measurements) in operations {
            let measurements_vec: Vec<PerformanceMeasurement> = measurements.iter().map(|m| (*m).clone()).collect();
            if let Some(stats) = PerformanceAnalyzer::calculate_statistics(&measurements_vec) {
                json_statistics.insert(operation_name, JsonStatistics {
                    count: stats.count,
                    mean_ns: stats.mean.as_nanos() as u64,
                    median_ns: stats.median.as_nanos() as u64,
                    std_dev_ns: stats.std_dev.as_nanos() as u64,
                    min_ns: stats.min.as_nanos() as u64,
                    max_ns: stats.max.as_nanos() as u64,
                    p95_ns: stats.p95.as_nanos() as u64,
                    p99_ns: stats.p99.as_nanos() as u64,
                    coefficient_of_variation: stats.coefficient_of_variation,
                });
            }
        }
        
        let insights = PerformanceAnalyzer::generate_insights(report);
        
        let json_report = JsonReport {
            timestamp: report.timestamp,
            metadata: report.metadata.clone(),
            measurements: json_measurements,
            statistics: json_statistics,
            insights,
        };
        
        serde_json::to_string_pretty(&json_report)
    }
    
    /// Generate a CSV report for data analysis
    pub fn generate_csv_report(report: &PerformanceReport) -> String {
        let mut output = String::new();
        
        // CSV header
        writeln!(output, "operation_name,configuration,iterations,avg_duration_ns,min_duration_ns,max_duration_ns,std_deviation_ns,throughput,coefficient_of_variation").unwrap();
        
        // CSV data
        for measurement in &report.measurements {
            writeln!(output, "{},{},{},{},{},{},{},{:.2},{:.6}",
                    measurement.operation_name,
                    measurement.configuration,
                    measurement.iterations,
                    measurement.avg_duration.as_nanos(),
                    measurement.min_duration.as_nanos(),
                    measurement.max_duration.as_nanos(),
                    measurement.std_deviation.as_nanos(),
                    measurement.throughput,
                    measurement.coefficient_of_variation()).unwrap();
        }
        
        output
    }
    
    /// Write statistics summary to output
    fn write_statistics_summary(output: &mut String, stats: &PerformanceStatistics) {
        writeln!(output, "  📈 Statistics ({} measurements):", stats.count).unwrap();
        writeln!(output, "    • Mean: {:.2}μs", stats.mean.as_micros()).unwrap();
        writeln!(output, "    • Median: {:.2}μs", stats.median.as_micros()).unwrap();
        writeln!(output, "    • Min: {:.2}μs", stats.min.as_micros()).unwrap();
        writeln!(output, "    • Max: {:.2}μs", stats.max.as_micros()).unwrap();
        writeln!(output, "    • P95: {:.2}μs", stats.p95.as_micros()).unwrap();
        writeln!(output, "    • P99: {:.2}μs", stats.p99.as_micros()).unwrap();
        writeln!(output, "    • Std Dev: {:.2}μs", stats.std_dev.as_micros()).unwrap();
        writeln!(output, "    • CV: {:.2}%", stats.coefficient_of_variation * 100.0).unwrap();
    }
    
    /// Generate a comparison report between two performance reports
    pub fn generate_comparison_report(
        baseline_report: &PerformanceReport,
        optimized_report: &PerformanceReport,
    ) -> String {
        let mut output = String::new();
        
        writeln!(output, "🔄 Performance Comparison Report").unwrap();
        writeln!(output, "{}", "=".repeat(60)).unwrap();
        writeln!(output).unwrap();
        
        // Group measurements by operation for both reports
        let mut baseline_ops: HashMap<String, Vec<&PerformanceMeasurement>> = HashMap::new();
        for measurement in &baseline_report.measurements {
            baseline_ops.entry(measurement.operation_name.clone())
                .or_insert_with(Vec::new)
                .push(measurement);
        }
        
        let mut optimized_ops: HashMap<String, Vec<&PerformanceMeasurement>> = HashMap::new();
        for measurement in &optimized_report.measurements {
            optimized_ops.entry(measurement.operation_name.clone())
                .or_insert_with(Vec::new)
                .push(measurement);
        }
        
        // Compare operations that exist in both reports
        for (operation_name, baseline_measurements) in baseline_ops {
            if let Some(optimized_measurements) = optimized_ops.get(&operation_name) {
                writeln!(output, "🔧 Operation: {}", operation_name).unwrap();
                
                let baseline_vec: Vec<PerformanceMeasurement> = baseline_measurements.iter().map(|m| (*m).clone()).collect();
                let optimized_vec: Vec<PerformanceMeasurement> = optimized_measurements.iter().map(|m| (*m).clone()).collect();
                if let Some(comparison) = PerformanceAnalyzer::compare_performance(
                    &baseline_vec,
                    &optimized_vec,
                ) {
                    writeln!(output, "  📊 Comparison Results:").unwrap();
                    writeln!(output, "    • Speedup: {:.2}x", comparison.speedup).unwrap();
                    writeln!(output, "    • Improvement: {:.1}%", comparison.improvement_percent).unwrap();
                    writeln!(output, "    • Significant: {}", if comparison.is_significant { "Yes ✅" } else { "No ❌" }).unwrap();
                    writeln!(output, "    • 95% CI: [{:.2}x, {:.2}x]", 
                            comparison.confidence_interval.0, comparison.confidence_interval.1).unwrap();
                    
                    if comparison.speedup > 1.1 {
                        writeln!(output, "    🚀 Performance improved!").unwrap();
                    } else if comparison.speedup < 0.9 {
                        writeln!(output, "    ⚠️  Performance degraded!").unwrap();
                    } else {
                        writeln!(output, "    ➡️  Performance similar").unwrap();
                    }
                }
                writeln!(output).unwrap();
            }
        }
        
        output
    }
}
