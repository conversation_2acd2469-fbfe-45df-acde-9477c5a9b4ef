//! Tensor operations and data structures.
//!
//! This module provides:
//! - Multi-dimensional tensor abstraction
//! - Efficient memory layout and access patterns
//! - SIMD-optimized mathematical operations
//! - Device abstraction for CPU/GPU computing
//! - Type-safe shape validation

use std::fmt;
use std::ops::{Add, Mul, Sub, Div};
use crate::error::TensorError;

pub mod shape;
pub mod storage;
pub mod ops;
pub mod reduction;
pub mod advanced;
pub mod inplace;
pub mod memory_pool;
pub mod simd;
pub mod simd_nn;
pub mod parallel;
pub mod optimized;
pub mod performance_test;
pub mod cpu;
pub mod device;

#[cfg(feature = "cuda")]
pub mod cuda;

#[cfg(feature = "metal")]
pub mod metal;

pub use shape::*;
pub use storage::*;
pub use device::*;

/// Supported data types for tensors.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub enum DType {
    /// 32-bit floating point.
    F32,
    /// 64-bit floating point.
    F64,
    /// 16-bit floating point (half precision).
    #[cfg(feature = "f16")]
    F16,
    /// 32-bit signed integer.
    I32,
    /// 64-bit signed integer.
    I64,
}

impl fmt::Display for DType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            DType::F32 => write!(f, "f32"),
            DType::F64 => write!(f, "f64"),
            #[cfg(feature = "f16")]
            DType::F16 => write!(f, "f16"),
            DType::I32 => write!(f, "i32"),
            DType::I64 => write!(f, "i64"),
        }
    }
}

/// Trait for numeric types that can be used in tensors.
pub trait Numeric: 
    Copy + Clone + Send + Sync + 'static + 
    Add<Output = Self> + Sub<Output = Self> + 
    Mul<Output = Self> + Div<Output = Self> +
    PartialEq + PartialOrd + fmt::Debug + fmt::Display
{
    /// Zero value for this type.
    const ZERO: Self;
    /// One value for this type.
    const ONE: Self;
    /// Negative infinity (for floating point types).
    const NEG_INFINITY: Self;
    /// Positive infinity (for floating point types).
    const POS_INFINITY: Self;
    
    /// Get the data type enum for this numeric type.
    fn dtype() -> DType;
    
    /// Convert from f32.
    fn from_f32(value: f32) -> Self;
    
    /// Convert to f32.
    fn to_f32(self) -> f32;
    
    /// Square root function.
    fn sqrt(self) -> Self;
    
    /// Exponential function.
    fn exp(self) -> Self;
    
    /// Natural logarithm.
    fn ln(self) -> Self;
    
    /// Hyperbolic tangent.
    fn tanh(self) -> Self;
    
    /// Maximum of two values.
    fn max(self, other: Self) -> Self;
    
    /// Minimum of two values.
    fn min(self, other: Self) -> Self;
    
    /// Absolute value.
    fn abs(self) -> Self;

    // Trigonometric functions
    /// Sine function.
    fn sin(self) -> Self;
    /// Cosine function.
    fn cos(self) -> Self;
    /// Tangent function.
    fn tan(self) -> Self;
    /// Arcsine function.
    fn asin(self) -> Self;
    /// Arccosine function.
    fn acos(self) -> Self;
    /// Arctangent function.
    fn atan(self) -> Self;

    // Exponential and logarithmic functions
    /// Base-2 logarithm.
    fn log2(self) -> Self;
    /// Base-10 logarithm.
    fn log10(self) -> Self;
    /// Power function.
    fn pow(self, exp: Self) -> Self;

    // Hyperbolic functions
    /// Hyperbolic sine.
    fn sinh(self) -> Self;
    /// Hyperbolic cosine.
    fn cosh(self) -> Self;

    // Rounding functions
    /// Floor function.
    fn floor(self) -> Self;
    /// Ceiling function.
    fn ceil(self) -> Self;
    /// Round to nearest integer.
    fn round(self) -> Self;
    /// Truncate to integer.
    fn trunc(self) -> Self;

    // Comparison functions
    /// Clamp value between min and max.
    fn clamp(self, min: Self, max: Self) -> Self;
}

impl Numeric for f32 {
    const ZERO: Self = 0.0;
    const ONE: Self = 1.0;
    const NEG_INFINITY: Self = f32::NEG_INFINITY;
    const POS_INFINITY: Self = f32::INFINITY;

    fn dtype() -> DType { DType::F32 }
    fn from_f32(value: f32) -> Self { value }
    fn to_f32(self) -> f32 { self }
    fn sqrt(self) -> Self { self.sqrt() }
    fn exp(self) -> Self { self.exp() }
    fn ln(self) -> Self { self.ln() }
    fn tanh(self) -> Self { self.tanh() }
    fn max(self, other: Self) -> Self { self.max(other) }
    fn min(self, other: Self) -> Self { self.min(other) }
    fn abs(self) -> Self { self.abs() }

    // Trigonometric functions
    fn sin(self) -> Self { self.sin() }
    fn cos(self) -> Self { self.cos() }
    fn tan(self) -> Self { self.tan() }
    fn asin(self) -> Self { self.asin() }
    fn acos(self) -> Self { self.acos() }
    fn atan(self) -> Self { self.atan() }

    // Exponential and logarithmic functions
    fn log2(self) -> Self { self.log2() }
    fn log10(self) -> Self { self.log10() }
    fn pow(self, exp: Self) -> Self { self.powf(exp) }

    // Hyperbolic functions
    fn sinh(self) -> Self { self.sinh() }
    fn cosh(self) -> Self { self.cosh() }

    // Rounding functions
    fn floor(self) -> Self { self.floor() }
    fn ceil(self) -> Self { self.ceil() }
    fn round(self) -> Self { self.round() }
    fn trunc(self) -> Self { self.trunc() }

    // Comparison functions
    fn clamp(self, min: Self, max: Self) -> Self { self.clamp(min, max) }
}

impl Numeric for f64 {
    const ZERO: Self = 0.0;
    const ONE: Self = 1.0;
    const NEG_INFINITY: Self = f64::NEG_INFINITY;
    const POS_INFINITY: Self = f64::INFINITY;

    fn dtype() -> DType { DType::F64 }
    fn from_f32(value: f32) -> Self { value as f64 }
    fn to_f32(self) -> f32 { self as f32 }
    fn sqrt(self) -> Self { self.sqrt() }
    fn exp(self) -> Self { self.exp() }
    fn ln(self) -> Self { self.ln() }
    fn tanh(self) -> Self { self.tanh() }
    fn max(self, other: Self) -> Self { self.max(other) }
    fn min(self, other: Self) -> Self { self.min(other) }
    fn abs(self) -> Self { self.abs() }

    // Trigonometric functions
    fn sin(self) -> Self { self.sin() }
    fn cos(self) -> Self { self.cos() }
    fn tan(self) -> Self { self.tan() }
    fn asin(self) -> Self { self.asin() }
    fn acos(self) -> Self { self.acos() }
    fn atan(self) -> Self { self.atan() }

    // Exponential and logarithmic functions
    fn log2(self) -> Self { self.log2() }
    fn log10(self) -> Self { self.log10() }
    fn pow(self, exp: Self) -> Self { self.powf(exp) }

    // Hyperbolic functions
    fn sinh(self) -> Self { self.sinh() }
    fn cosh(self) -> Self { self.cosh() }

    // Rounding functions
    fn floor(self) -> Self { self.floor() }
    fn ceil(self) -> Self { self.ceil() }
    fn round(self) -> Self { self.round() }
    fn trunc(self) -> Self { self.trunc() }

    // Comparison functions
    fn clamp(self, min: Self, max: Self) -> Self { self.clamp(min, max) }
}

/// Core tensor trait defining the interface for all tensor implementations.
pub trait Tensor<T: Numeric>: Clone + Send + Sync {
    /// Error type for tensor operations.
    type Error: std::error::Error + Send + Sync + 'static;
    
    /// Get the shape of this tensor.
    fn shape(&self) -> &Shape;
    
    /// Get the data type of this tensor.
    fn dtype(&self) -> DType;
    
    /// Check if the tensor data is stored contiguously in memory.
    fn is_contiguous(&self) -> bool;
    
    /// Get the total number of elements in this tensor.
    fn size(&self) -> usize {
        self.shape().size()
    }
    
    /// Get the number of dimensions (rank) of this tensor.
    fn rank(&self) -> usize {
        self.shape().rank()
    }
    
    /// Reshape the tensor to a new shape.
    fn reshape(&self, shape: &Shape) -> Result<Self, Self::Error>;
    
    /// Transpose the tensor by swapping two dimensions.
    fn transpose(&self, dim1: usize, dim2: usize) -> Result<Self, Self::Error>;
    
    /// Permute the dimensions of the tensor.
    fn permute(&self, dims: &[usize]) -> Result<Self, Self::Error>;
    
    /// Remove dimensions of size 1.
    fn squeeze(&self, dim: Option<usize>) -> Result<Self, Self::Error>;
    
    /// Add a dimension of size 1.
    fn unsqueeze(&self, dim: usize) -> Result<Self, Self::Error>;
    
    /// Extract a slice of the tensor.
    fn slice(&self, ranges: &[std::ops::Range<usize>]) -> Result<Self, Self::Error>;
    
    /// Select elements along a dimension using indices.
    fn index_select(&self, dim: usize, indices: &[usize]) -> Result<Self, Self::Error>;
}

/// Trait for tensor mathematical operations.
///
/// This trait provides the core mathematical operations for tensors, including
/// element-wise arithmetic, matrix operations, reductions, and activation functions.
/// All operations are designed to be memory-safe and support broadcasting where appropriate.
///
/// # Broadcasting
///
/// Element-wise operations support broadcasting, allowing tensors of different but
/// compatible shapes to be combined. Broadcasting rules follow NumPy conventions:
/// - Dimensions are aligned from the rightmost dimension
/// - Missing dimensions are treated as size 1
/// - Dimensions of size 1 can be broadcast to any size
///
/// # Examples
///
/// ```rust
/// use qilin_inference::tensor::{CpuTensor, Shape, TensorOps};
///
/// let data1 = vec![1.0, 2.0, 3.0, 4.0];
/// let data2 = vec![2.0, 2.0, 2.0, 2.0];
/// let tensor1 = CpuTensor::from_data(data1, Shape::new(vec![2, 2])).unwrap();
/// let tensor2 = CpuTensor::from_data(data2, Shape::new(vec![2, 2])).unwrap();
///
/// // Element-wise addition
/// let sum = tensor1.add(&tensor2).unwrap();
///
/// // Scalar multiplication
/// let scaled = tensor1.mul_scalar(3.0).unwrap();
///
/// // Matrix multiplication
/// let product = tensor1.matmul(&tensor2).unwrap();
///
/// // Reduction operations
/// let total_sum = tensor1.sum(None, false).unwrap();
/// let row_means = tensor1.mean(Some(1), false).unwrap();
///
/// // Activation functions
/// let activated = tensor1.relu().unwrap();
/// let probabilities = tensor1.softmax(1).unwrap();
/// ```
pub trait TensorOps<T: Numeric>: Tensor<T> {
    // ========== Element-wise Operations ==========

    /// Element-wise addition of two tensors.
    ///
    /// Supports broadcasting for tensors of compatible shapes.
    ///
    /// # Arguments
    /// * `other` - The tensor to add to this tensor
    ///
    /// # Returns
    /// A new tensor containing the element-wise sum
    ///
    /// # Examples
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, TensorOps};
    /// let a = CpuTensor::from_data(vec![1.0, 2.0], Shape::new(vec![2])).unwrap();
    /// let b = CpuTensor::from_data(vec![3.0, 4.0], Shape::new(vec![2])).unwrap();
    /// let result = a.add(&b).unwrap(); // [4.0, 6.0]
    /// ```
    fn add(&self, other: &Self) -> Result<Self, Self::Error>;

    /// Element-wise subtraction of two tensors.
    ///
    /// Supports broadcasting for tensors of compatible shapes.
    fn sub(&self, other: &Self) -> Result<Self, Self::Error>;

    /// Element-wise multiplication of two tensors.
    ///
    /// Supports broadcasting for tensors of compatible shapes.
    fn mul(&self, other: &Self) -> Result<Self, Self::Error>;

    /// Element-wise division of two tensors.
    ///
    /// Supports broadcasting for tensors of compatible shapes.
    /// Division by zero results in infinity or NaN according to IEEE 754.
    fn div(&self, other: &Self) -> Result<Self, Self::Error>;

    // ========== Scalar Operations ==========

    /// Add a scalar value to all elements of the tensor.
    ///
    /// # Arguments
    /// * `scalar` - The scalar value to add
    ///
    /// # Examples
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, TensorOps};
    /// let tensor = CpuTensor::from_data(vec![1.0, 2.0, 3.0], Shape::new(vec![3])).unwrap();
    /// let result = tensor.add_scalar(5.0).unwrap(); // [6.0, 7.0, 8.0]
    /// ```
    fn add_scalar(&self, scalar: T) -> Result<Self, Self::Error>;

    /// Subtract a scalar value from all elements of the tensor.
    fn sub_scalar(&self, scalar: T) -> Result<Self, Self::Error>;

    /// Multiply all elements of the tensor by a scalar value.
    fn mul_scalar(&self, scalar: T) -> Result<Self, Self::Error>;

    /// Divide all elements of the tensor by a scalar value.
    fn div_scalar(&self, scalar: T) -> Result<Self, Self::Error>;

    // ========== Matrix Operations ==========

    /// Matrix multiplication of two tensors.
    ///
    /// For 2D tensors, performs standard matrix multiplication.
    /// For higher-dimensional tensors, performs batched matrix multiplication
    /// on the last two dimensions.
    ///
    /// # Arguments
    /// * `other` - The tensor to multiply with this tensor
    ///
    /// # Shape Requirements
    /// - For 2D: `(m, k) × (k, n) → (m, n)`
    /// - For 3D+: `(..., m, k) × (..., k, n) → (..., m, n)`
    ///
    /// # Examples
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, TensorOps};
    /// let a = CpuTensor::from_data(vec![1.0, 2.0, 3.0, 4.0], Shape::new(vec![2, 2])).unwrap();
    /// let b = CpuTensor::from_data(vec![5.0, 6.0, 7.0, 8.0], Shape::new(vec![2, 2])).unwrap();
    /// let result = a.matmul(&b).unwrap(); // Matrix multiplication
    /// ```
    fn matmul(&self, other: &Self) -> Result<Self, Self::Error>;

    /// Dot product of two tensors.
    ///
    /// For 1D tensors, computes the standard dot product.
    /// For higher-dimensional tensors, computes the sum of element-wise products.
    ///
    /// # Returns
    /// A scalar value representing the dot product
    fn dot(&self, other: &Self) -> Result<T, Self::Error>;

    // ========== Reduction Operations ==========

    /// Sum of tensor elements along specified dimensions.
    ///
    /// # Arguments
    /// * `dim` - Dimension to reduce over (`None` means all dimensions)
    /// * `keepdim` - Whether to keep the reduced dimension with size 1
    ///
    /// # Examples
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, TensorOps};
    /// let tensor = CpuTensor::from_data(vec![1.0, 2.0, 3.0, 4.0], Shape::new(vec![2, 2])).unwrap();
    /// let total = tensor.sum(None, false).unwrap(); // Sum all elements
    /// let row_sums = tensor.sum(Some(1), false).unwrap(); // Sum each row
    /// ```
    fn sum(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error>;

    /// Mean of tensor elements along specified dimensions.
    fn mean(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error>;

    /// Maximum values along specified dimensions.
    fn max(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error>;

    /// Minimum values along specified dimensions.
    fn min(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error>;

    // ========== Activation Functions ==========

    /// Rectified Linear Unit activation function.
    ///
    /// Applies `f(x) = max(0, x)` element-wise.
    ///
    /// # Examples
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, TensorOps};
    /// let tensor = CpuTensor::from_data(vec![-1.0, 0.0, 1.0, 2.0], Shape::new(vec![4])).unwrap();
    /// let result = tensor.relu().unwrap(); // [0.0, 0.0, 1.0, 2.0]
    /// ```
    fn relu(&self) -> Result<Self, Self::Error>;

    /// Gaussian Error Linear Unit activation function.
    ///
    /// Applies `f(x) = x * Φ(x)` where Φ is the CDF of the standard normal distribution.
    fn gelu(&self) -> Result<Self, Self::Error>;

    /// Softmax activation function along a specified dimension.
    ///
    /// Applies `softmax(x_i) = exp(x_i) / Σ(exp(x_j))` along the specified dimension.
    /// Uses numerical stabilization to prevent overflow.
    ///
    /// # Arguments
    /// * `dim` - The dimension along which to apply softmax
    ///
    /// # Examples
    /// ```rust
    /// # use qilin_inference::tensor::{CpuTensor, Shape, TensorOps};
    /// let tensor = CpuTensor::from_data(vec![1.0, 2.0, 3.0], Shape::new(vec![3])).unwrap();
    /// let probs = tensor.softmax(0).unwrap(); // Probability distribution
    /// ```
    fn softmax(&self, dim: usize) -> Result<Self, Self::Error>;

    /// Log-softmax activation function along a specified dimension.
    ///
    /// Applies `log_softmax(x_i) = log(softmax(x_i))` with numerical stabilization.
    /// More numerically stable than computing `log(softmax(x))`.
    fn log_softmax(&self, dim: usize) -> Result<Self, Self::Error>;

    /// Hyperbolic tangent activation function.
    ///
    /// Applies `f(x) = tanh(x)` element-wise.
    fn tanh(&self) -> Result<Self, Self::Error>;

    /// Sigmoid activation function.
    ///
    /// Applies `f(x) = 1 / (1 + exp(-x))` element-wise with numerical stabilization.
    fn sigmoid(&self) -> Result<Self, Self::Error>;

    // Mathematical functions
    fn sin(&self) -> Result<Self, Self::Error>;
    fn cos(&self) -> Result<Self, Self::Error>;
    fn tan(&self) -> Result<Self, Self::Error>;
    fn asin(&self) -> Result<Self, Self::Error>;
    fn acos(&self) -> Result<Self, Self::Error>;
    fn atan(&self) -> Result<Self, Self::Error>;
    fn exp(&self) -> Result<Self, Self::Error>;
    fn log(&self) -> Result<Self, Self::Error>;
    fn log2(&self) -> Result<Self, Self::Error>;
    fn log10(&self) -> Result<Self, Self::Error>;
    fn sqrt(&self) -> Result<Self, Self::Error>;
    fn pow(&self, exp: &Self) -> Result<Self, Self::Error>;
    fn pow_scalar(&self, exp: T) -> Result<Self, Self::Error>;
    fn sinh(&self) -> Result<Self, Self::Error>;
    fn cosh(&self) -> Result<Self, Self::Error>;
    fn floor(&self) -> Result<Self, Self::Error>;
    fn ceil(&self) -> Result<Self, Self::Error>;
    fn round(&self) -> Result<Self, Self::Error>;
    fn trunc(&self) -> Result<Self, Self::Error>;
    fn abs(&self) -> Result<Self, Self::Error>;
    fn clamp(&self, min: T, max: T) -> Result<Self, Self::Error>;
}

/// Factory functions for creating tensors.
pub trait TensorFactory<T: Numeric> {
    type TensorType: Tensor<T>;
    type Error: std::error::Error + Send + Sync + 'static;
    
    /// Create a tensor filled with zeros.
    fn zeros(shape: &Shape) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a tensor filled with ones.
    fn ones(shape: &Shape) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a tensor filled with a constant value.
    fn full(shape: &Shape, value: T) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a tensor from a slice of data.
    fn from_slice(data: &[T], shape: &Shape) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a tensor from a vector of data.
    fn from_vec(data: Vec<T>, shape: &Shape) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a random tensor with values from a normal distribution.
    fn randn(shape: &Shape, mean: T, std: T) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a random tensor with values from a uniform distribution.
    fn rand(shape: &Shape, low: T, high: T) -> Result<Self::TensorType, Self::Error>;
}

/// Convenience functions for creating tensors.
pub fn zeros<T: Numeric>(shape: &Shape) -> Result<cpu::CpuTensor<T>, TensorError> {
    cpu::CpuTensorFactory::zeros(shape)
}

pub fn ones<T: Numeric>(shape: &Shape) -> Result<cpu::CpuTensor<T>, TensorError> {
    cpu::CpuTensorFactory::ones(shape)
}

pub fn from_slice<T: Numeric>(data: &[T], shape: &Shape) -> Result<cpu::CpuTensor<T>, TensorError> {
    cpu::CpuTensorFactory::from_slice(data, shape)
}

/// Trait for tensor view operations.
///
/// This trait provides methods for creating views of tensors without copying data.
/// Views are lightweight references to tensor data that can be used for operations
/// like slicing, transposing, and reshaping without memory allocation.
///
/// # Safety
/// Views maintain references to the original tensor data. The caller must ensure
/// that the original tensor remains valid for the lifetime of the view.
///
/// # Examples
/// ```rust,ignore
/// use qilin_inference::tensor::{TensorView, from_slice, Shape};
///
/// let tensor = from_slice(&[1.0, 2.0, 3.0, 4.0, 5.0, 6.0], &Shape::new(vec![2, 3]))?;
///
/// // Create a view of a slice
/// let slice_view = tensor.slice(&[0..1, 1..3])?;
///
/// // Create a transposed view
/// let transposed = tensor.transpose(&[1, 0])?;
///
/// // Create a reshaped view (if possible)
/// let reshaped = tensor.view_as(&Shape::new(vec![3, 2]))?;
/// ```
pub trait TensorView<T: Numeric>: Tensor<T> {
    /// The view type returned by view operations.
    type View: TensorView<T, Error = Self::Error>;

    /// Create a view of a slice of this tensor.
    ///
    /// # Arguments
    /// * `ranges` - The ranges to slice along each dimension
    ///
    /// # Returns
    /// A view of the sliced tensor data.
    ///
    /// # Errors
    /// Returns error if ranges are out of bounds or invalid.
    fn slice(&self, ranges: &[std::ops::Range<usize>]) -> Result<Self::View, Self::Error>;

    /// Create a transposed view of this tensor.
    ///
    /// # Arguments
    /// * `dims` - The new dimension order (permutation)
    ///
    /// # Returns
    /// A view of the transposed tensor.
    ///
    /// # Errors
    /// Returns error if the dimension permutation is invalid.
    fn transpose(&self, dims: &[usize]) -> Result<Self::View, Self::Error>;

    /// Create a view with a different shape (if compatible).
    ///
    /// # Arguments
    /// * `shape` - The new shape for the view
    ///
    /// # Returns
    /// A view of the tensor with the new shape.
    ///
    /// # Errors
    /// Returns error if the new shape is incompatible with the tensor size.
    fn view_as(&self, shape: &Shape) -> Result<Self::View, Self::Error>;

    /// Create a view that selects elements along a dimension.
    ///
    /// # Arguments
    /// * `dim` - The dimension to index along
    /// * `indices` - The indices to select
    ///
    /// # Returns
    /// A view containing only the selected elements.
    ///
    /// # Errors
    /// Returns error if dimension or indices are invalid.
    fn index_select(&self, dim: usize, indices: &[usize]) -> Result<Self::View, Self::Error>;

    /// Create a view that squeezes dimensions of size 1.
    ///
    /// # Arguments
    /// * `dims` - Optional specific dimensions to squeeze. If None, squeeze all size-1 dimensions.
    ///
    /// # Returns
    /// A view with squeezed dimensions.
    fn squeeze(&self, dims: Option<&[usize]>) -> Result<Self::View, Self::Error>;

    /// Create a view that adds dimensions of size 1.
    ///
    /// # Arguments
    /// * `dims` - The positions where to insert new dimensions
    ///
    /// # Returns
    /// A view with unsqueezed dimensions.
    fn unsqueeze(&self, dims: &[usize]) -> Result<Self::View, Self::Error>;

    /// Create a flattened view of the tensor.
    ///
    /// # Arguments
    /// * `start_dim` - The first dimension to flatten (default: 0)
    /// * `end_dim` - The last dimension to flatten (default: -1, meaning last dimension)
    ///
    /// # Returns
    /// A view with flattened dimensions.
    fn flatten(&self, start_dim: Option<usize>, end_dim: Option<isize>) -> Result<Self::View, Self::Error>;

    /// Check if this tensor is a view of another tensor.
    fn is_view(&self) -> bool;

    /// Get the base tensor that this view references (if it's a view).
    /// Returns None if this is not a view.
    fn base_tensor(&self) -> Option<&Self>;

    /// Get the stride information for this tensor/view.
    fn strides(&self) -> &[usize];

    /// Get the offset of this view into the base tensor data.
    fn offset(&self) -> usize;

    /// Create a contiguous copy of this tensor/view.
    ///
    /// If the tensor is already contiguous, this may return a clone.
    /// If it's a non-contiguous view, this will copy the data to create
    /// a contiguous tensor.
    fn contiguous(&self) -> Result<Self, Self::Error>;
}

/// Trait for mutable tensor view operations.
///
/// This trait extends TensorView with operations that can modify the viewed data.
/// It's separate from TensorView to allow for immutable views when needed.
pub trait TensorViewMut<T: Numeric>: TensorView<T> {
    /// The mutable view type returned by mutable view operations.
    type ViewMut: TensorViewMut<T, Error = Self::Error>;

    /// Create a mutable view of a slice of this tensor.
    fn slice_mut(&mut self, ranges: &[std::ops::Range<usize>]) -> Result<Self::ViewMut, Self::Error>;

    /// Create a mutable transposed view of this tensor.
    fn transpose_mut(&mut self, dims: &[usize]) -> Result<Self::ViewMut, Self::Error>;

    /// Create a mutable view with a different shape.
    fn view_as_mut(&mut self, shape: &Shape) -> Result<Self::ViewMut, Self::Error>;

    /// Fill the viewed data with a constant value.
    fn fill_view(&mut self, value: T) -> Result<(), Self::Error>;

    /// Copy data from another tensor into this view.
    fn copy_from_view(&mut self, other: &Self) -> Result<(), Self::Error>;
}

pub fn from_vec<T: Numeric>(data: Vec<T>, shape: &Shape) -> Result<cpu::CpuTensor<T>, TensorError> {
    cpu::CpuTensorFactory::from_vec(data, shape)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dtype_display() {
        assert_eq!(format!("{}", DType::F32), "f32");
        assert_eq!(format!("{}", DType::F64), "f64");
        assert_eq!(format!("{}", DType::I32), "i32");
    }
    
    #[test]
    fn test_numeric_traits() {
        assert_eq!(f32::ZERO, 0.0);
        assert_eq!(f32::ONE, 1.0);
        assert_eq!(f32::dtype(), DType::F32);
        
        assert_eq!(f64::ZERO, 0.0);
        assert_eq!(f64::ONE, 1.0);
        assert_eq!(f64::dtype(), DType::F64);
    }
}
