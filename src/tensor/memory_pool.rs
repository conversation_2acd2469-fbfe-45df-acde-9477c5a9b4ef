//! Memory pool for efficient tensor memory management.
//!
//! This module provides a memory pool implementation that reduces allocation overhead
//! for tensor operations by reusing memory buffers. This is particularly beneficial for:
//! - Frequent tensor operations in training loops
//! - Real-time inference scenarios
//! - Reducing garbage collection pressure
//! - Minimizing memory fragmentation

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{SystemTime, UNIX_EPOCH};
use crate::tensor::Numeric;
use crate::error::TensorError;

/// Configuration for memory pool behavior.
#[derive(Debug, Clone)]
pub struct MemoryPoolConfig {
    /// Maximum number of buffers to keep per size bucket.
    pub max_buffers_per_bucket: usize,
    /// Maximum total memory to keep in the pool (in bytes).
    pub max_total_memory: usize,
    /// Whether to enable memory pool statistics collection.
    pub enable_stats: bool,
    /// Minimum buffer size to pool (smaller buffers are not pooled).
    pub min_pooled_size: usize,
    /// Enable size bucketing strategy (powers of 2).
    pub enable_size_bucketing: bool,
    /// Preallocation sizes for common tensor sizes.
    pub preallocation_sizes: Vec<usize>,
    /// Maximum age of unused buffers before cleanup (in seconds).
    pub max_buffer_age: u64,
    /// Enable automatic memory compaction.
    pub enable_compaction: bool,
}

impl Default for MemoryPoolConfig {
    fn default() -> Self {
        Self {
            max_buffers_per_bucket: 16,
            max_total_memory: 1024 * 1024 * 1024, // 1GB
            enable_stats: false,
            min_pooled_size: 64, // Don't pool very small buffers
            enable_size_bucketing: true,
            preallocation_sizes: vec![
                256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 65536, 131072
            ],
            max_buffer_age: 300, // 5 minutes
            enable_compaction: true,
        }
    }
}

/// Statistics for memory pool usage.
#[derive(Debug, Clone, Default)]
pub struct MemoryPoolStats {
    /// Total number of allocations requested.
    pub total_allocations: u64,
    /// Number of allocations served from the pool.
    pub pool_hits: u64,
    /// Number of allocations that required new memory.
    pub pool_misses: u64,
    /// Total number of deallocations.
    pub total_deallocations: u64,
    /// Number of buffers returned to the pool.
    pub pool_returns: u64,
    /// Number of buffers discarded (pool full or too large).
    pub pool_discards: u64,
    /// Current number of buffers in the pool.
    pub current_pooled_buffers: usize,
    /// Current total memory in the pool (bytes).
    pub current_pooled_memory: usize,
    /// Number of memory compactions performed.
    pub compactions: u64,
    /// Number of buffers cleaned up due to age.
    pub age_cleanups: u64,
    /// Peak memory usage in bytes.
    pub peak_memory_usage: usize,
    /// Total bytes allocated (for calculating average).
    pub total_bytes_allocated: u64,
}

/// Buffer metadata for advanced memory management.
#[derive(Debug, Clone)]
struct BufferMetadata<T: Numeric> {
    /// Buffer data.
    buffer: Vec<T>,
    /// Timestamp when buffer was last used.
    last_used: u64,
    /// Number of times this buffer has been reused.
    reuse_count: u32,
}

impl MemoryPoolStats {
    /// Calculate the pool hit rate as a percentage.
    pub fn hit_rate(&self) -> f64 {
        if self.total_allocations == 0 {
            0.0
        } else {
            (self.pool_hits as f64 / self.total_allocations as f64) * 100.0
        }
    }

    /// Calculate the pool return rate as a percentage.
    pub fn return_rate(&self) -> f64 {
        if self.total_deallocations == 0 {
            0.0
        } else {
            (self.pool_returns as f64 / self.total_deallocations as f64) * 100.0
        }
    }

    /// Calculate the average allocation size in bytes.
    pub fn avg_allocation_size(&self) -> f64 {
        if self.total_allocations == 0 {
            0.0
        } else {
            self.total_bytes_allocated as f64 / self.total_allocations as f64
        }
    }

    /// Calculate memory efficiency (pooled memory / peak memory).
    pub fn memory_efficiency(&self) -> f64 {
        if self.peak_memory_usage == 0 {
            0.0
        } else {
            self.current_pooled_memory as f64 / self.peak_memory_usage as f64
        }
    }

    /// Get a summary string of key statistics.
    pub fn summary(&self) -> String {
        format!(
            "Pool Stats: {:.1}% hit rate, {:.1}% return rate, {} buffers, {:.1} MB pooled, avg size: {:.1} KB",
            self.hit_rate(),
            self.return_rate(),
            self.current_pooled_buffers,
            self.current_pooled_memory as f64 / (1024.0 * 1024.0),
            self.avg_allocation_size() / 1024.0
        )
    }
}

/// A memory pool for efficient tensor buffer management.
/// 
/// The memory pool maintains buckets of pre-allocated buffers organized by size.
/// This reduces allocation overhead and memory fragmentation for frequent tensor operations.
/// 
/// # Thread Safety
/// The memory pool is thread-safe and can be shared across multiple threads.
/// 
/// # Examples
/// ```rust,ignore
/// use qilin_inference::tensor::memory_pool::{TensorMemoryPool, MemoryPoolConfig};
/// 
/// let config = MemoryPoolConfig::default();
/// let pool = TensorMemoryPool::new(config);
/// 
/// // Allocate a buffer
/// let buffer: Vec<f32> = pool.allocate(1024)?;
/// 
/// // Use the buffer...
/// 
/// // Return the buffer to the pool
/// pool.deallocate(buffer);
/// ```
pub struct TensorMemoryPool<T: Numeric> {
    /// Configuration for the memory pool.
    config: MemoryPoolConfig,
    /// Buckets of buffers organized by size with metadata.
    /// Key: buffer size, Value: vector of buffer metadata
    buckets: Arc<Mutex<HashMap<usize, Vec<BufferMetadata<T>>>>>,
    /// Statistics for pool usage.
    stats: Arc<Mutex<MemoryPoolStats>>,
    /// Current total memory in the pool.
    current_memory: Arc<Mutex<usize>>,
    /// Last cleanup timestamp.
    last_cleanup: Arc<Mutex<u64>>,
}

impl<T: Numeric> TensorMemoryPool<T> {
    /// Create a new memory pool with the given configuration.
    pub fn new(config: MemoryPoolConfig) -> Self {
        let pool = Self {
            config: config.clone(),
            buckets: Arc::new(Mutex::new(HashMap::new())),
            stats: Arc::new(Mutex::new(MemoryPoolStats::default())),
            current_memory: Arc::new(Mutex::new(0)),
            last_cleanup: Arc::new(Mutex::new(Self::current_timestamp())),
        };

        // Preallocate common sizes if enabled
        if !config.preallocation_sizes.is_empty() {
            pool.preallocate_buffers();
        }

        pool
    }
    
    /// Create a new memory pool with default configuration.
    pub fn default() -> Self {
        Self::new(MemoryPoolConfig::default())
    }

    /// Get current timestamp in seconds since UNIX epoch.
    fn current_timestamp() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }

    /// Preallocate buffers for common sizes.
    fn preallocate_buffers(&self) {
        let mut buckets = self.buckets.lock().unwrap();
        let current_time = Self::current_timestamp();

        for &size in &self.config.preallocation_sizes {
            if size >= self.config.min_pooled_size {
                let bucket = buckets.entry(size).or_insert_with(Vec::new);

                // Preallocate a few buffers for each size
                let preallocate_count = (self.config.max_buffers_per_bucket / 4).max(1);
                for _ in 0..preallocate_count {
                    let buffer = vec![T::ZERO; size];
                    let metadata = BufferMetadata {
                        buffer,
                        last_used: current_time,
                        reuse_count: 0,
                    };
                    bucket.push(metadata);
                }
            }
        }

        // Update memory tracking
        let total_preallocated: usize = self.config.preallocation_sizes.iter()
            .filter(|&&size| size >= self.config.min_pooled_size)
            .map(|&size| size * (self.config.max_buffers_per_bucket / 4).max(1))
            .sum();

        let mut current_memory = self.current_memory.lock().unwrap();
        *current_memory += total_preallocated * std::mem::size_of::<T>();

        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.current_pooled_memory = *current_memory;
            stats.peak_memory_usage = stats.peak_memory_usage.max(*current_memory);
        }
    }

    /// Get the bucketed size for a given size (power of 2 bucketing).
    fn get_bucketed_size(&self, size: usize) -> usize {
        if !self.config.enable_size_bucketing {
            return size;
        }

        // Round up to next power of 2 for better reuse
        if size <= 64 {
            size
        } else {
            let mut bucket_size = 64;
            while bucket_size < size {
                bucket_size *= 2;
            }
            bucket_size
        }
    }
    
    /// Allocate a buffer of the specified size.
    /// 
    /// This will first try to reuse a buffer from the pool. If no suitable buffer
    /// is available, a new buffer will be allocated.
    /// 
    /// # Arguments
    /// * `size` - The number of elements to allocate
    /// 
    /// # Returns
    /// A vector with the requested capacity, initialized with zeros.
    pub fn allocate(&self, size: usize) -> Result<Vec<T>, TensorError> {
        let bytes_requested = size * std::mem::size_of::<T>();

        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.total_allocations += 1;
            stats.total_bytes_allocated += bytes_requested as u64;
        }

        // Don't pool very small buffers
        if size < self.config.min_pooled_size {
            if self.config.enable_stats {
                let mut stats = self.stats.lock().unwrap();
                stats.pool_misses += 1;
            }
            return Ok(vec![T::ZERO; size]);
        }

        // Get bucketed size for better reuse
        let bucket_size = self.get_bucketed_size(size);
        let current_time = Self::current_timestamp();

        // Try to get a buffer from the pool
        let mut buckets = self.buckets.lock().unwrap();
        if let Some(bucket) = buckets.get_mut(&bucket_size) {
            if let Some(mut metadata) = bucket.pop() {
                // Update metadata
                metadata.last_used = current_time;
                metadata.reuse_count += 1;

                // Update statistics
                if self.config.enable_stats {
                    let mut stats = self.stats.lock().unwrap();
                    stats.pool_hits += 1;
                    stats.current_pooled_buffers = stats.current_pooled_buffers.saturating_sub(1);
                }

                // Update current memory
                let mut current_memory = self.current_memory.lock().unwrap();
                *current_memory = current_memory.saturating_sub(bucket_size * std::mem::size_of::<T>());

                // Ensure the buffer is the right size and zeroed
                let mut buffer = metadata.buffer;
                buffer.resize(size, T::ZERO);
                buffer.fill(T::ZERO);

                return Ok(buffer);
            }
        }
        
        // No buffer available in pool, allocate new one
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.pool_misses += 1;
        }
        
        Ok(vec![T::ZERO; size])
    }

    /// Allocate multiple buffers of the same size efficiently.
    ///
    /// This is more efficient than calling allocate() multiple times
    /// as it can batch the operations and reduce lock contention.
    ///
    /// # Arguments
    /// * `size` - The size of each buffer
    /// * `count` - The number of buffers to allocate
    ///
    /// # Returns
    /// A vector of allocated buffers
    pub fn allocate_batch(&self, size: usize, count: usize) -> Result<Vec<Vec<T>>, TensorError> {
        if count == 0 {
            return Ok(Vec::new());
        }

        let mut result = Vec::with_capacity(count);
        let bytes_per_buffer = size * std::mem::size_of::<T>();
        let total_bytes = bytes_per_buffer * count;

        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.total_allocations += count as u64;
            stats.total_bytes_allocated += total_bytes as u64;
        }

        // Don't pool very small buffers
        if size < self.config.min_pooled_size {
            if self.config.enable_stats {
                let mut stats = self.stats.lock().unwrap();
                stats.pool_misses += count as u64;
            }
            for _ in 0..count {
                result.push(vec![T::ZERO; size]);
            }
            return Ok(result);
        }

        let bucket_size = self.get_bucketed_size(size);
        let current_time = Self::current_timestamp();

        // Try to get buffers from the pool
        let mut buckets = self.buckets.lock().unwrap();
        let mut pool_hits = 0;

        if let Some(bucket) = buckets.get_mut(&bucket_size) {
            let available = bucket.len().min(count);

            for _ in 0..available {
                if let Some(mut metadata) = bucket.pop() {
                    metadata.last_used = current_time;
                    metadata.reuse_count += 1;

                    let mut buffer = metadata.buffer;
                    buffer.resize(size, T::ZERO);
                    buffer.fill(T::ZERO);

                    result.push(buffer);
                    pool_hits += 1;
                }
            }
        }

        drop(buckets);

        // Allocate remaining buffers
        let remaining = count - pool_hits;
        for _ in 0..remaining {
            result.push(vec![T::ZERO; size]);
        }

        // Update statistics
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.pool_hits += pool_hits as u64;
            stats.pool_misses += remaining as u64;
            stats.current_pooled_buffers = stats.current_pooled_buffers.saturating_sub(pool_hits);
        }

        // Update memory tracking
        if pool_hits > 0 {
            let mut current_memory = self.current_memory.lock().unwrap();
            *current_memory = current_memory.saturating_sub(pool_hits * bucket_size * std::mem::size_of::<T>());
        }

        Ok(result)
    }

    /// Deallocate multiple buffers efficiently.
    ///
    /// This is more efficient than calling deallocate() multiple times.
    ///
    /// # Arguments
    /// * `buffers` - The buffers to return to the pool
    pub fn deallocate_batch(&self, buffers: Vec<Vec<T>>) {
        if buffers.is_empty() {
            return;
        }

        let count = buffers.len();
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.total_deallocations += count as u64;
        }

        let current_time = Self::current_timestamp();
        let mut buckets = self.buckets.lock().unwrap();
        let mut current_memory = self.current_memory.lock().unwrap();
        let mut pool_returns = 0;
        let mut pool_discards = 0;

        for buffer in buffers {
            let size = buffer.len();

            // Don't pool very small buffers
            if size < self.config.min_pooled_size {
                pool_discards += 1;
                continue;
            }

            let bucket_size = self.get_bucketed_size(size);
            let buffer_memory = bucket_size * std::mem::size_of::<T>();

            // Check memory limits
            if *current_memory + buffer_memory > self.config.max_total_memory {
                pool_discards += 1;
                continue;
            }

            let bucket = buckets.entry(bucket_size).or_insert_with(Vec::new);

            // Check bucket limits
            if bucket.len() >= self.config.max_buffers_per_bucket {
                pool_discards += 1;
                continue;
            }

            // Create buffer metadata and add to pool
            let mut buffer_to_store = buffer;
            if buffer_to_store.len() != bucket_size {
                buffer_to_store.resize(bucket_size, T::ZERO);
            }

            let metadata = BufferMetadata {
                buffer: buffer_to_store,
                last_used: current_time,
                reuse_count: 0,
            };

            bucket.push(metadata);
            *current_memory += buffer_memory;
            pool_returns += 1;
        }

        // Update statistics
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.pool_returns += pool_returns;
            stats.pool_discards += pool_discards;
            stats.current_pooled_buffers += pool_returns as usize;
            stats.current_pooled_memory = *current_memory;
            stats.peak_memory_usage = stats.peak_memory_usage.max(*current_memory);
        }
    }
    
    /// Return a buffer to the pool for reuse.
    ///
    /// The buffer will be added to the appropriate size bucket if there's space.
    /// If the pool is full or the buffer is too large, it will be discarded.
    ///
    /// # Arguments
    /// * `buffer` - The buffer to return to the pool
    pub fn deallocate(&self, buffer: Vec<T>) {
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.total_deallocations += 1;
        }

        let size = buffer.len();

        // Don't pool very small buffers
        if size < self.config.min_pooled_size {
            if self.config.enable_stats {
                let mut stats = self.stats.lock().unwrap();
                stats.pool_discards += 1;
            }
            return;
        }

        // Get bucketed size for consistency
        let bucket_size = self.get_bucketed_size(size);
        let buffer_memory = bucket_size * std::mem::size_of::<T>();

        // Check if we can add this buffer to the pool
        let mut current_memory = self.current_memory.lock().unwrap();
        if *current_memory + buffer_memory > self.config.max_total_memory {
            if self.config.enable_stats {
                let mut stats = self.stats.lock().unwrap();
                stats.pool_discards += 1;
            }
            return;
        }

        let mut buckets = self.buckets.lock().unwrap();
        let bucket = buckets.entry(bucket_size).or_insert_with(Vec::new);

        // Check if this bucket is full
        if bucket.len() >= self.config.max_buffers_per_bucket {
            if self.config.enable_stats {
                let mut stats = self.stats.lock().unwrap();
                stats.pool_discards += 1;
            }
            return;
        }
        
        // Create buffer metadata and add to pool
        let current_time = Self::current_timestamp();
        let mut buffer_to_store = buffer;

        // Resize buffer to bucket size if needed
        if buffer_to_store.len() != bucket_size {
            buffer_to_store.resize(bucket_size, T::ZERO);
        }

        let metadata = BufferMetadata {
            buffer: buffer_to_store,
            last_used: current_time,
            reuse_count: 0,
        };

        bucket.push(metadata);
        *current_memory += buffer_memory;

        // Update statistics
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.pool_returns += 1;
            stats.current_pooled_buffers += 1;
            stats.current_pooled_memory = *current_memory;
            stats.peak_memory_usage = stats.peak_memory_usage.max(*current_memory);
        }

        // Trigger cleanup if needed
        if self.config.enable_compaction {
            self.maybe_cleanup();
        }
    }
    
    /// Maybe perform cleanup based on time and configuration.
    fn maybe_cleanup(&self) {
        let current_time = Self::current_timestamp();
        let mut last_cleanup = self.last_cleanup.lock().unwrap();

        // Only cleanup every 60 seconds
        if current_time - *last_cleanup < 60 {
            return;
        }

        *last_cleanup = current_time;
        drop(last_cleanup);

        self.cleanup_old_buffers();
    }

    /// Clean up old buffers that haven't been used recently.
    pub fn cleanup_old_buffers(&self) {
        let current_time = Self::current_timestamp();
        let max_age = self.config.max_buffer_age;
        let mut cleaned_count = 0;
        let mut freed_memory = 0;

        let mut buckets = self.buckets.lock().unwrap();
        for (size, bucket) in buckets.iter_mut() {
            let original_len = bucket.len();
            bucket.retain(|metadata| {
                let age = current_time.saturating_sub(metadata.last_used);
                if age > max_age {
                    freed_memory += size * std::mem::size_of::<T>();
                    cleaned_count += 1;
                    false
                } else {
                    true
                }
            });
        }

        // Remove empty buckets
        buckets.retain(|_, bucket| !bucket.is_empty());
        drop(buckets);

        // Update memory tracking
        if freed_memory > 0 {
            let mut current_memory = self.current_memory.lock().unwrap();
            *current_memory = current_memory.saturating_sub(freed_memory);

            if self.config.enable_stats {
                let mut stats = self.stats.lock().unwrap();
                stats.age_cleanups += cleaned_count;
                stats.current_pooled_buffers = stats.current_pooled_buffers.saturating_sub(cleaned_count as usize);
                stats.current_pooled_memory = *current_memory;
            }
        }
    }

    /// Get current memory pool statistics.
    pub fn stats(&self) -> MemoryPoolStats {
        if self.config.enable_stats {
            self.stats.lock().unwrap().clone()
        } else {
            MemoryPoolStats::default()
        }
    }
    
    /// Clear all buffers from the pool.
    /// 
    /// This frees all pooled memory and resets statistics.
    pub fn clear(&self) {
        let mut buckets = self.buckets.lock().unwrap();
        buckets.clear();
        
        let mut current_memory = self.current_memory.lock().unwrap();
        *current_memory = 0;
        
        if self.config.enable_stats {
            let mut stats = self.stats.lock().unwrap();
            stats.current_pooled_buffers = 0;
            stats.current_pooled_memory = 0;
        }
    }
    
    /// Get the current configuration.
    pub fn config(&self) -> &MemoryPoolConfig {
        &self.config
    }
    
    /// Get the number of buffers currently in the pool.
    pub fn pooled_buffer_count(&self) -> usize {
        let buckets = self.buckets.lock().unwrap();
        buckets.values().map(|bucket| bucket.len()).sum()
    }
    
    /// Get the current memory usage of the pool in bytes.
    pub fn current_memory_usage(&self) -> usize {
        *self.current_memory.lock().unwrap()
    }
}

impl<T: Numeric> Clone for TensorMemoryPool<T> {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            buckets: Arc::clone(&self.buckets),
            stats: Arc::clone(&self.stats),
            current_memory: Arc::clone(&self.current_memory),
            last_cleanup: Arc::clone(&self.last_cleanup),
        }
    }
}

/// Global memory pool instance for convenient access.
/// 
/// This provides a default memory pool that can be used throughout the application
/// without needing to pass pool instances around.
pub struct GlobalMemoryPool;

impl GlobalMemoryPool {
    /// Get the global memory pool for f32 tensors.
    pub fn f32() -> &'static TensorMemoryPool<f32> {
        static POOL: std::sync::OnceLock<TensorMemoryPool<f32>> = std::sync::OnceLock::new();
        POOL.get_or_init(|| TensorMemoryPool::new(MemoryPoolConfig::default()))
    }
    
    /// Get the global memory pool for f64 tensors.
    pub fn f64() -> &'static TensorMemoryPool<f64> {
        static POOL: std::sync::OnceLock<TensorMemoryPool<f64>> = std::sync::OnceLock::new();
        POOL.get_or_init(|| TensorMemoryPool::new(MemoryPoolConfig::default()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_memory_pool_basic() {
        let mut config = MemoryPoolConfig::default();
        config.enable_stats = true;
        let pool = TensorMemoryPool::<f32>::new(config);
        
        // Allocate a buffer
        let buffer = pool.allocate(1024).unwrap();
        assert_eq!(buffer.len(), 1024);
        assert!(buffer.iter().all(|&x| x == 0.0));
        
        // Return it to the pool
        pool.deallocate(buffer);
        
        // Allocate again - should come from pool
        let buffer2 = pool.allocate(1024).unwrap();
        assert_eq!(buffer2.len(), 1024);
        
        let stats = pool.stats();
        assert_eq!(stats.total_allocations, 2);
        assert_eq!(stats.pool_hits, 1);
        assert_eq!(stats.pool_misses, 1);
    }
    
    #[test]
    fn test_memory_pool_size_buckets() {
        let pool = TensorMemoryPool::<f32>::new(MemoryPoolConfig::default());
        
        // Allocate buffers of different sizes
        let buffer1 = pool.allocate(512).unwrap();
        let buffer2 = pool.allocate(1024).unwrap();
        let buffer3 = pool.allocate(512).unwrap();
        
        // Return them
        pool.deallocate(buffer1);
        pool.deallocate(buffer2);
        pool.deallocate(buffer3);
        
        // Should have 2 buffers of size 512 and 1 of size 1024
        assert_eq!(pool.pooled_buffer_count(), 3);
    }
    
    #[test]
    fn test_memory_pool_limits() {
        let mut config = MemoryPoolConfig::default();
        config.max_buffers_per_bucket = 2;
        config.enable_stats = true;
        let pool = TensorMemoryPool::<f32>::new(config);

        // Allocate 3 buffers of the same size
        let buffer1 = pool.allocate(1024).unwrap();
        let buffer2 = pool.allocate(1024).unwrap();
        let buffer3 = pool.allocate(1024).unwrap();

        // Return them all to the pool
        pool.deallocate(buffer1); // Goes to pool (1 in pool)
        pool.deallocate(buffer2); // Goes to pool (2 in pool)
        pool.deallocate(buffer3); // Should be discarded (pool full)

        // Should only have 2 buffers in pool (max_buffers_per_bucket = 2)
        assert_eq!(pool.pooled_buffer_count(), 2);

        let stats = pool.stats();
        assert_eq!(stats.pool_discards, 1);
    }
}
