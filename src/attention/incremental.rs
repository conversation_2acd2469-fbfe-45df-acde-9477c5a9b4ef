//! Incremental attention computation for autoregressive generation.
//!
//! This module provides efficient attention computation for autoregressive
//! text generation by leveraging KV caching. Instead of recomputing attention
//! for all previous tokens, it reuses cached key-value pairs and only computes
//! attention for new tokens.
//!
//! Features:
//! - Incremental key-value computation
//! - Automatic cache management
//! - Batch processing support
//! - Memory-efficient generation

use std::marker::PhantomData;
use crate::tensor::{Tensor, TensorOps, TensorView, Shape, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::layers::{Layer, linear::{Linear, LinearConfig}};
use crate::error::{AttentionError, CacheError, ErrorContext};
use super::{Attention, AttentionConfig};
use super::scaled_dot_product::ScaledDotProductAttention;
use super::cache::{KVCache, CacheConfig};

/// Incremental multi-head attention for autoregressive generation.
///
/// This layer combines multi-head attention with KV caching to enable
/// efficient autoregressive text generation. It maintains a cache of
/// previously computed key-value pairs and only computes attention
/// for new tokens.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::attention::{IncrementalAttention, AttentionConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create incremental attention
/// let config = AttentionConfig::new(512, 8);
/// let mut attn = IncrementalAttention::new(config).unwrap();
///
/// // First step: compute attention for initial tokens
/// let input = CpuTensor::randn(&Shape::new(vec![1, 5, 512]), 0.0, 1.0).unwrap();
/// let (output, _) = attn.forward_initial("seq_1", &input).unwrap();
///
/// // Subsequent steps: incremental computation
/// let new_token = CpuTensor::randn(&Shape::new(vec![1, 1, 512]), 0.0, 1.0).unwrap();
/// let (output, _) = attn.forward_incremental("seq_1", &new_token).unwrap();
/// ```
#[derive(Debug)]
pub struct IncrementalAttention<T: Numeric> {
    /// Attention configuration.
    config: AttentionConfig,
    /// Linear projection for queries.
    q_proj: Linear<T>,
    /// Linear projection for keys.
    k_proj: Linear<T>,
    /// Linear projection for values.
    v_proj: Linear<T>,
    /// Output projection.
    out_proj: Linear<T>,
    /// Attention implementation.
    attention: ScaledDotProductAttention<T>,
    /// KV cache for storing previous computations.
    cache: KVCache<T>,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> IncrementalAttention<T> {
    /// Create a new incremental attention layer.
    pub fn new(config: AttentionConfig) -> Result<Self, AttentionError> {
        // Validate configuration
        config.validate().map_err(|msg| AttentionError::InvalidConfiguration {
            message: msg,
            config_field: None,
            context: Some(ErrorContext::new("new", "attention::incremental")),
        })?;
        
        let hidden_size = config.hidden_size;
        let head_dim = config.head_dim();
        
        // Create linear projections
        let linear_config = LinearConfig::new(hidden_size, hidden_size)
            .with_bias(config.use_bias);
        
        let q_proj = Linear::new(linear_config.clone())
            .map_err(AttentionError::Tensor)?;
        let k_proj = Linear::new(linear_config.clone())
            .map_err(AttentionError::Tensor)?;
        let v_proj = Linear::new(linear_config.clone())
            .map_err(AttentionError::Tensor)?;
        let out_proj = Linear::new(linear_config)
            .map_err(AttentionError::Tensor)?;
        
        // Create attention mechanism
        let attention = ScaledDotProductAttention::new(
            head_dim,
            config.dropout,
            config.causal,
        )?;
        
        // Create KV cache
        let cache_config = CacheConfig::new(
            config.max_seq_len.unwrap_or(2048),
            32, // Default batch size
            config.num_heads,
            head_dim,
        );
        let cache = KVCache::new(cache_config).map_err(AttentionError::Cache)?;
        
        Ok(Self {
            config,
            q_proj,
            k_proj,
            v_proj,
            out_proj,
            attention,
            cache,
            _phantom: PhantomData,
        })
    }
    
    /// Get the configuration.
    pub fn config(&self) -> &AttentionConfig {
        &self.config
    }
    
    /// Get cache statistics.
    pub fn cache_stats(&self) -> super::cache::CacheStats {
        self.cache.stats()
    }
    
    /// Clear the cache for a specific sequence.
    pub fn clear_sequence(&mut self, sequence_id: &str) -> Result<(), AttentionError> {
        self.cache.remove(sequence_id).map_err(AttentionError::Cache)
    }
    
    /// Clear all cached sequences.
    pub fn clear_all(&mut self) {
        self.cache.clear();
    }
    
    /// Check if a sequence is cached.
    pub fn is_cached(&self, sequence_id: &str) -> bool {
        self.cache.contains(sequence_id)
    }
    
    /// Get the current sequence length for a cached sequence.
    pub fn get_sequence_length(&self, sequence_id: &str) -> Option<usize> {
        self.cache.get_sequence_length(sequence_id)
    }

    /// Initial forward pass for a new sequence.
    ///
    /// This method processes the initial tokens of a sequence and caches
    /// the computed key-value pairs for future incremental computation.
    ///
    /// # Arguments
    /// * `sequence_id` - Unique identifier for the sequence
    /// * `input` - Input tensor with shape [batch_size, seq_len, hidden_size]
    ///
    /// # Returns
    /// * `output` - Output tensor with shape [batch_size, seq_len, hidden_size]
    /// * `attention_weights` - Attention weights for analysis
    pub fn forward_initial(
        &mut self,
        sequence_id: &str,
        input: &CpuTensor<T>,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), AttentionError> {
        // Validate input
        self.validate_input(input)?;

        let batch_size = input.shape().dims()[0];
        let seq_len = input.shape().dims()[1];
        let hidden_size = self.config.hidden_size;
        let num_heads = self.config.num_heads;
        let head_dim = self.config.head_dim();

        // Apply linear projections
        let q = self.q_proj.forward(input.clone()).map_err(AttentionError::Tensor)?;
        let k = self.k_proj.forward(input.clone()).map_err(AttentionError::Tensor)?;
        let v = self.v_proj.forward(input.clone()).map_err(AttentionError::Tensor)?;

        // Reshape for multi-head attention: [batch_size, seq_len, num_heads, head_dim]
        let q_heads = self.reshape_for_heads(&q, batch_size, seq_len, num_heads, head_dim)?;
        let k_heads = self.reshape_for_heads(&k, batch_size, seq_len, num_heads, head_dim)?;
        let v_heads = self.reshape_for_heads(&v, batch_size, seq_len, num_heads, head_dim)?;

        // Transpose to [batch_size, num_heads, seq_len, head_dim]
        let q_transposed = Tensor::transpose(&q_heads, 1, 2).map_err(AttentionError::Tensor)?;
        let k_transposed = Tensor::transpose(&k_heads, 1, 2).map_err(AttentionError::Tensor)?;
        let v_transposed = Tensor::transpose(&v_heads, 1, 2).map_err(AttentionError::Tensor)?;

        // Store key-value pairs in cache for future use
        // Cache format: [seq_len, num_heads, head_dim]
        let k_cache = Tensor::transpose(&k_transposed, 1, 2).map_err(AttentionError::Tensor)?;
        let v_cache = Tensor::transpose(&v_transposed, 1, 2).map_err(AttentionError::Tensor)?;

        // For batch processing, we store the first sequence in the batch
        // TODO: Support full batch caching
        let k_cache_single = self.extract_single_sequence(&k_cache, 0)?;
        let v_cache_single = self.extract_single_sequence(&v_cache, 0)?;

        self.cache.store(sequence_id, k_cache_single, v_cache_single)
            .map_err(AttentionError::Cache)?;

        // Compute attention
        let (attn_output, attn_weights) = self.compute_attention_with_tensors(
            &q_transposed, &k_transposed, &v_transposed
        )?;

        // Reshape output back to [batch_size, seq_len, hidden_size]
        let output = self.reshape_output(&attn_output, batch_size, seq_len, hidden_size)?;

        // Apply output projection
        let final_output = self.out_proj.forward(output).map_err(AttentionError::Tensor)?;

        Ok((final_output, attn_weights))
    }

    /// Incremental forward pass for existing sequence.
    ///
    /// This method processes new tokens for an existing sequence by reusing
    /// cached key-value pairs and only computing attention for the new tokens.
    ///
    /// # Arguments
    /// * `sequence_id` - Unique identifier for the sequence
    /// * `new_input` - New input tensor with shape [batch_size, new_seq_len, hidden_size]
    ///
    /// # Returns
    /// * `output` - Output tensor with shape [batch_size, new_seq_len, hidden_size]
    /// * `attention_weights` - Attention weights for analysis
    pub fn forward_incremental(
        &mut self,
        sequence_id: &str,
        new_input: &CpuTensor<T>,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), AttentionError> {
        // Validate input
        self.validate_input(new_input)?;

        let batch_size = new_input.shape().dims()[0];
        let new_seq_len = new_input.shape().dims()[1];
        let hidden_size = self.config.hidden_size;
        let num_heads = self.config.num_heads;
        let head_dim = self.config.head_dim();

        // Check if sequence exists in cache
        if !self.cache.contains(sequence_id) {
            return Err(AttentionError::InvalidConfiguration {
                message: format!("Sequence '{}' not found in cache. Use forward_initial first.", sequence_id),
                config_field: Some("sequence_id".to_string()),
                context: Some(ErrorContext::new("forward_incremental", "attention::incremental")),
            });
        }

        // Apply linear projections to new input
        let q_new = self.q_proj.forward(new_input.clone()).map_err(AttentionError::Tensor)?;
        let k_new = self.k_proj.forward(new_input.clone()).map_err(AttentionError::Tensor)?;
        let v_new = self.v_proj.forward(new_input.clone()).map_err(AttentionError::Tensor)?;

        // Reshape new tensors
        let q_new_heads = self.reshape_for_heads(&q_new, batch_size, new_seq_len, num_heads, head_dim)?;
        let k_new_heads = self.reshape_for_heads(&k_new, batch_size, new_seq_len, num_heads, head_dim)?;
        let v_new_heads = self.reshape_for_heads(&v_new, batch_size, new_seq_len, num_heads, head_dim)?;

        // Transpose new tensors
        let q_new_transposed = Tensor::transpose(&q_new_heads, 1, 2).map_err(AttentionError::Tensor)?;
        let k_new_transposed = Tensor::transpose(&k_new_heads, 1, 2).map_err(AttentionError::Tensor)?;
        let v_new_transposed = Tensor::transpose(&v_new_heads, 1, 2).map_err(AttentionError::Tensor)?;

        // Prepare new key-value pairs for caching
        let k_new_cache = Tensor::transpose(&k_new_transposed, 1, 2).map_err(AttentionError::Tensor)?;
        let v_new_cache = Tensor::transpose(&v_new_transposed, 1, 2).map_err(AttentionError::Tensor)?;

        // Extract single sequence for caching
        let k_new_single = self.extract_single_sequence(&k_new_cache, 0)?;
        let v_new_single = self.extract_single_sequence(&v_new_cache, 0)?;

        // Append to cache
        self.cache.append(sequence_id, k_new_single, v_new_single)
            .map_err(AttentionError::Cache)?;

        // Retrieve full cached key-value pairs
        let (k_cached, v_cached) = self.cache.get(sequence_id)
            .map_err(AttentionError::Cache)?;

        // Expand cached tensors to batch format
        let k_full = self.expand_to_batch(&k_cached, batch_size)?;
        let v_full = self.expand_to_batch(&v_cached, batch_size)?;

        // Transpose cached tensors to attention format
        let k_full_transposed = Tensor::transpose(&k_full, 1, 2).map_err(AttentionError::Tensor)?;
        let v_full_transposed = Tensor::transpose(&v_full, 1, 2).map_err(AttentionError::Tensor)?;

        // Compute attention with new queries and full key-value history
        let (attn_output, attn_weights) = self.compute_attention_with_tensors(
            &q_new_transposed, &k_full_transposed, &v_full_transposed
        )?;

        // Reshape output
        let output = self.reshape_output(&attn_output, batch_size, new_seq_len, hidden_size)?;

        // Apply output projection
        let final_output = self.out_proj.forward(output).map_err(AttentionError::Tensor)?;

        Ok((final_output, attn_weights))
    }

    /// Validate input tensor.
    fn validate_input(&self, input: &CpuTensor<T>) -> Result<(), AttentionError> {
        let input_shape = input.shape().dims();

        if input_shape.len() != 3 {
            return Err(AttentionError::DimensionMismatch {
                expected: "3D tensor [batch_size, seq_len, hidden_size]".to_string(),
                actual: format!("{}D tensor", input_shape.len()),
                operation: "input_validation".to_string(),
                context: Some(ErrorContext::new("validate_input", "attention::incremental")),
            });
        }

        if input_shape[2] != self.config.hidden_size {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", self.config.hidden_size),
                actual: format!("hidden_size: {}", input_shape[2]),
                operation: "hidden_size_validation".to_string(),
                context: Some(ErrorContext::new("validate_input", "attention::incremental")),
            });
        }

        Ok(())
    }

    /// Reshape tensor for multi-head attention.
    fn reshape_for_heads(
        &self,
        tensor: &CpuTensor<T>,
        batch_size: usize,
        seq_len: usize,
        num_heads: usize,
        head_dim: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let new_shape = Shape::new(vec![batch_size, seq_len, num_heads, head_dim]);
        TensorView::view_as(tensor, &new_shape).map_err(AttentionError::Tensor)
    }

    /// Reshape output tensor back to original format.
    fn reshape_output(
        &self,
        tensor: &CpuTensor<T>,
        batch_size: usize,
        seq_len: usize,
        hidden_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        // First transpose from [batch_size, num_heads, seq_len, head_dim] to [batch_size, seq_len, num_heads, head_dim]
        let transposed = Tensor::transpose(tensor, 1, 2).map_err(AttentionError::Tensor)?;

        // Then reshape to [batch_size, seq_len, hidden_size]
        let new_shape = Shape::new(vec![batch_size, seq_len, hidden_size]);
        TensorView::view_as(&transposed, &new_shape).map_err(AttentionError::Tensor)
    }

    /// Extract a single sequence from a batched tensor.
    fn extract_single_sequence(
        &self,
        tensor: &CpuTensor<T>,
        batch_idx: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let tensor_shape = tensor.shape().dims();
        if batch_idx >= tensor_shape[0] {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("batch_idx < {}", tensor_shape[0]),
                actual: format!("batch_idx = {}", batch_idx),
                operation: "batch_extraction".to_string(),
                context: Some(ErrorContext::new("extract_single_sequence", "attention::incremental")),
            });
        }

        // Extract slice: [seq_len, num_heads, head_dim]
        let seq_len = tensor_shape[1];
        let num_heads = tensor_shape[2];
        let head_dim = tensor_shape[3];

        let data = tensor.data();
        let start_idx = batch_idx * seq_len * num_heads * head_dim;
        let end_idx = start_idx + seq_len * num_heads * head_dim;

        let extracted_data = data[start_idx..end_idx].to_vec();
        let new_shape = Shape::new(vec![seq_len, num_heads, head_dim]);

        CpuTensor::from_data(extracted_data, new_shape).map_err(AttentionError::Tensor)
    }

    /// Expand single sequence tensor to batch format.
    fn expand_to_batch(
        &self,
        tensor: &CpuTensor<T>,
        batch_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let tensor_shape = tensor.shape().dims();
        let seq_len = tensor_shape[0];
        let num_heads = tensor_shape[1];
        let head_dim = tensor_shape[2];

        // Create new data by repeating the tensor for each batch
        let original_data = tensor.data();
        let mut new_data = Vec::with_capacity(original_data.len() * batch_size);

        for _ in 0..batch_size {
            new_data.extend_from_slice(original_data);
        }

        let new_shape = Shape::new(vec![batch_size, seq_len, num_heads, head_dim]);
        CpuTensor::from_data(new_data, new_shape).map_err(AttentionError::Tensor)
    }

    /// Compute attention with prepared tensors.
    fn compute_attention_with_tensors(
        &self,
        query: &CpuTensor<T>,
        key: &CpuTensor<T>,
        value: &CpuTensor<T>,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), AttentionError> {
        let q_shape = query.shape().dims();
        let batch_size = q_shape[0];
        let num_heads = q_shape[1];
        let seq_len_q = q_shape[2];
        let head_dim = q_shape[3];

        let k_shape = key.shape().dims();
        let seq_len_k = k_shape[2];

        // Flatten for attention computation: [batch_size * num_heads, seq_len, head_dim]
        let q_flat = self.flatten_heads(query, batch_size, num_heads, seq_len_q, head_dim)?;
        let k_flat = self.flatten_heads(key, batch_size, num_heads, seq_len_k, head_dim)?;
        let v_flat = self.flatten_heads(value, batch_size, num_heads, seq_len_k, head_dim)?;

        // Compute attention
        let (attn_output_flat, attn_weights_flat) = self.attention.compute_attention(
            &q_flat, &k_flat, &v_flat, None
        )?;

        // Reshape back to [batch_size, num_heads, seq_len_q, head_dim]
        let attn_output = self.unflatten_heads(
            &attn_output_flat, batch_size, num_heads, seq_len_q, head_dim
        )?;

        // Reshape attention weights to [batch_size, num_heads, seq_len_q, seq_len_k]
        let attn_weights = self.reshape_attention_weights(
            &attn_weights_flat, batch_size, num_heads, seq_len_q, seq_len_k
        )?;

        Ok((attn_output, attn_weights))
    }

    /// Flatten heads for batch processing.
    fn flatten_heads(
        &self,
        tensor: &CpuTensor<T>,
        batch_size: usize,
        num_heads: usize,
        seq_len: usize,
        head_dim: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let new_shape = Shape::new(vec![batch_size * num_heads, seq_len, head_dim]);
        TensorView::view_as(tensor, &new_shape).map_err(AttentionError::Tensor)
    }

    /// Unflatten heads after attention computation.
    fn unflatten_heads(
        &self,
        tensor: &CpuTensor<T>,
        batch_size: usize,
        num_heads: usize,
        seq_len: usize,
        head_dim: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let new_shape = Shape::new(vec![batch_size, num_heads, seq_len, head_dim]);
        TensorView::view_as(tensor, &new_shape).map_err(AttentionError::Tensor)
    }

    /// Reshape attention weights for output.
    fn reshape_attention_weights(
        &self,
        tensor: &CpuTensor<T>,
        batch_size: usize,
        num_heads: usize,
        seq_len_q: usize,
        seq_len_k: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let new_shape = Shape::new(vec![batch_size, num_heads, seq_len_q, seq_len_k]);
        TensorView::view_as(tensor, &new_shape).map_err(AttentionError::Tensor)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::cpu::CpuTensorFactory;
    use crate::tensor::TensorFactory;

    fn create_test_input(batch_size: usize, seq_len: usize, hidden_size: usize) -> CpuTensor<f32> {
        let shape = Shape::new(vec![batch_size, seq_len, hidden_size]);
        CpuTensorFactory::ones(&shape).unwrap()
    }

    #[test]
    fn test_incremental_attention_creation() {
        let config = AttentionConfig::new(64, 4).with_max_seq_len(1024);
        let attn = IncrementalAttention::<f32>::new(config.clone()).unwrap();

        assert_eq!(attn.config(), &config);
        assert_eq!(attn.cache_stats().num_sequences, 0);
    }

    #[test]
    fn test_forward_initial() {
        let config = AttentionConfig::new(64, 4).with_max_seq_len(1024);
        let mut attn = IncrementalAttention::<f32>::new(config).unwrap();

        let input = create_test_input(1, 5, 64);
        let result = attn.forward_initial("seq_1", &input);

        assert!(result.is_ok());
        let (output, attention_weights) = result.unwrap();

        // Check output shape
        assert_eq!(output.shape().dims(), &[1, 5, 64]);

        // Check attention weights shape
        assert_eq!(attention_weights.shape().dims(), &[1, 4, 5, 5]);

        // Check that sequence is cached
        assert!(attn.is_cached("seq_1"));
        assert_eq!(attn.get_sequence_length("seq_1"), Some(5));
    }

    #[test]
    fn test_forward_incremental() {
        let config = AttentionConfig::new(64, 4).with_max_seq_len(1024);
        let mut attn = IncrementalAttention::<f32>::new(config).unwrap();

        // Initial forward pass
        let initial_input = create_test_input(1, 3, 64);
        let _ = attn.forward_initial("seq_1", &initial_input).unwrap();

        // Incremental forward pass
        let new_input = create_test_input(1, 2, 64);
        let result = attn.forward_incremental("seq_1", &new_input);

        assert!(result.is_ok());
        let (output, attention_weights) = result.unwrap();

        // Check output shape (only for new tokens)
        assert_eq!(output.shape().dims(), &[1, 2, 64]);

        // Check attention weights shape (new queries attend to all keys)
        assert_eq!(attention_weights.shape().dims(), &[1, 4, 2, 5]); // 2 new queries, 5 total keys

        // Check that sequence length is updated
        assert_eq!(attn.get_sequence_length("seq_1"), Some(5));
    }

    #[test]
    fn test_incremental_without_initial() {
        let config = AttentionConfig::new(64, 4).with_max_seq_len(1024);
        let mut attn = IncrementalAttention::<f32>::new(config).unwrap();

        let input = create_test_input(1, 2, 64);
        let result = attn.forward_incremental("seq_1", &input);

        // Should fail because sequence is not cached
        assert!(result.is_err());
    }

    #[test]
    fn test_multiple_sequences() {
        let config = AttentionConfig::new(64, 4).with_max_seq_len(1024);
        let mut attn = IncrementalAttention::<f32>::new(config).unwrap();

        // Initialize two sequences
        let input1 = create_test_input(1, 3, 64);
        let input2 = create_test_input(1, 4, 64);

        let _ = attn.forward_initial("seq_1", &input1).unwrap();
        let _ = attn.forward_initial("seq_2", &input2).unwrap();

        assert!(attn.is_cached("seq_1"));
        assert!(attn.is_cached("seq_2"));
        assert_eq!(attn.get_sequence_length("seq_1"), Some(3));
        assert_eq!(attn.get_sequence_length("seq_2"), Some(4));

        // Incremental updates
        let new_input1 = create_test_input(1, 1, 64);
        let new_input2 = create_test_input(1, 2, 64);

        let _ = attn.forward_incremental("seq_1", &new_input1).unwrap();
        let _ = attn.forward_incremental("seq_2", &new_input2).unwrap();

        assert_eq!(attn.get_sequence_length("seq_1"), Some(4));
        assert_eq!(attn.get_sequence_length("seq_2"), Some(6));
    }

    #[test]
    fn test_cache_management() {
        let config = AttentionConfig::new(64, 4).with_max_seq_len(1024);
        let mut attn = IncrementalAttention::<f32>::new(config).unwrap();

        let input = create_test_input(1, 5, 64);
        let _ = attn.forward_initial("seq_1", &input).unwrap();

        assert!(attn.is_cached("seq_1"));

        // Clear specific sequence
        attn.clear_sequence("seq_1").unwrap();
        assert!(!attn.is_cached("seq_1"));

        // Re-initialize and clear all
        let _ = attn.forward_initial("seq_1", &input).unwrap();
        let _ = attn.forward_initial("seq_2", &input).unwrap();

        attn.clear_all();
        assert!(!attn.is_cached("seq_1"));
        assert!(!attn.is_cached("seq_2"));
    }

    #[test]
    fn test_input_validation() {
        let config = AttentionConfig::new(64, 4).with_max_seq_len(1024);
        let mut attn = IncrementalAttention::<f32>::new(config).unwrap();

        // Wrong number of dimensions
        let input_2d = CpuTensorFactory::ones(&Shape::new(vec![5, 64])).unwrap();
        let result = attn.forward_initial("seq_1", &input_2d);
        assert!(result.is_err());

        // Wrong hidden size
        let input_wrong_hidden = create_test_input(1, 5, 32); // Should be 64
        let result = attn.forward_initial("seq_1", &input_wrong_hidden);
        assert!(result.is_err());
    }

    #[test]
    fn test_cache_stats() {
        let config = AttentionConfig::new(64, 4).with_max_seq_len(1024);
        let mut attn = IncrementalAttention::<f32>::new(config).unwrap();

        let stats_empty = attn.cache_stats();
        assert_eq!(stats_empty.num_sequences, 0);

        let input = create_test_input(1, 5, 64);
        let _ = attn.forward_initial("seq_1", &input).unwrap();

        let stats_with_data = attn.cache_stats();
        assert_eq!(stats_with_data.num_sequences, 1);
        assert!(stats_with_data.memory_usage > 0);
    }

    #[test]
    fn test_sequence_length_growth() {
        let config = AttentionConfig::new(64, 4).with_max_seq_len(1024);
        let mut attn = IncrementalAttention::<f32>::new(config).unwrap();

        // Start with 2 tokens
        let initial_input = create_test_input(1, 2, 64);
        let _ = attn.forward_initial("seq_1", &initial_input).unwrap();
        assert_eq!(attn.get_sequence_length("seq_1"), Some(2));

        // Add 1 token
        let new_input1 = create_test_input(1, 1, 64);
        let _ = attn.forward_incremental("seq_1", &new_input1).unwrap();
        assert_eq!(attn.get_sequence_length("seq_1"), Some(3));

        // Add 3 more tokens
        let new_input2 = create_test_input(1, 3, 64);
        let _ = attn.forward_incremental("seq_1", &new_input2).unwrap();
        assert_eq!(attn.get_sequence_length("seq_1"), Some(6));
    }
}
