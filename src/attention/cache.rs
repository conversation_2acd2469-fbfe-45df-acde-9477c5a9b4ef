//! Attention cache management for efficient autoregressive generation.
//!
//! This module provides KV (Key-Value) caching functionality to optimize
//! autoregressive text generation. During generation, previously computed
//! key and value tensors can be cached and reused, avoiding redundant
//! computation for past tokens.
//!
//! Features:
//! - Efficient storage and retrieval of key-value pairs
//! - Dynamic cache size management
//! - Memory pool optimization
//! - Batch processing support
//! - Cache eviction policies

use std::collections::HashMap;
use crate::tensor::{Tensor, TensorOps, Shape, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::error::{CacheError, ErrorContext};

/// Configuration for KV cache.
#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// Maximum number of tokens to cache per sequence.
    pub max_seq_len: usize,
    /// Maximum number of sequences to cache simultaneously.
    pub max_batch_size: usize,
    /// Number of attention heads.
    pub num_heads: usize,
    /// Dimension of each attention head.
    pub head_dim: usize,
    /// Cache eviction policy.
    pub eviction_policy: EvictionPolicy,
    /// Whether to enable memory pooling.
    pub enable_pooling: bool,
}

/// Cache eviction policies.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum EvictionPolicy {
    /// Least Recently Used (LRU) eviction.
    LRU,
    /// First In, First Out (FIFO) eviction.
    FIFO,
    /// No eviction (fail when full).
    None,
}

impl CacheConfig {
    /// Create a new cache configuration.
    pub fn new(max_seq_len: usize, max_batch_size: usize, num_heads: usize, head_dim: usize) -> Self {
        Self {
            max_seq_len,
            max_batch_size,
            num_heads,
            head_dim,
            eviction_policy: EvictionPolicy::LRU,
            enable_pooling: true,
        }
    }

    /// Set eviction policy.
    pub fn with_eviction_policy(mut self, policy: EvictionPolicy) -> Self {
        self.eviction_policy = policy;
        self
    }

    /// Set memory pooling.
    pub fn with_pooling(mut self, enable: bool) -> Self {
        self.enable_pooling = enable;
        self
    }

    /// Calculate the memory footprint per sequence.
    pub fn memory_per_sequence(&self) -> usize {
        // Key + Value tensors: 2 * max_seq_len * num_heads * head_dim * sizeof(T)
        // Assuming f32 (4 bytes)
        2 * self.max_seq_len * self.num_heads * self.head_dim * 4
    }

    /// Calculate total memory footprint.
    pub fn total_memory(&self) -> usize {
        self.memory_per_sequence() * self.max_batch_size
    }

    /// Validate the configuration.
    pub fn validate(&self) -> Result<(), String> {
        if self.max_seq_len == 0 {
            return Err("max_seq_len must be greater than 0".to_string());
        }

        if self.max_batch_size == 0 {
            return Err("max_batch_size must be greater than 0".to_string());
        }

        if self.num_heads == 0 {
            return Err("num_heads must be greater than 0".to_string());
        }

        if self.head_dim == 0 {
            return Err("head_dim must be greater than 0".to_string());
        }

        // Check for reasonable memory usage (warn if > 1GB)
        let total_mem = self.total_memory();
        if total_mem > 1_000_000_000 {
            return Err(format!(
                "Cache configuration requires too much memory: {} bytes (> 1GB)",
                total_mem
            ));
        }

        Ok(())
    }
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self::new(2048, 32, 8, 64) // Common defaults
    }
}

/// Entry in the KV cache for a single sequence.
#[derive(Debug, Clone)]
struct CacheEntry<T: Numeric> {
    /// Cached key tensor: [seq_len, num_heads, head_dim]
    keys: CpuTensor<T>,
    /// Cached value tensor: [seq_len, num_heads, head_dim]
    values: CpuTensor<T>,
    /// Current sequence length.
    seq_len: usize,
    /// Last access timestamp for LRU eviction.
    last_access: u64,
    /// Creation timestamp for FIFO eviction.
    created_at: u64,
}

impl<T: Numeric> CacheEntry<T> {
    /// Create a new cache entry.
    fn new(
        keys: CpuTensor<T>,
        values: CpuTensor<T>,
        seq_len: usize,
        timestamp: u64,
    ) -> Self {
        Self {
            keys,
            values,
            seq_len,
            last_access: timestamp,
            created_at: timestamp,
        }
    }

    /// Update the last access timestamp.
    fn touch(&mut self, timestamp: u64) {
        self.last_access = timestamp;
    }
}

/// KV cache for efficient autoregressive generation.
///
/// This cache stores key and value tensors from previous attention computations
/// to avoid redundant calculations during autoregressive text generation.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::attention::cache::{KVCache, CacheConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create cache configuration
/// let config = CacheConfig::new(1024, 16, 8, 64);
/// let mut cache = KVCache::new(config).unwrap();
///
/// // Store key-value pairs
/// let keys = CpuTensor::zeros(&Shape::new(vec![10, 8, 64])).unwrap();
/// let values = CpuTensor::zeros(&Shape::new(vec![10, 8, 64])).unwrap();
/// cache.store("seq_1", keys, values).unwrap();
///
/// // Retrieve cached values
/// let (cached_keys, cached_values) = cache.get("seq_1").unwrap();
/// ```
#[derive(Debug)]
pub struct KVCache<T: Numeric> {
    /// Cache configuration.
    config: CacheConfig,
    /// Storage for cache entries.
    entries: HashMap<String, CacheEntry<T>>,
    /// Current timestamp for LRU/FIFO tracking.
    current_timestamp: u64,
    /// Current memory usage in bytes.
    memory_usage: usize,
}

impl<T: Numeric> KVCache<T> {
    /// Create a new KV cache.
    pub fn new(config: CacheConfig) -> Result<Self, CacheError> {
        config.validate().map_err(|msg| CacheError::InvalidState {
            reason: msg,
            current_state: "uninitialized".to_string(),
            expected_state: "valid configuration".to_string(),
            context: Some(ErrorContext::new("new", "attention::cache")),
        })?;

        Ok(Self {
            config,
            entries: HashMap::new(),
            current_timestamp: 0,
            memory_usage: 0,
        })
    }

    /// Get the cache configuration.
    pub fn config(&self) -> &CacheConfig {
        &self.config
    }

    /// Get the current number of cached sequences.
    pub fn len(&self) -> usize {
        self.entries.len()
    }

    /// Check if the cache is empty.
    pub fn is_empty(&self) -> bool {
        self.entries.is_empty()
    }

    /// Get current memory usage in bytes.
    pub fn memory_usage(&self) -> usize {
        self.memory_usage
    }

    /// Get cache utilization as a percentage.
    pub fn utilization(&self) -> f32 {
        if self.config.max_batch_size == 0 {
            0.0
        } else {
            (self.len() as f32) / (self.config.max_batch_size as f32) * 100.0
        }
    }

    /// Store key-value pairs in the cache.
    ///
    /// # Arguments
    /// * `sequence_id` - Unique identifier for the sequence
    /// * `keys` - Key tensor with shape [seq_len, num_heads, head_dim]
    /// * `values` - Value tensor with shape [seq_len, num_heads, head_dim]
    pub fn store(
        &mut self,
        sequence_id: &str,
        keys: CpuTensor<T>,
        values: CpuTensor<T>,
    ) -> Result<(), CacheError> {
        // Validate tensor shapes
        self.validate_tensors(&keys, &values)?;

        let seq_len = keys.shape().dims()[0];

        // Check sequence length limit
        if seq_len > self.config.max_seq_len {
            return Err(CacheError::CapacityExceeded {
                current_size: seq_len,
                max_capacity: self.config.max_seq_len,
                cache_type: "sequence_length".to_string(),
                context: Some(ErrorContext::new("store", "attention::cache")),
            });
        }

        // Check if we need to evict entries
        if self.entries.len() >= self.config.max_batch_size && !self.entries.contains_key(sequence_id) {
            self.evict_if_needed()?;
        }

        // Calculate memory usage for this entry
        let entry_memory = self.calculate_entry_memory(&keys, &values);

        // Update timestamp
        self.current_timestamp += 1;

        // Create and store the entry
        let entry = CacheEntry::new(keys, values, seq_len, self.current_timestamp);

        // Update memory usage
        if let Some(old_entry) = self.entries.insert(sequence_id.to_string(), entry) {
            // Replace existing entry - subtract old memory, add new memory
            let old_memory = self.calculate_entry_memory(&old_entry.keys, &old_entry.values);
            self.memory_usage = self.memory_usage.saturating_sub(old_memory) + entry_memory;
        } else {
            // New entry
            self.memory_usage += entry_memory;
        }

        Ok(())
    }

    /// Retrieve cached key-value pairs.
    ///
    /// # Arguments
    /// * `sequence_id` - Unique identifier for the sequence
    ///
    /// # Returns
    /// * `(keys, values)` - Cached key and value tensors
    pub fn get(&mut self, sequence_id: &str) -> Result<(CpuTensor<T>, CpuTensor<T>), CacheError> {
        let entry = self.entries.get_mut(sequence_id).ok_or_else(|| CacheError::KeyNotFound {
            key: sequence_id.to_string(),
            cache_type: "kv_cache".to_string(),
            context: Some(ErrorContext::new("get", "attention::cache")),
        })?;

        // Update access timestamp for LRU
        self.current_timestamp += 1;
        entry.touch(self.current_timestamp);

        Ok((entry.keys.clone(), entry.values.clone()))
    }

    /// Append new key-value pairs to existing cache entry.
    ///
    /// This is used for incremental generation where new tokens are added
    /// to an existing sequence.
    ///
    /// # Arguments
    /// * `sequence_id` - Unique identifier for the sequence
    /// * `new_keys` - New key tensor to append
    /// * `new_values` - New value tensor to append
    pub fn append(
        &mut self,
        sequence_id: &str,
        new_keys: CpuTensor<T>,
        new_values: CpuTensor<T>,
    ) -> Result<(), CacheError> {
        // Validate new tensors
        self.validate_tensors(&new_keys, &new_values)?;

        // First, get the current entry data without holding a mutable reference
        let (current_keys, current_values, current_seq_len) = {
            let entry = self.entries.get(sequence_id).ok_or_else(|| CacheError::KeyNotFound {
                key: sequence_id.to_string(),
                cache_type: "kv_cache".to_string(),
                context: Some(ErrorContext::new("append", "attention::cache")),
            })?;
            (entry.keys.clone(), entry.values.clone(), entry.seq_len)
        };

        let new_seq_len = new_keys.shape().dims()[0];
        let total_seq_len = current_seq_len + new_seq_len;

        // Check sequence length limit
        if total_seq_len > self.config.max_seq_len {
            return Err(CacheError::CapacityExceeded {
                current_size: total_seq_len,
                max_capacity: self.config.max_seq_len,
                cache_type: "sequence_length".to_string(),
                context: Some(ErrorContext::new("append", "attention::cache")),
            });
        }

        // Calculate old memory
        let old_memory = self.calculate_entry_memory(&current_keys, &current_values);

        // Concatenate tensors along sequence dimension
        let concatenated_keys = self.concatenate_tensors(&current_keys, &new_keys)?;
        let concatenated_values = self.concatenate_tensors(&current_values, &new_values)?;

        // Calculate new memory
        let new_memory = self.calculate_entry_memory(&concatenated_keys, &concatenated_values);

        // Now get mutable reference and update
        let entry = self.entries.get_mut(sequence_id).unwrap(); // Safe because we checked existence above
        entry.keys = concatenated_keys;
        entry.values = concatenated_values;
        entry.seq_len = total_seq_len;

        // Update access timestamp
        self.current_timestamp += 1;
        entry.touch(self.current_timestamp);

        // Update memory usage
        self.memory_usage = self.memory_usage.saturating_sub(old_memory) + new_memory;

        Ok(())
    }

    /// Remove a sequence from the cache.
    pub fn remove(&mut self, sequence_id: &str) -> Result<(), CacheError> {
        if let Some(entry) = self.entries.remove(sequence_id) {
            let entry_memory = self.calculate_entry_memory(&entry.keys, &entry.values);
            self.memory_usage = self.memory_usage.saturating_sub(entry_memory);
        }
        Ok(())
    }

    /// Clear all entries from the cache.
    pub fn clear(&mut self) {
        self.entries.clear();
        self.memory_usage = 0;
        self.current_timestamp = 0;
    }

    /// Check if a sequence is cached.
    pub fn contains(&self, sequence_id: &str) -> bool {
        self.entries.contains_key(sequence_id)
    }

    /// Get the sequence length for a cached sequence.
    pub fn get_sequence_length(&self, sequence_id: &str) -> Option<usize> {
        self.entries.get(sequence_id).map(|entry| entry.seq_len)
    }

    /// Get statistics about the cache.
    pub fn stats(&self) -> CacheStats {
        CacheStats {
            num_sequences: self.len(),
            memory_usage: self.memory_usage,
            utilization: self.utilization(),
            max_capacity: self.config.max_batch_size,
            max_memory: self.config.total_memory(),
        }
    }

    /// Validate tensor shapes.
    fn validate_tensors(&self, keys: &CpuTensor<T>, values: &CpuTensor<T>) -> Result<(), CacheError> {
        let key_shape = keys.shape().dims();
        let value_shape = values.shape().dims();

        // Both tensors should be 3D: [seq_len, num_heads, head_dim]
        if key_shape.len() != 3 || value_shape.len() != 3 {
            return Err(CacheError::InvalidState {
                reason: "tensors must be 3D".to_string(),
                current_state: format!("key: {:?}, value: {:?}", key_shape, value_shape),
                expected_state: "[seq_len, num_heads, head_dim]".to_string(),
                context: Some(ErrorContext::new("validate_tensors", "attention::cache")),
            });
        }

        // Shapes must match
        if key_shape != value_shape {
            return Err(CacheError::InvalidState {
                reason: "key and value shapes must match".to_string(),
                current_state: format!("key: {:?}, value: {:?}", key_shape, value_shape),
                expected_state: "matching shapes".to_string(),
                context: Some(ErrorContext::new("validate_tensors", "attention::cache")),
            });
        }

        // Check dimensions match configuration
        if key_shape[1] != self.config.num_heads || key_shape[2] != self.config.head_dim {
            return Err(CacheError::InvalidState {
                reason: "tensor dimensions don't match cache configuration".to_string(),
                current_state: format!("heads: {}, head_dim: {}", key_shape[1], key_shape[2]),
                expected_state: format!("heads: {}, head_dim: {}", self.config.num_heads, self.config.head_dim),
                context: Some(ErrorContext::new("validate_tensors", "attention::cache")),
            });
        }

        Ok(())
    }

    /// Calculate memory usage for a cache entry.
    fn calculate_entry_memory(&self, keys: &CpuTensor<T>, values: &CpuTensor<T>) -> usize {
        let key_size = keys.shape().size() * std::mem::size_of::<T>();
        let value_size = values.shape().size() * std::mem::size_of::<T>();
        key_size + value_size
    }

    /// Concatenate tensors along the sequence dimension.
    fn concatenate_tensors(
        &self,
        tensor1: &CpuTensor<T>,
        tensor2: &CpuTensor<T>,
    ) -> Result<CpuTensor<T>, CacheError> {
        let shape1 = tensor1.shape().dims();
        let shape2 = tensor2.shape().dims();

        // Ensure compatible shapes
        if shape1[1] != shape2[1] || shape1[2] != shape2[2] {
            return Err(CacheError::InvalidState {
                reason: "incompatible tensor shapes for concatenation".to_string(),
                current_state: format!("tensor1: {:?}, tensor2: {:?}", shape1, shape2),
                expected_state: "matching head and head_dim dimensions".to_string(),
                context: Some(ErrorContext::new("concatenate_tensors", "attention::cache")),
            });
        }

        // Create new shape
        let new_seq_len = shape1[0] + shape2[0];
        let new_shape = Shape::new(vec![new_seq_len, shape1[1], shape1[2]]);

        // Concatenate data
        let data1 = tensor1.data();
        let data2 = tensor2.data();
        let mut new_data = Vec::with_capacity(data1.len() + data2.len());
        new_data.extend_from_slice(data1);
        new_data.extend_from_slice(data2);

        CpuTensor::from_data(new_data, new_shape).map_err(CacheError::Tensor)
    }

    /// Evict entries if needed based on the eviction policy.
    fn evict_if_needed(&mut self) -> Result<(), CacheError> {
        if self.entries.len() < self.config.max_batch_size {
            return Ok(());
        }

        match self.config.eviction_policy {
            EvictionPolicy::None => {
                return Err(CacheError::CapacityExceeded {
                    current_size: self.entries.len(),
                    max_capacity: self.config.max_batch_size,
                    cache_type: "batch_size".to_string(),
                    context: Some(ErrorContext::new("evict_if_needed", "attention::cache")),
                });
            }
            EvictionPolicy::LRU => {
                self.evict_lru()?;
            }
            EvictionPolicy::FIFO => {
                self.evict_fifo()?;
            }
        }

        Ok(())
    }

    /// Evict the least recently used entry.
    fn evict_lru(&mut self) -> Result<(), CacheError> {
        let oldest_key = self.entries
            .iter()
            .min_by_key(|(_, entry)| entry.last_access)
            .map(|(key, _)| key.clone());

        if let Some(key) = oldest_key {
            self.remove(&key)?;
        }

        Ok(())
    }

    /// Evict the first in, first out entry.
    fn evict_fifo(&mut self) -> Result<(), CacheError> {
        let oldest_key = self.entries
            .iter()
            .min_by_key(|(_, entry)| entry.created_at)
            .map(|(key, _)| key.clone());

        if let Some(key) = oldest_key {
            self.remove(&key)?;
        }

        Ok(())
    }
}

/// Cache statistics.
#[derive(Debug, Clone)]
pub struct CacheStats {
    /// Number of cached sequences.
    pub num_sequences: usize,
    /// Current memory usage in bytes.
    pub memory_usage: usize,
    /// Cache utilization percentage.
    pub utilization: f32,
    /// Maximum number of sequences.
    pub max_capacity: usize,
    /// Maximum memory usage in bytes.
    pub max_memory: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::cpu::CpuTensorFactory;
    use crate::tensor::TensorFactory;

    fn create_test_tensors(seq_len: usize, num_heads: usize, head_dim: usize) -> (CpuTensor<f32>, CpuTensor<f32>) {
        let shape = Shape::new(vec![seq_len, num_heads, head_dim]);
        let keys = CpuTensorFactory::ones(&shape).unwrap();
        let values = CpuTensorFactory::zeros(&shape).unwrap();
        (keys, values)
    }

    #[test]
    fn test_cache_config() {
        let config = CacheConfig::new(1024, 16, 8, 64);
        assert_eq!(config.max_seq_len, 1024);
        assert_eq!(config.max_batch_size, 16);
        assert_eq!(config.num_heads, 8);
        assert_eq!(config.head_dim, 64);
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_cache_config_validation() {
        // Invalid configurations
        let config = CacheConfig::new(0, 16, 8, 64);
        assert!(config.validate().is_err());

        let config = CacheConfig::new(1024, 0, 8, 64);
        assert!(config.validate().is_err());

        let config = CacheConfig::new(1024, 16, 0, 64);
        assert!(config.validate().is_err());

        let config = CacheConfig::new(1024, 16, 8, 0);
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_cache_creation() {
        let config = CacheConfig::new(1024, 16, 8, 64);
        let cache = KVCache::<f32>::new(config).unwrap();

        assert_eq!(cache.len(), 0);
        assert!(cache.is_empty());
        assert_eq!(cache.memory_usage(), 0);
        assert_eq!(cache.utilization(), 0.0);
    }

    #[test]
    fn test_cache_store_and_get() {
        let config = CacheConfig::new(1024, 16, 8, 64);
        let mut cache = KVCache::<f32>::new(config).unwrap();

        let (keys, values) = create_test_tensors(10, 8, 64);

        // Store key-value pair
        cache.store("seq_1", keys.clone(), values.clone()).unwrap();

        assert_eq!(cache.len(), 1);
        assert!(!cache.is_empty());
        assert!(cache.contains("seq_1"));
        assert_eq!(cache.get_sequence_length("seq_1"), Some(10));

        // Retrieve key-value pair
        let (cached_keys, cached_values) = cache.get("seq_1").unwrap();
        assert_eq!(cached_keys.shape().dims(), keys.shape().dims());
        assert_eq!(cached_values.shape().dims(), values.shape().dims());
    }

    #[test]
    fn test_cache_append() {
        let config = CacheConfig::new(1024, 16, 8, 64);
        let mut cache = KVCache::<f32>::new(config).unwrap();

        // Store initial key-value pair
        let (keys1, values1) = create_test_tensors(5, 8, 64);
        cache.store("seq_1", keys1, values1).unwrap();

        // Append new key-value pair
        let (keys2, values2) = create_test_tensors(3, 8, 64);
        cache.append("seq_1", keys2, values2).unwrap();

        // Check that sequence length is updated
        assert_eq!(cache.get_sequence_length("seq_1"), Some(8));

        // Retrieve and check shape
        let (cached_keys, cached_values) = cache.get("seq_1").unwrap();
        assert_eq!(cached_keys.shape().dims(), &[8, 8, 64]);
        assert_eq!(cached_values.shape().dims(), &[8, 8, 64]);
    }

    #[test]
    fn test_cache_capacity_exceeded() {
        let config = CacheConfig::new(5, 2, 8, 64); // Small limits for testing
        let mut cache = KVCache::<f32>::new(config).unwrap();

        // Test sequence length limit
        let (keys, values) = create_test_tensors(10, 8, 64); // Exceeds max_seq_len
        let result = cache.store("seq_1", keys, values);
        assert!(result.is_err());

        // Test batch size limit with LRU eviction
        let config = CacheConfig::new(1024, 2, 8, 64).with_eviction_policy(EvictionPolicy::LRU);
        let mut cache = KVCache::<f32>::new(config).unwrap();

        let (keys1, values1) = create_test_tensors(5, 8, 64);
        let (keys2, values2) = create_test_tensors(5, 8, 64);
        let (keys3, values3) = create_test_tensors(5, 8, 64);

        cache.store("seq_1", keys1, values1).unwrap();
        cache.store("seq_2", keys2, values2).unwrap();

        // This should evict seq_1 (LRU)
        cache.store("seq_3", keys3, values3).unwrap();

        assert_eq!(cache.len(), 2);
        assert!(!cache.contains("seq_1"));
        assert!(cache.contains("seq_2"));
        assert!(cache.contains("seq_3"));
    }

    #[test]
    fn test_cache_eviction_policies() {
        // Test FIFO eviction
        let config = CacheConfig::new(1024, 2, 8, 64).with_eviction_policy(EvictionPolicy::FIFO);
        let mut cache = KVCache::<f32>::new(config).unwrap();

        let (keys1, values1) = create_test_tensors(5, 8, 64);
        let (keys2, values2) = create_test_tensors(5, 8, 64);
        let (keys3, values3) = create_test_tensors(5, 8, 64);

        cache.store("seq_1", keys1, values1).unwrap();
        cache.store("seq_2", keys2, values2).unwrap();

        // Access seq_1 to make it more recently used
        let _ = cache.get("seq_1").unwrap();

        // This should still evict seq_1 (FIFO, not LRU)
        cache.store("seq_3", keys3, values3).unwrap();

        assert_eq!(cache.len(), 2);
        assert!(!cache.contains("seq_1"));
        assert!(cache.contains("seq_2"));
        assert!(cache.contains("seq_3"));
    }

    #[test]
    fn test_cache_clear_and_remove() {
        let config = CacheConfig::new(1024, 16, 8, 64);
        let mut cache = KVCache::<f32>::new(config).unwrap();

        let (keys1, values1) = create_test_tensors(5, 8, 64);
        let (keys2, values2) = create_test_tensors(5, 8, 64);

        cache.store("seq_1", keys1, values1).unwrap();
        cache.store("seq_2", keys2, values2).unwrap();

        assert_eq!(cache.len(), 2);

        // Remove one entry
        cache.remove("seq_1").unwrap();
        assert_eq!(cache.len(), 1);
        assert!(!cache.contains("seq_1"));
        assert!(cache.contains("seq_2"));

        // Clear all entries
        cache.clear();
        assert_eq!(cache.len(), 0);
        assert!(cache.is_empty());
        assert_eq!(cache.memory_usage(), 0);
    }

    #[test]
    fn test_cache_stats() {
        let config = CacheConfig::new(1024, 4, 8, 64);
        let mut cache = KVCache::<f32>::new(config).unwrap();

        let (keys, values) = create_test_tensors(10, 8, 64);
        cache.store("seq_1", keys, values).unwrap();

        let stats = cache.stats();
        assert_eq!(stats.num_sequences, 1);
        assert_eq!(stats.max_capacity, 4);
        assert_eq!(stats.utilization, 25.0); // 1/4 * 100
        assert!(stats.memory_usage > 0);
    }

    #[test]
    fn test_invalid_tensor_shapes() {
        let config = CacheConfig::new(1024, 16, 8, 64);
        let mut cache = KVCache::<f32>::new(config).unwrap();

        // Wrong number of dimensions
        let keys = CpuTensorFactory::ones(&Shape::new(vec![10, 8])).unwrap(); // 2D instead of 3D
        let values = CpuTensorFactory::ones(&Shape::new(vec![10, 8, 64])).unwrap();

        let result = cache.store("seq_1", keys, values);
        assert!(result.is_err());

        // Mismatched key-value shapes
        let keys = CpuTensorFactory::ones(&Shape::new(vec![10, 8, 64])).unwrap();
        let values = CpuTensorFactory::ones(&Shape::new(vec![5, 8, 64])).unwrap(); // Different seq_len

        let result = cache.store("seq_1", keys, values);
        assert!(result.is_err());

        // Wrong head dimensions
        let keys = CpuTensorFactory::ones(&Shape::new(vec![10, 4, 64])).unwrap(); // Wrong num_heads
        let values = CpuTensorFactory::ones(&Shape::new(vec![10, 4, 64])).unwrap();

        let result = cache.store("seq_1", keys, values);
        assert!(result.is_err());
    }
}
