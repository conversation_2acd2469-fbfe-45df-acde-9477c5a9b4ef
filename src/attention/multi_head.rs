//! Multi-head attention implementation.
//!
//! This module implements multi-head attention, which runs multiple attention
//! heads in parallel and concatenates their outputs. The implementation follows
//! the standard Transformer architecture:
//!
//! MultiHead(Q, K, V) = Concat(head_1, ..., head_h)W^O
//! where head_i = Attention(QW_i^Q, KW_i^K, VW_i^V)
//!
//! Features:
//! - Parallel computation of multiple attention heads
//! - Linear projections for Q, K, V, and output
//! - Support for different attention implementations
//! - Efficient memory usage with proper tensor reshaping

use std::marker::PhantomData;
use crate::tensor::{Tensor, TensorOps, TensorView, Shape, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::layers::{Layer, linear::{Linear, LinearConfig}, ParameterizedLayer, ParameterInit};
use crate::error::{AttentionError, ErrorContext};
use super::{Attention, AttentionConfig};
use super::scaled_dot_product::ScaledDotProductAttention;

/// Multi-head attention layer.
///
/// This layer implements the multi-head attention mechanism from the
/// "Attention Is All You Need" paper. It consists of:
/// - Linear projections for queries, keys, and values
/// - Multiple parallel attention heads
/// - Output projection to combine head outputs
///
/// # Examples
///
/// ```rust
/// use qilin_inference::attention::{MultiHeadAttention, AttentionConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create multi-head attention with 8 heads and 512 hidden size
/// let config = AttentionConfig::new(512, 8);
/// let mut mha = MultiHeadAttention::new(config).unwrap();
///
/// // Forward pass
/// let input = CpuTensor::randn(&Shape::new(vec![2, 10, 512]), 0.0, 1.0).unwrap();
/// let output = mha.forward(&input, &input, &input, None).unwrap();
/// assert_eq!(output.shape().dims(), &[2, 10, 512]);
/// ```
#[derive(Debug, Clone)]
pub struct MultiHeadAttention<T: Numeric> {
    /// Configuration for the attention layer.
    config: AttentionConfig,
    /// Linear projection for queries.
    q_proj: Linear<T>,
    /// Linear projection for keys.
    k_proj: Linear<T>,
    /// Linear projection for values.
    v_proj: Linear<T>,
    /// Output projection.
    out_proj: Linear<T>,
    /// Attention implementation for each head.
    attention: ScaledDotProductAttention<T>,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> MultiHeadAttention<T> {
    /// Create a new multi-head attention layer.
    pub fn new(config: AttentionConfig) -> Result<Self, AttentionError> {
        // Validate configuration
        config.validate().map_err(|msg| AttentionError::InvalidConfiguration {
            message: msg,
            config_field: None,
            context: Some(ErrorContext::new("new", "attention::multi_head")),
        })?;

        let hidden_size = config.hidden_size;
        let head_dim = config.head_dim();

        // Create linear projections
        let linear_config = LinearConfig::new(hidden_size, hidden_size)
            .with_bias(config.use_bias);

        let mut q_proj = Linear::new(linear_config.clone())
            .map_err(AttentionError::Tensor)?;
        let mut k_proj = Linear::new(linear_config.clone())
            .map_err(AttentionError::Tensor)?;
        let mut v_proj = Linear::new(linear_config.clone())
            .map_err(AttentionError::Tensor)?;
        let mut out_proj = Linear::new(linear_config)
            .map_err(AttentionError::Tensor)?;

        // Initialize parameters
        use crate::layers::ParameterizedLayer;
        use crate::layers::ParameterInit;
        q_proj.init_parameters(ParameterInit::XavierUniform).map_err(AttentionError::Tensor)?;
        k_proj.init_parameters(ParameterInit::XavierUniform).map_err(AttentionError::Tensor)?;
        v_proj.init_parameters(ParameterInit::XavierUniform).map_err(AttentionError::Tensor)?;
        out_proj.init_parameters(ParameterInit::XavierUniform).map_err(AttentionError::Tensor)?;

        // Create attention mechanism
        let attention = ScaledDotProductAttention::new(
            head_dim,
            config.dropout,
            config.causal,
        )?;

        Ok(Self {
            config,
            q_proj,
            k_proj,
            v_proj,
            out_proj,
            attention,
            _phantom: PhantomData,
        })
    }

    /// Get the configuration.
    pub fn config(&self) -> &AttentionConfig {
        &self.config
    }

    /// Get the number of attention heads.
    pub fn num_heads(&self) -> usize {
        self.config.num_heads
    }

    /// Get the dimension of each head.
    pub fn head_dim(&self) -> usize {
        self.config.head_dim()
    }

    /// Get the hidden size.
    pub fn hidden_size(&self) -> usize {
        self.config.hidden_size
    }

    /// Initialize all parameters using the specified initialization strategy.
    pub fn init_parameters(&mut self, init: ParameterInit) -> Result<(), AttentionError> {
        use crate::layers::ParameterizedLayer;

        self.q_proj.init_parameters(init.clone()).map_err(AttentionError::Tensor)?;
        self.k_proj.init_parameters(init.clone()).map_err(AttentionError::Tensor)?;
        self.v_proj.init_parameters(init.clone()).map_err(AttentionError::Tensor)?;
        self.out_proj.init_parameters(init).map_err(AttentionError::Tensor)?;

        Ok(())
    }

    /// Forward pass through multi-head attention.
    ///
    /// # Arguments
    /// * `query` - Query tensor with shape [batch_size, seq_len_q, hidden_size]
    /// * `key` - Key tensor with shape [batch_size, seq_len_k, hidden_size]
    /// * `value` - Value tensor with shape [batch_size, seq_len_v, hidden_size]
    /// * `mask` - Optional attention mask
    ///
    /// # Returns
    /// * `output` - Output tensor with shape [batch_size, seq_len_q, hidden_size]
    /// * `attention_weights` - Attention weights for visualization/analysis
    pub fn forward(
        &self,
        query: &CpuTensor<T>,
        key: &CpuTensor<T>,
        value: &CpuTensor<T>,
        mask: Option<&CpuTensor<T>>,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), AttentionError> {
        // Validate input shapes
        self.validate_inputs(query, key, value)?;

        let batch_size = query.shape().dims()[0];
        let seq_len_q = query.shape().dims()[1];
        let seq_len_k = key.shape().dims()[1];
        let hidden_size = self.config.hidden_size;
        let num_heads = self.config.num_heads;
        let head_dim = self.config.head_dim();

        // Apply linear projections: [batch_size, seq_len, hidden_size]
        let q = self.q_proj.forward(query.clone()).map_err(AttentionError::Tensor)?;
        let k = self.k_proj.forward(key.clone()).map_err(AttentionError::Tensor)?;
        let v = self.v_proj.forward(value.clone()).map_err(AttentionError::Tensor)?;

        // Reshape to multi-head format: [batch_size, seq_len, num_heads, head_dim]
        let q_heads = self.reshape_for_heads(&q, batch_size, seq_len_q, num_heads, head_dim)?;
        let k_heads = self.reshape_for_heads(&k, batch_size, seq_len_k, num_heads, head_dim)?;
        let v_heads = self.reshape_for_heads(&v, batch_size, seq_len_k, num_heads, head_dim)?;

        // Transpose to [batch_size, num_heads, seq_len, head_dim]
        let q_transposed = Tensor::transpose(&q_heads, 1, 2).map_err(AttentionError::Tensor)?;
        let k_transposed = Tensor::transpose(&k_heads, 1, 2).map_err(AttentionError::Tensor)?;
        let v_transposed = Tensor::transpose(&v_heads, 1, 2).map_err(AttentionError::Tensor)?;

        // Reshape for attention computation: [batch_size * num_heads, seq_len, head_dim]
        let q_flat = self.flatten_heads(&q_transposed, batch_size, num_heads, seq_len_q, head_dim)?;
        let k_flat = self.flatten_heads(&k_transposed, batch_size, num_heads, seq_len_k, head_dim)?;
        let v_flat = self.flatten_heads(&v_transposed, batch_size, num_heads, seq_len_k, head_dim)?;

        // Apply attention to all heads simultaneously
        let (attn_output_flat, attn_weights_flat) = self.attention.compute_attention(
            &q_flat, &k_flat, &v_flat, mask
        )?;

        // Reshape attention output back: [batch_size, num_heads, seq_len_q, head_dim]
        let attn_output_heads = self.unflatten_heads(
            &attn_output_flat, batch_size, num_heads, seq_len_q, head_dim
        )?;

        // Transpose back: [batch_size, seq_len_q, num_heads, head_dim]
        let attn_output_transposed = Tensor::transpose(&attn_output_heads, 1, 2).map_err(AttentionError::Tensor)?;

        // Concatenate heads: [batch_size, seq_len_q, hidden_size]
        let attn_output_concat = self.concatenate_heads(
            &attn_output_transposed, batch_size, seq_len_q, hidden_size
        )?;

        // Apply output projection
        let output = self.out_proj.forward(attn_output_concat).map_err(AttentionError::Tensor)?;

        // Reshape attention weights for return: [batch_size, num_heads, seq_len_q, seq_len_k]
        let attn_weights = self.reshape_attention_weights(
            &attn_weights_flat, batch_size, num_heads, seq_len_q, seq_len_k
        )?;

        Ok((output, attn_weights))
    }

    /// Validate input tensor shapes.
    fn validate_inputs(
        &self,
        query: &CpuTensor<T>,
        key: &CpuTensor<T>,
        value: &CpuTensor<T>,
    ) -> Result<(), AttentionError> {
        let q_shape = query.shape().dims();
        let k_shape = key.shape().dims();
        let v_shape = value.shape().dims();

        // All tensors should be 3D
        if q_shape.len() != 3 || k_shape.len() != 3 || v_shape.len() != 3 {
            return Err(AttentionError::DimensionMismatch {
                expected: "3D tensors [batch_size, seq_len, hidden_size]".to_string(),
                actual: format!("Q: {:?}, K: {:?}, V: {:?}", q_shape, k_shape, v_shape),
                operation: "input_validation".to_string(),
                context: Some(ErrorContext::new("validate_inputs", "attention::multi_head")),
            });
        }

        // Batch sizes must match
        if q_shape[0] != k_shape[0] || q_shape[0] != v_shape[0] {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("batch_size: {}", q_shape[0]),
                actual: format!("Q: {}, K: {}, V: {}", q_shape[0], k_shape[0], v_shape[0]),
                operation: "batch_size_validation".to_string(),
                context: Some(ErrorContext::new("validate_inputs", "attention::multi_head")),
            });
        }

        // Hidden dimensions must match
        if q_shape[2] != self.config.hidden_size ||
           k_shape[2] != self.config.hidden_size ||
           v_shape[2] != self.config.hidden_size {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", self.config.hidden_size),
                actual: format!("Q: {}, K: {}, V: {}", q_shape[2], k_shape[2], v_shape[2]),
                operation: "hidden_size_validation".to_string(),
                context: Some(ErrorContext::new("validate_inputs", "attention::multi_head")),
            });
        }

        // Key and value sequence lengths must match
        if k_shape[1] != v_shape[1] {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("seq_len: {}", k_shape[1]),
                actual: format!("K: {}, V: {}", k_shape[1], v_shape[1]),
                operation: "seq_len_validation".to_string(),
                context: Some(ErrorContext::new("validate_inputs", "attention::multi_head")),
            });
        }

        Ok(())
    }

    /// Reshape tensor for multi-head attention.
    /// From [batch_size, seq_len, hidden_size] to [batch_size, seq_len, num_heads, head_dim]
    fn reshape_for_heads(
        &self,
        tensor: &CpuTensor<T>,
        batch_size: usize,
        seq_len: usize,
        num_heads: usize,
        head_dim: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let new_shape = Shape::new(vec![batch_size, seq_len, num_heads, head_dim]);
        TensorView::view_as(tensor, &new_shape).map_err(AttentionError::Tensor)
    }

    /// Flatten heads for batch processing.
    /// From [batch_size, num_heads, seq_len, head_dim] to [batch_size * num_heads, seq_len, head_dim]
    fn flatten_heads(
        &self,
        tensor: &CpuTensor<T>,
        batch_size: usize,
        num_heads: usize,
        seq_len: usize,
        head_dim: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let new_shape = Shape::new(vec![batch_size * num_heads, seq_len, head_dim]);
        TensorView::view_as(tensor, &new_shape).map_err(AttentionError::Tensor)
    }

    /// Unflatten heads after attention computation.
    /// From [batch_size * num_heads, seq_len, head_dim] to [batch_size, num_heads, seq_len, head_dim]
    fn unflatten_heads(
        &self,
        tensor: &CpuTensor<T>,
        batch_size: usize,
        num_heads: usize,
        seq_len: usize,
        head_dim: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let new_shape = Shape::new(vec![batch_size, num_heads, seq_len, head_dim]);
        TensorView::view_as(tensor, &new_shape).map_err(AttentionError::Tensor)
    }

    /// Concatenate attention heads.
    /// From [batch_size, seq_len, num_heads, head_dim] to [batch_size, seq_len, hidden_size]
    fn concatenate_heads(
        &self,
        tensor: &CpuTensor<T>,
        batch_size: usize,
        seq_len: usize,
        hidden_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let new_shape = Shape::new(vec![batch_size, seq_len, hidden_size]);
        TensorView::view_as(tensor, &new_shape).map_err(AttentionError::Tensor)
    }

    /// Reshape attention weights for output.
    /// From [batch_size * num_heads, seq_len_q, seq_len_k] to [batch_size, num_heads, seq_len_q, seq_len_k]
    fn reshape_attention_weights(
        &self,
        tensor: &CpuTensor<T>,
        batch_size: usize,
        num_heads: usize,
        seq_len_q: usize,
        seq_len_k: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let new_shape = Shape::new(vec![batch_size, num_heads, seq_len_q, seq_len_k]);
        TensorView::view_as(tensor, &new_shape).map_err(AttentionError::Tensor)
    }
}

impl<T: Numeric> Attention<T> for MultiHeadAttention<T> {
    type Tensor = CpuTensor<T>;
    type Error = AttentionError;

    fn compute_attention(
        &self,
        query: &Self::Tensor,
        key: &Self::Tensor,
        value: &Self::Tensor,
        mask: Option<&Self::Tensor>,
    ) -> Result<(Self::Tensor, Self::Tensor), Self::Error> {
        let (output, attention_weights) = self.forward(query, key, value, mask)?;
        Ok((output, attention_weights))
    }

    fn num_heads(&self) -> usize {
        self.config.num_heads
    }

    fn head_dim(&self) -> usize {
        self.config.head_dim()
    }

    fn scale_factor(&self) -> T {
        T::from_f32(self.config.effective_scale())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::Shape;
    use crate::layers::ParameterizedLayer;

    #[test]
    fn test_multi_head_attention_creation() {
        let config = AttentionConfig::new(512, 8);
        let mha = MultiHeadAttention::<f32>::new(config.clone()).unwrap();

        assert_eq!(mha.num_heads(), 8);
        assert_eq!(mha.head_dim(), 64);
        assert_eq!(mha.hidden_size(), 512);
        assert_eq!(mha.config(), &config);
    }

    #[test]
    fn test_invalid_config() {
        // Invalid: hidden_size not divisible by num_heads
        let config = AttentionConfig::new(513, 8);
        let result = MultiHeadAttention::<f32>::new(config);
        assert!(result.is_err());
    }

    #[test]
    fn test_multi_head_attention_forward() {
        let config = AttentionConfig::new(64, 4); // Small size for testing
        let mha = MultiHeadAttention::<f32>::new(config).unwrap();

        // Create test tensors: [batch_size=2, seq_len=3, hidden_size=64]
        let batch_size = 2;
        let seq_len = 3;
        let hidden_size = 64;

        let query_data = vec![1.0; batch_size * seq_len * hidden_size];
        let key_data = vec![0.5; batch_size * seq_len * hidden_size];
        let value_data = vec![2.0; batch_size * seq_len * hidden_size];

        let query = CpuTensor::from_data(
            query_data,
            Shape::new(vec![batch_size, seq_len, hidden_size])
        ).unwrap();
        let key = CpuTensor::from_data(
            key_data,
            Shape::new(vec![batch_size, seq_len, hidden_size])
        ).unwrap();
        let value = CpuTensor::from_data(
            value_data,
            Shape::new(vec![batch_size, seq_len, hidden_size])
        ).unwrap();

        let result = mha.forward(&query, &key, &value, None);
        assert!(result.is_ok());

        let (output, attention_weights) = result.unwrap();

        // Check output shape: [batch_size, seq_len, hidden_size]
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);

        // Check attention weights shape: [batch_size, num_heads, seq_len, seq_len]
        assert_eq!(attention_weights.shape().dims(), &[batch_size, 4, seq_len, seq_len]);
    }

    #[test]
    fn test_self_attention() {
        let config = AttentionConfig::new(32, 2); // Very small for testing
        let mha = MultiHeadAttention::<f32>::new(config).unwrap();

        // Create test tensor
        let input_data = vec![
            // Batch 0, sequence 0
            1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
            0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
            0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
            0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
            // Batch 0, sequence 1
            0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
            0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
            0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
            0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
        ];

        let input = CpuTensor::from_data(input_data, Shape::new(vec![1, 2, 32])).unwrap();

        // Self-attention: Q = K = V = input
        let result = mha.forward(&input, &input, &input, None);
        assert!(result.is_ok());

        let (output, _) = result.unwrap();
        assert_eq!(output.shape().dims(), &[1, 2, 32]);
    }

    #[test]
    fn test_input_validation() {
        let config = AttentionConfig::new(64, 4);
        let mha = MultiHeadAttention::<f32>::new(config).unwrap();

        // Test mismatched batch sizes
        let query = CpuTensor::from_data(vec![1.0; 64], Shape::new(vec![1, 1, 64])).unwrap();
        let key = CpuTensor::from_data(vec![1.0; 128], Shape::new(vec![2, 1, 64])).unwrap(); // Different batch
        let value = CpuTensor::from_data(vec![1.0; 64], Shape::new(vec![1, 1, 64])).unwrap();

        let result = mha.forward(&query, &key, &value, None);
        assert!(result.is_err());

        // Test mismatched hidden dimensions
        let query = CpuTensor::from_data(vec![1.0; 64], Shape::new(vec![1, 1, 64])).unwrap();
        let key = CpuTensor::from_data(vec![1.0; 32], Shape::new(vec![1, 1, 32])).unwrap(); // Different hidden size
        let value = CpuTensor::from_data(vec![1.0; 64], Shape::new(vec![1, 1, 64])).unwrap();

        let result = mha.forward(&query, &key, &value, None);
        assert!(result.is_err());

        // Test mismatched key-value sequence lengths
        let query = CpuTensor::from_data(vec![1.0; 64], Shape::new(vec![1, 1, 64])).unwrap();
        let key = CpuTensor::from_data(vec![1.0; 128], Shape::new(vec![1, 2, 64])).unwrap();
        let value = CpuTensor::from_data(vec![1.0; 192], Shape::new(vec![1, 3, 64])).unwrap(); // Different seq_len

        let result = mha.forward(&query, &key, &value, None);
        assert!(result.is_err());
    }

    #[test]
    fn test_causal_attention() {
        let config = AttentionConfig::causal(32, 2);
        let mha = MultiHeadAttention::<f32>::new(config).unwrap();

        let input_data = vec![1.0; 64]; // 1 * 2 * 32
        let input = CpuTensor::from_data(input_data, Shape::new(vec![1, 2, 32])).unwrap();

        let result = mha.forward(&input, &input, &input, None);
        assert!(result.is_ok());

        let (output, attention_weights) = result.unwrap();
        assert_eq!(output.shape().dims(), &[1, 2, 32]);
        assert_eq!(attention_weights.shape().dims(), &[1, 2, 2, 2]);
    }
}
