//! Attention mechanisms for transformer models.

pub mod advanced;
pub mod scaled_dot_product;
pub mod multi_head;
pub mod cache;
pub mod incremental;
pub mod variants;
pub mod positional;

// Re-exports
pub use advanced::{SparseAttention, SparseAttentionConfig, SparsePattern, SparseStats};
pub use scaled_dot_product::ScaledDotProductAttention;
pub use multi_head::MultiHeadAttention;
pub use cache::{KVCache, CacheConfig, CacheStats, EvictionPolicy};
pub use incremental::IncrementalAttention;
pub use variants::{SelfAttention, CrossAttention, CausalAttention, AttentionVariant};
pub use positional::{
    PositionalEncoding, PositionalConfig,
    SinusoidalPositionalEncoding, LearnedPositionalEncoding,
    RelativePositionalEncoding, RotaryPositionalEncoding
};

use crate::tensor::{Tensor, Numeric};

/// Base trait for attention mechanisms.
pub trait Attention<T: Numeric>: Send + Sync {
    /// Tensor type used by this attention mechanism.
    type Tensor: Tensor<T>;
    /// Error type for attention operations.
    type Error: std::error::Error + Send + Sync + 'static;
    
    /// Compute attention weights and output.
    fn compute_attention(
        &self,
        query: &Self::Tensor,
        key: &Self::Tensor,
        value: &Self::Tensor,
        mask: Option<&Self::Tensor>,
    ) -> Result<(Self::Tensor, Self::Tensor), Self::Error>; // (output, attention_weights)
    
    /// Get the number of attention heads.
    fn num_heads(&self) -> usize;
    
    /// Get the dimension of each attention head.
    fn head_dim(&self) -> usize;
    
    /// Get the scale factor used in attention computation.
    fn scale_factor(&self) -> T;
}

/// Trait for cached attention (used in autoregressive generation).
pub trait CachedAttention<T: Numeric>: Attention<T> {
    /// Cache type for storing key-value pairs.
    type Cache;
    
    /// Forward pass with key-value cache.
    fn forward_with_cache(
        &self,
        query: &Self::Tensor,
        key: &Self::Tensor,
        value: &Self::Tensor,
        cache: Option<&mut Self::Cache>,
        mask: Option<&Self::Tensor>,
    ) -> Result<(Self::Tensor, Self::Tensor), Self::Error>;
    
    /// Create a new cache for the given batch size and sequence length.
    fn create_cache(&self, batch_size: usize, max_seq_len: usize) -> Self::Cache;
    
    /// Clear the cache.
    fn clear_cache(&self, cache: &mut Self::Cache);
}

/// Configuration for attention mechanisms.
#[derive(Debug, Clone, PartialEq)]
#[cfg_attr(feature = "serde", derive(serde::Serialize, serde::Deserialize))]
pub struct AttentionConfig {
    /// Hidden dimension.
    pub hidden_size: usize,
    /// Number of attention heads.
    pub num_heads: usize,
    /// Dropout probability for attention weights.
    pub dropout: f32,
    /// Whether to use bias in linear projections.
    pub use_bias: bool,
    /// Scale factor (if None, uses 1/sqrt(head_dim)).
    pub scale: Option<f32>,
    /// Maximum sequence length for positional encoding.
    pub max_seq_len: Option<usize>,
    /// Whether to use causal masking.
    pub causal: bool,
    /// Attention implementation type.
    pub attention_type: AttentionType,
}

/// Types of attention implementations.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[cfg_attr(feature = "serde", derive(serde::Serialize, serde::Deserialize))]
pub enum AttentionType {
    /// Standard scaled dot-product attention.
    ScaledDotProduct,
    /// Flash attention (memory efficient).
    Flash,
    /// Sparse attention.
    Sparse,
}

impl AttentionConfig {
    /// Create a new attention configuration with default values.
    pub fn new(hidden_size: usize, num_heads: usize) -> Self {
        Self {
            hidden_size,
            num_heads,
            dropout: 0.0,
            use_bias: true,
            scale: None,
            max_seq_len: None,
            causal: false,
            attention_type: AttentionType::ScaledDotProduct,
        }
    }

    /// Create a configuration for causal (autoregressive) attention.
    pub fn causal(hidden_size: usize, num_heads: usize) -> Self {
        Self {
            hidden_size,
            num_heads,
            dropout: 0.0,
            use_bias: true,
            scale: None,
            max_seq_len: None,
            causal: true,
            attention_type: AttentionType::ScaledDotProduct,
        }
    }

    /// Set dropout probability.
    pub fn with_dropout(mut self, dropout: f32) -> Self {
        self.dropout = dropout;
        self
    }

    /// Set whether to use bias in projections.
    pub fn with_bias(mut self, use_bias: bool) -> Self {
        self.use_bias = use_bias;
        self
    }

    /// Set custom scale factor.
    pub fn with_scale(mut self, scale: f32) -> Self {
        self.scale = Some(scale);
        self
    }

    /// Set maximum sequence length.
    pub fn with_max_seq_len(mut self, max_seq_len: usize) -> Self {
        self.max_seq_len = Some(max_seq_len);
        self
    }

    /// Set attention type.
    pub fn with_attention_type(mut self, attention_type: AttentionType) -> Self {
        self.attention_type = attention_type;
        self
    }

    /// Get the dimension of each attention head.
    pub fn head_dim(&self) -> usize {
        self.hidden_size / self.num_heads
    }

    /// Get the effective scale factor.
    pub fn effective_scale(&self) -> f32 {
        self.scale.unwrap_or_else(|| 1.0 / (self.head_dim() as f32).sqrt())
    }

    /// Validate the configuration.
    pub fn validate(&self) -> Result<(), String> {
        // Check basic constraints
        if self.hidden_size == 0 {
            return Err("hidden_size must be greater than 0".to_string());
        }

        if self.num_heads == 0 {
            return Err("num_heads must be greater than 0".to_string());
        }

        if self.hidden_size % self.num_heads != 0 {
            return Err(format!(
                "hidden_size ({}) must be divisible by num_heads ({})",
                self.hidden_size, self.num_heads
            ));
        }

        if self.dropout < 0.0 || self.dropout > 1.0 {
            return Err(format!("dropout must be between 0.0 and 1.0, got {}", self.dropout));
        }

        if let Some(scale) = self.scale {
            if scale <= 0.0 {
                return Err(format!("scale must be positive, got {}", scale));
            }
        }

        if let Some(max_seq_len) = self.max_seq_len {
            if max_seq_len == 0 {
                return Err("max_seq_len must be greater than 0".to_string());
            }
        }

        // Check head dimension is reasonable
        let head_dim = self.head_dim();
        if head_dim < 8 {
            return Err(format!("head_dim ({}) is too small, should be at least 8", head_dim));
        }

        if head_dim > 512 {
            return Err(format!("head_dim ({}) is too large, should be at most 512", head_dim));
        }

        Ok(())
    }

    /// Get a summary of the configuration.
    pub fn summary(&self) -> String {
        format!(
            "AttentionConfig {{ hidden_size: {}, num_heads: {}, head_dim: {}, dropout: {:.3}, causal: {}, type: {:?} }}",
            self.hidden_size,
            self.num_heads,
            self.head_dim(),
            self.dropout,
            self.causal,
            self.attention_type
        )
    }
}

impl Default for AttentionConfig {
    fn default() -> Self {
        Self::new(512, 8) // Common default: 512 hidden size, 8 heads
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_attention_config_creation() {
        let config = AttentionConfig::new(512, 8);
        assert_eq!(config.hidden_size, 512);
        assert_eq!(config.num_heads, 8);
        assert_eq!(config.head_dim(), 64);
        assert_eq!(config.dropout, 0.0);
        assert!(config.use_bias);
        assert!(!config.causal);
        assert_eq!(config.attention_type, AttentionType::ScaledDotProduct);
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_causal_config() {
        let config = AttentionConfig::causal(768, 12);
        assert_eq!(config.hidden_size, 768);
        assert_eq!(config.num_heads, 12);
        assert_eq!(config.head_dim(), 64);
        assert!(config.causal);
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_config_builder_pattern() {
        let config = AttentionConfig::new(512, 8)
            .with_dropout(0.1)
            .with_bias(false)
            .with_scale(0.125)
            .with_max_seq_len(2048)
            .with_attention_type(AttentionType::Flash);

        assert_eq!(config.dropout, 0.1);
        assert!(!config.use_bias);
        assert_eq!(config.scale, Some(0.125));
        assert_eq!(config.max_seq_len, Some(2048));
        assert_eq!(config.attention_type, AttentionType::Flash);
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_effective_scale() {
        let config1 = AttentionConfig::new(512, 8);
        let expected_scale = 1.0 / (64.0_f32).sqrt(); // 1/sqrt(head_dim)
        assert!((config1.effective_scale() - expected_scale).abs() < 1e-6);

        let config2 = AttentionConfig::new(512, 8).with_scale(0.25);
        assert_eq!(config2.effective_scale(), 0.25);
    }

    #[test]
    fn test_config_validation() {
        // Valid config
        let config = AttentionConfig::new(512, 8);
        assert!(config.validate().is_ok());

        // Invalid: hidden_size not divisible by num_heads
        let config = AttentionConfig::new(513, 8);
        assert!(config.validate().is_err());

        // Invalid: dropout out of range
        let config = AttentionConfig::new(512, 8).with_dropout(1.5);
        assert!(config.validate().is_err());

        let config = AttentionConfig::new(512, 8).with_dropout(-0.1);
        assert!(config.validate().is_err());

        // Invalid: zero values
        let config = AttentionConfig::new(0, 8);
        assert!(config.validate().is_err());

        let config = AttentionConfig::new(512, 0);
        assert!(config.validate().is_err());

        // Invalid: negative scale
        let config = AttentionConfig::new(512, 8).with_scale(-0.1);
        assert!(config.validate().is_err());

        // Invalid: head dimension too small
        let config = AttentionConfig::new(32, 8); // head_dim = 4
        assert!(config.validate().is_err());

        // Invalid: head dimension too large
        let config = AttentionConfig::new(8192, 8); // head_dim = 1024
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_default_config() {
        let config = AttentionConfig::default();
        assert_eq!(config.hidden_size, 512);
        assert_eq!(config.num_heads, 8);
        assert_eq!(config.head_dim(), 64);
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_config_summary() {
        let config = AttentionConfig::new(768, 12).with_dropout(0.1);
        let summary = config.summary();
        assert!(summary.contains("hidden_size: 768"));
        assert!(summary.contains("num_heads: 12"));
        assert!(summary.contains("head_dim: 64"));
        assert!(summary.contains("dropout: 0.100"));
        assert!(summary.contains("causal: false"));
    }
}
