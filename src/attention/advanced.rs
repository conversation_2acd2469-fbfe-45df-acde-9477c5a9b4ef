//! Advanced attention mechanisms and optimizations.
//!
//! This module provides advanced attention patterns and optimizations including:
//! - Sparse attention patterns
//! - Memory-efficient attention computation
//! - Attention pattern analysis and visualization
//! - Custom attention kernels

use crate::attention::{Attention, AttentionConfig, ScaledDotProductAttention};
use crate::error::{AttentionError, ErrorContext};
use crate::tensor::{Tensor, Shape, Numeric};
use crate::tensor::cpu::CpuTensor;
use std::collections::HashMap;

/// Sparse attention pattern types
#[derive(Debug, Clone, PartialEq)]
pub enum SparsePattern {
    /// Local attention with fixed window size
    Local { window_size: usize },
    /// Strided attention with fixed stride
    Strided { stride: usize },
    /// Random sparse attention
    Random { sparsity: f32 },
    /// Block sparse attention
    Block { block_size: usize },
    /// Custom pattern defined by mask (0.0 = allow, -inf = mask)
    Custom { mask: Vec<f32>, seq_len: usize },
}

/// Configuration for sparse attention
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct SparseAttentionConfig {
    /// Base attention configuration
    pub base_config: AttentionConfig,
    /// Sparse pattern to use
    pub pattern: SparsePattern,
    /// Whether to use approximate computation
    pub approximate: bool,
    /// Memory optimization level (0-3)
    pub memory_level: u8,
}

impl SparseAttentionConfig {
    /// Create a new sparse attention configuration
    pub fn new(base_config: AttentionConfig, pattern: SparsePattern) -> Self {
        Self {
            base_config,
            pattern,
            approximate: false,
            memory_level: 1,
        }
    }

    /// Enable approximate computation for better performance
    pub fn with_approximation(mut self, approximate: bool) -> Self {
        self.approximate = approximate;
        self
    }

    /// Set memory optimization level
    pub fn with_memory_level(mut self, level: u8) -> Self {
        self.memory_level = level.min(3);
        self
    }
}

/// Sparse attention implementation
pub struct SparseAttention<T: Numeric> {
    config: SparseAttentionConfig,
    base_attention: ScaledDotProductAttention<T>,
    pattern_cache: HashMap<String, CpuTensor<T>>,
}

impl<T: Numeric> SparseAttention<T> {
    /// Create a new sparse attention layer
    pub fn new(config: SparseAttentionConfig) -> Result<Self, AttentionError> {
        let head_dim = config.base_config.hidden_size / config.base_config.num_heads;
        let base_attention = ScaledDotProductAttention::new(head_dim, 0.0, false)?;
        
        Ok(Self {
            config,
            base_attention,
            pattern_cache: HashMap::new(),
        })
    }

    /// Forward pass with sparse attention
    pub fn forward(
        &mut self,
        query: &CpuTensor<T>,
        key: &CpuTensor<T>,
        value: &CpuTensor<T>,
        mask: Option<&CpuTensor<T>>,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), AttentionError> {
        let q_shape = query.shape().dims();
        let batch_size = q_shape[0];
        let seq_len = q_shape[q_shape.len() - 2];

        // Generate or retrieve sparse mask
        let sparse_mask = self.get_sparse_mask(seq_len)?;

        // Expand sparse mask to include batch dimension
        let expanded_mask = self.expand_mask_for_batch(&sparse_mask, batch_size)?;

        // Combine with user-provided mask if any
        let combined_mask = match mask {
            Some(user_mask) => self.combine_masks(&expanded_mask, user_mask)?,
            None => expanded_mask,
        };

        // Use base attention with combined mask
        self.base_attention.compute_attention(query, key, value, Some(&combined_mask))
    }

    /// Get or generate sparse mask for given sequence length
    fn get_sparse_mask(&mut self, seq_len: usize) -> Result<CpuTensor<T>, AttentionError> {
        let cache_key = format!("{}_{}", self.pattern_key(), seq_len);

        if let Some(cached_mask) = self.pattern_cache.get(&cache_key) {
            return Ok(cached_mask.clone());
        }

        let mask = self.generate_sparse_mask(seq_len)?;
        self.pattern_cache.insert(cache_key, mask.clone());
        Ok(mask)
    }

    /// Generate sparse mask based on pattern
    fn generate_sparse_mask(&self, seq_len: usize) -> Result<CpuTensor<T>, AttentionError> {
        match &self.config.pattern {
            SparsePattern::Local { window_size } => {
                self.generate_local_mask(seq_len, *window_size)
            }
            SparsePattern::Strided { stride } => {
                self.generate_strided_mask(seq_len, *stride)
            }
            SparsePattern::Random { sparsity } => {
                self.generate_random_mask(seq_len, *sparsity)
            }
            SparsePattern::Block { block_size } => {
                self.generate_block_mask(seq_len, *block_size)
            }
            SparsePattern::Custom { mask, seq_len: pattern_seq_len } => {
                if *pattern_seq_len != seq_len {
                    return Err(AttentionError::InvalidMask {
                        reason: "custom mask sequence length mismatch".to_string(),
                        mask_shape: Some(vec![*pattern_seq_len, *pattern_seq_len]),
                        expected_shape: Some(vec![seq_len, seq_len]),
                        context: Some(ErrorContext::new("generate_sparse_mask", "attention::advanced")),
                    });
                }
                let float_data: Vec<T> = mask.iter().map(|&x| T::from_f32(x)).collect();
                CpuTensor::from_data(float_data, Shape::new(vec![seq_len, seq_len]))
                    .map_err(AttentionError::Tensor)
            }
        }
    }

    /// Generate local attention mask
    fn generate_local_mask(&self, seq_len: usize, window_size: usize) -> Result<CpuTensor<T>, AttentionError> {
        let mut data = vec![T::NEG_INFINITY; seq_len * seq_len];
        let half_window = window_size / 2;

        for i in 0..seq_len {
            let start = i.saturating_sub(half_window);
            let end = (i + half_window + 1).min(seq_len);

            for j in start..end {
                data[i * seq_len + j] = T::ZERO;
            }
        }

        CpuTensor::from_data(data, Shape::new(vec![seq_len, seq_len]))
            .map_err(AttentionError::Tensor)
    }

    /// Generate strided attention mask
    fn generate_strided_mask(&self, seq_len: usize, stride: usize) -> Result<CpuTensor<T>, AttentionError> {
        let mut data = vec![T::NEG_INFINITY; seq_len * seq_len];

        for i in 0..seq_len {
            // Allow attention to positions at regular intervals
            for j in (0..seq_len).step_by(stride) {
                if j <= i {  // Maintain causality if needed
                    data[i * seq_len + j] = T::ZERO;
                }
            }
            // Always allow attention to self
            data[i * seq_len + i] = T::ZERO;
        }

        CpuTensor::from_data(data, Shape::new(vec![seq_len, seq_len]))
            .map_err(AttentionError::Tensor)
    }

    /// Generate random sparse attention mask
    fn generate_random_mask(&self, seq_len: usize, sparsity: f32) -> Result<CpuTensor<T>, AttentionError> {
        let mut data = vec![T::NEG_INFINITY; seq_len * seq_len];
        let keep_prob = 1.0 - sparsity;

        // Use a simple PRNG for reproducibility
        let mut seed = 42u64;
        for i in 0..seq_len {
            for j in 0..=i {  // Maintain causality
                seed = seed.wrapping_mul(1103515245).wrapping_add(12345);
                let rand_val = (seed >> 16) as f32 / 65536.0;

                if rand_val < keep_prob || i == j {  // Always keep diagonal
                    data[i * seq_len + j] = T::ZERO;
                }
            }
        }

        CpuTensor::from_data(data, Shape::new(vec![seq_len, seq_len]))
            .map_err(AttentionError::Tensor)
    }

    /// Generate block sparse attention mask
    fn generate_block_mask(&self, seq_len: usize, block_size: usize) -> Result<CpuTensor<T>, AttentionError> {
        let mut data = vec![T::NEG_INFINITY; seq_len * seq_len];
        let num_blocks = (seq_len + block_size - 1) / block_size;

        for block_i in 0..num_blocks {
            for block_j in 0..=block_i {  // Lower triangular blocks for causality
                let start_i = block_i * block_size;
                let end_i = ((block_i + 1) * block_size).min(seq_len);
                let start_j = block_j * block_size;
                let end_j = ((block_j + 1) * block_size).min(seq_len);

                for i in start_i..end_i {
                    for j in start_j..end_j {
                        if j <= i {  // Maintain causality within blocks
                            data[i * seq_len + j] = T::ZERO;
                        }
                    }
                }
            }
        }

        CpuTensor::from_data(data, Shape::new(vec![seq_len, seq_len]))
            .map_err(AttentionError::Tensor)
    }

    /// Expand 2D mask to 3D by adding batch dimension
    fn expand_mask_for_batch(
        &self,
        mask: &CpuTensor<T>,
        batch_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let mask_shape = mask.shape().dims();
        let seq_len = mask_shape[0];

        // Create expanded mask with batch dimension
        let mut expanded_data = Vec::with_capacity(batch_size * seq_len * seq_len);
        for _ in 0..batch_size {
            expanded_data.extend_from_slice(mask.data());
        }

        CpuTensor::from_data(expanded_data, Shape::new(vec![batch_size, seq_len, seq_len]))
            .map_err(AttentionError::Tensor)
    }

    /// Combine sparse mask with user mask
    fn combine_masks(
        &self,
        sparse_mask: &CpuTensor<T>,
        user_mask: &CpuTensor<T>,
    ) -> Result<CpuTensor<T>, AttentionError> {
        // Element-wise minimum (most restrictive)
        let combined_data: Vec<T> = sparse_mask.data()
            .iter()
            .zip(user_mask.data().iter())
            .map(|(s, u)| if *s == T::NEG_INFINITY || *u == T::NEG_INFINITY {
                T::NEG_INFINITY
            } else {
                T::ZERO
            })
            .collect();

        CpuTensor::from_data(combined_data, user_mask.shape().clone())
            .map_err(AttentionError::Tensor)
    }

    /// Generate pattern key for caching
    fn pattern_key(&self) -> String {
        match &self.config.pattern {
            SparsePattern::Local { window_size } => format!("local_{}", window_size),
            SparsePattern::Strided { stride } => format!("strided_{}", stride),
            SparsePattern::Random { sparsity } => format!("random_{:.2}", sparsity),
            SparsePattern::Block { block_size } => format!("block_{}", block_size),
            SparsePattern::Custom { .. } => "custom".to_string(),
        }
    }

    /// Get sparsity statistics
    pub fn sparsity_stats(&mut self, seq_len: usize) -> Result<SparseStats, AttentionError> {
        let mask = self.get_sparse_mask(seq_len)?;
        let total_elements = seq_len * seq_len;
        let active_elements = mask.data().iter().filter(|&&x| x != T::NEG_INFINITY).count();
        let sparsity = 1.0 - (active_elements as f32 / total_elements as f32);

        Ok(SparseStats {
            total_elements,
            active_elements,
            sparsity,
            memory_reduction: sparsity,
            compute_reduction: sparsity,
        })
    }

    /// Clear pattern cache
    pub fn clear_cache(&mut self) {
        self.pattern_cache.clear();
    }
}

/// Statistics about sparse attention pattern
#[derive(Debug, Clone)]
pub struct SparseStats {
    pub total_elements: usize,
    pub active_elements: usize,
    pub sparsity: f32,
    pub memory_reduction: f32,
    pub compute_reduction: f32,
}

impl std::fmt::Display for SparseStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "SparseStats {{ total: {}, active: {}, sparsity: {:.2}%, memory_reduction: {:.2}%, compute_reduction: {:.2}% }}",
            self.total_elements,
            self.active_elements,
            self.sparsity * 100.0,
            self.memory_reduction * 100.0,
            self.compute_reduction * 100.0
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::cpu::CpuTensorFactory;
    use crate::tensor::{TensorFactory, Tensor};

    fn create_test_config() -> AttentionConfig {
        AttentionConfig::new(64, 4)
    }

    #[test]
    fn test_sparse_attention_config_creation() {
        let base_config = create_test_config();
        let sparse_config = SparseAttentionConfig::new(
            base_config.clone(),
            SparsePattern::Local { window_size: 16 }
        );

        assert_eq!(sparse_config.base_config.hidden_size, 64);
        assert_eq!(sparse_config.base_config.num_heads, 4);
        assert!(!sparse_config.approximate);
        assert_eq!(sparse_config.memory_level, 1);

        // Test builder pattern
        let sparse_config = sparse_config
            .with_approximation(true)
            .with_memory_level(3);

        assert!(sparse_config.approximate);
        assert_eq!(sparse_config.memory_level, 3);
    }

    #[test]
    fn test_local_sparse_attention() -> Result<(), AttentionError> {
        let base_config = create_test_config();
        let sparse_config = SparseAttentionConfig::new(
            base_config,
            SparsePattern::Local { window_size: 8 }
        );

        let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;

        let batch_size = 2;
        let seq_len = 16;
        let hidden_size = 64;

        let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;

        let (output, attention_weights) = sparse_attention.forward(&query, &key, &value, None)?;

        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);
        assert_eq!(attention_weights.shape().dims(), &[batch_size, seq_len, seq_len]);

        // Test sparsity statistics
        let stats = sparse_attention.sparsity_stats(seq_len)?;
        assert!(stats.sparsity > 0.0);
        assert!(stats.sparsity < 1.0);
        assert_eq!(stats.memory_reduction, stats.sparsity);
        assert_eq!(stats.compute_reduction, stats.sparsity);

        Ok(())
    }

    #[test]
    fn test_strided_sparse_attention() -> Result<(), AttentionError> {
        let base_config = create_test_config();
        let sparse_config = SparseAttentionConfig::new(
            base_config,
            SparsePattern::Strided { stride: 2 }
        );

        let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;

        let batch_size = 1;
        let seq_len = 12;
        let hidden_size = 64;

        let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;

        let (output, _) = sparse_attention.forward(&query, &key, &value, None)?;
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);

        let stats = sparse_attention.sparsity_stats(seq_len)?;
        assert!(stats.sparsity > 0.0);

        Ok(())
    }

    #[test]
    fn test_random_sparse_attention() -> Result<(), AttentionError> {
        let base_config = create_test_config();
        let sparse_config = SparseAttentionConfig::new(
            base_config,
            SparsePattern::Random { sparsity: 0.7 }
        );

        let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;

        let batch_size = 1;
        let seq_len = 10;
        let hidden_size = 64;

        let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;

        let (output, _) = sparse_attention.forward(&query, &key, &value, None)?;
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);

        let stats = sparse_attention.sparsity_stats(seq_len)?;
        // Random sparsity should be reasonably high (but may vary due to randomness)
        assert!(stats.sparsity >= 0.5);
        assert!(stats.sparsity <= 1.0);

        Ok(())
    }

    #[test]
    fn test_block_sparse_attention() -> Result<(), AttentionError> {
        let base_config = create_test_config();
        let sparse_config = SparseAttentionConfig::new(
            base_config,
            SparsePattern::Block { block_size: 4 }
        );

        let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;

        let batch_size = 1;
        let seq_len = 16;
        let hidden_size = 64;

        let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;

        let (output, _) = sparse_attention.forward(&query, &key, &value, None)?;
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);

        let stats = sparse_attention.sparsity_stats(seq_len)?;
        assert!(stats.sparsity > 0.0);

        Ok(())
    }

    #[test]
    fn test_custom_sparse_attention() -> Result<(), AttentionError> {
        let base_config = create_test_config();
        let seq_len = 8;

        // Create a custom mask (diagonal pattern - allow only self-attention)
        let mut custom_mask = vec![f32::NEG_INFINITY; seq_len * seq_len];
        for i in 0..seq_len {
            custom_mask[i * seq_len + i] = 0.0; // Allow self-attention
        }

        let sparse_config = SparseAttentionConfig::new(
            base_config,
            SparsePattern::Custom { mask: custom_mask, seq_len }
        );

        let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;

        let batch_size = 1;
        let hidden_size = 64;

        let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;

        let (output, _) = sparse_attention.forward(&query, &key, &value, None)?;
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);

        let stats = sparse_attention.sparsity_stats(seq_len)?;
        // Diagonal pattern should have high sparsity (only 8 out of 64 elements are active)
        assert!(stats.sparsity > 0.8);

        Ok(())
    }

    #[test]
    fn test_sparse_attention_caching() -> Result<(), AttentionError> {
        let base_config = create_test_config();
        let sparse_config = SparseAttentionConfig::new(
            base_config,
            SparsePattern::Local { window_size: 4 }
        );

        let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;

        let seq_len = 8;

        // First call should generate and cache the mask
        let stats1 = sparse_attention.sparsity_stats(seq_len)?;

        // Second call should use cached mask
        let stats2 = sparse_attention.sparsity_stats(seq_len)?;

        // Results should be identical
        assert_eq!(stats1.total_elements, stats2.total_elements);
        assert_eq!(stats1.active_elements, stats2.active_elements);
        assert!((stats1.sparsity - stats2.sparsity).abs() < 1e-6);

        // Clear cache
        sparse_attention.clear_cache();

        // After clearing, should still work
        let stats3 = sparse_attention.sparsity_stats(seq_len)?;
        assert_eq!(stats1.total_elements, stats3.total_elements);

        Ok(())
    }

    #[test]
    fn test_sparse_attention_with_user_mask() -> Result<(), AttentionError> {
        let base_config = create_test_config();
        let sparse_config = SparseAttentionConfig::new(
            base_config,
            SparsePattern::Local { window_size: 8 }
        );

        let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;

        let batch_size = 1;
        let seq_len = 8;
        let hidden_size = 64;

        let query = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let key = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;
        let value = CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 1.0)?;

        // Create a user mask (causal mask)
        let mut user_mask_data = vec![0.0f32; batch_size * seq_len * seq_len];
        for b in 0..batch_size {
            for i in 0..seq_len {
                for j in 0..seq_len {
                    let idx = b * seq_len * seq_len + i * seq_len + j;
                    if j > i {
                        user_mask_data[idx] = f32::NEG_INFINITY;
                    }
                }
            }
        }
        let user_mask = CpuTensor::from_data(user_mask_data, Shape::new(vec![batch_size, seq_len, seq_len]))?;

        let (output, _) = sparse_attention.forward(&query, &key, &value, Some(&user_mask))?;
        assert_eq!(output.shape().dims(), &[batch_size, seq_len, hidden_size]);

        Ok(())
    }

    #[test]
    fn test_sparse_stats_display() -> Result<(), AttentionError> {
        let base_config = create_test_config();
        let sparse_config = SparseAttentionConfig::new(
            base_config,
            SparsePattern::Local { window_size: 4 }
        );

        let mut sparse_attention: SparseAttention<f32> = SparseAttention::new(sparse_config)?;
        let stats = sparse_attention.sparsity_stats(8)?;

        let display_str = format!("{}", stats);
        assert!(display_str.contains("total:"));
        assert!(display_str.contains("active:"));
        assert!(display_str.contains("sparsity:"));
        assert!(display_str.contains("memory_reduction:"));
        assert!(display_str.contains("compute_reduction:"));

        Ok(())
    }
}
