//! Attention mechanism variants.
//!
//! This module provides various specialized attention mechanisms:
//! - Self-attention for transformer blocks
//! - Cross-attention for encoder-decoder architectures
//! - Causal attention for autoregressive models
//! - Sparse attention for long sequences

pub mod self_attention;
pub mod cross_attention;
pub mod causal_attention;

// Re-exports
pub use self_attention::SelfAttention;
pub use cross_attention::CrossAttention;
pub use causal_attention::CausalAttention;

use crate::tensor::Numeric;
use crate::tensor::cpu::CpuTensor;
use crate::error::AttentionError;

/// Common interface for attention variants.
pub trait AttentionVariant<T: Numeric> {
    /// Forward pass through the attention mechanism.
    fn forward(
        &self,
        input: &CpuTensor<T>,
        context: Option<&CpuTensor<T>>,
        mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, AttentionError>;
    
    /// Get the attention type name.
    fn attention_type(&self) -> &'static str;
    
    /// Check if this attention variant supports causal masking.
    fn supports_causal(&self) -> bool;
    
    /// Check if this attention variant supports cross-attention.
    fn supports_cross_attention(&self) -> bool;
}
