//! Cross-attention implementation.
//!
//! Cross-attention is used in encoder-decoder architectures where the decoder
//! attends to the encoder's output. The queries come from the decoder, while
//! the keys and values come from the encoder.

use std::marker::PhantomData;
use crate::tensor::{Tensor, TensorOps, Shape, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::layers::{Layer, linear::{Linear, LinearConfig}};
use crate::error::{AttentionError, ErrorContext};
use super::AttentionVariant;
use crate::attention::{Attention, AttentionConfig, MultiHeadAttention};

/// Cross-attention layer for encoder-decoder architectures.
///
/// Cross-attention allows the decoder to attend to the encoder's output.
/// The queries come from the decoder sequence, while keys and values
/// come from the encoder sequence.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::attention::variants::CrossAttention;
/// use qilin_inference::attention::AttentionConfig;
/// use qilin_inference::tensor::{CpuTensor, <PERSON>hape};
///
/// // Create cross-attention layer
/// let config = AttentionConfig::new(512, 8);
/// let cross_attn = CrossAttention::new(config).unwrap();
///
/// // Forward pass
/// let decoder_input = CpuTensor::randn(&Shape::new(vec![2, 5, 512]), 0.0, 1.0).unwrap();
/// let encoder_output = CpuTensor::randn(&Shape::new(vec![2, 10, 512]), 0.0, 1.0).unwrap();
/// let output = cross_attn.forward(&decoder_input, Some(&encoder_output), None).unwrap();
/// assert_eq!(output.shape().dims(), &[2, 5, 512]);
/// ```
#[derive(Debug)]
pub struct CrossAttention<T: Numeric> {
    /// Multi-head attention implementation.
    mha: MultiHeadAttention<T>,
    /// Configuration.
    config: AttentionConfig,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> CrossAttention<T> {
    /// Create a new cross-attention layer.
    pub fn new(config: AttentionConfig) -> Result<Self, AttentionError> {
        // Cross-attention typically doesn't use causal masking
        let mut cross_config = config.clone();
        cross_config.causal = false;
        
        let mha = MultiHeadAttention::new(cross_config)?;
        
        Ok(Self {
            mha,
            config,
            _phantom: PhantomData,
        })
    }
    
    /// Get the configuration.
    pub fn config(&self) -> &AttentionConfig {
        &self.config
    }
    
    /// Get the number of attention heads.
    pub fn num_heads(&self) -> usize {
        self.config.num_heads
    }
    
    /// Get the dimension of each head.
    pub fn head_dim(&self) -> usize {
        self.config.head_dim()
    }
    
    /// Forward pass with detailed output.
    ///
    /// Returns both the output tensor and attention weights for analysis.
    pub fn forward_with_weights(
        &self,
        decoder_input: &CpuTensor<T>,
        encoder_output: &CpuTensor<T>,
        mask: Option<&CpuTensor<T>>,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), AttentionError> {
        // In cross-attention: Q = decoder_input, K = V = encoder_output
        self.mha.compute_attention(decoder_input, encoder_output, encoder_output, mask)
    }
    
    /// Forward pass with encoder-decoder attention.
    ///
    /// This is the standard cross-attention computation where:
    /// - Queries come from the decoder
    /// - Keys and values come from the encoder
    pub fn encoder_decoder_attention(
        &self,
        decoder_queries: &CpuTensor<T>,
        encoder_keys_values: &CpuTensor<T>,
        mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let (output, _) = self.forward_with_weights(decoder_queries, encoder_keys_values, mask)?;
        Ok(output)
    }
    
    /// Validate that decoder and encoder inputs are compatible.
    fn validate_cross_attention_inputs(
        &self,
        decoder_input: &CpuTensor<T>,
        encoder_output: &CpuTensor<T>,
    ) -> Result<(), AttentionError> {
        let decoder_shape = decoder_input.shape().dims();
        let encoder_shape = encoder_output.shape().dims();
        
        // Both should be 3D tensors
        if decoder_shape.len() != 3 || encoder_shape.len() != 3 {
            return Err(AttentionError::DimensionMismatch {
                expected: "3D tensors [batch_size, seq_len, hidden_size]".to_string(),
                actual: format!("decoder: {:?}, encoder: {:?}", decoder_shape, encoder_shape),
                operation: "cross_attention_input_validation".to_string(),
                context: Some(ErrorContext::new("validate_cross_attention_inputs", "attention::variants::cross_attention")),
            });
        }
        
        // Batch sizes must match
        if decoder_shape[0] != encoder_shape[0] {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("batch_size: {}", decoder_shape[0]),
                actual: format!("decoder: {}, encoder: {}", decoder_shape[0], encoder_shape[0]),
                operation: "batch_size_validation".to_string(),
                context: Some(ErrorContext::new("validate_cross_attention_inputs", "attention::variants::cross_attention")),
            });
        }
        
        // Hidden dimensions must match
        if decoder_shape[2] != encoder_shape[2] {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", decoder_shape[2]),
                actual: format!("decoder: {}, encoder: {}", decoder_shape[2], encoder_shape[2]),
                operation: "hidden_size_validation".to_string(),
                context: Some(ErrorContext::new("validate_cross_attention_inputs", "attention::variants::cross_attention")),
            });
        }
        
        // Hidden size must match configuration
        if decoder_shape[2] != self.config.hidden_size {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", self.config.hidden_size),
                actual: format!("input hidden_size: {}", decoder_shape[2]),
                operation: "config_validation".to_string(),
                context: Some(ErrorContext::new("validate_cross_attention_inputs", "attention::variants::cross_attention")),
            });
        }
        
        Ok(())
    }
}

impl<T: Numeric> AttentionVariant<T> for CrossAttention<T> {
    fn forward(
        &self,
        input: &CpuTensor<T>,
        context: Option<&CpuTensor<T>>,
        mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let encoder_output = context.ok_or_else(|| AttentionError::InvalidConfiguration {
            message: "Cross-attention requires encoder context".to_string(),
            config_field: Some("context".to_string()),
            context: Some(ErrorContext::new("forward", "attention::variants::cross_attention")),
        })?;
        
        // Validate inputs
        self.validate_cross_attention_inputs(input, encoder_output)?;
        
        // In cross-attention: Q = decoder_input, K = V = encoder_output
        let (output, _) = self.mha.compute_attention(input, encoder_output, encoder_output, mask)?;
        Ok(output)
    }
    
    fn attention_type(&self) -> &'static str {
        "cross_attention"
    }
    
    fn supports_causal(&self) -> bool {
        false // Cross-attention typically doesn't use causal masking
    }
    
    fn supports_cross_attention(&self) -> bool {
        true
    }
}

impl<T: Numeric> Attention<T> for CrossAttention<T> {
    type Tensor = CpuTensor<T>;
    type Error = AttentionError;
    
    fn compute_attention(
        &self,
        query: &Self::Tensor,
        key: &Self::Tensor,
        value: &Self::Tensor,
        mask: Option<&Self::Tensor>,
    ) -> Result<(Self::Tensor, Self::Tensor), Self::Error> {
        // Validate that key and value have the same shape (encoder constraint)
        if key.shape().dims() != value.shape().dims() {
            return Err(AttentionError::DimensionMismatch {
                expected: "K and V must have same shape in cross-attention".to_string(),
                actual: format!("K: {:?}, V: {:?}", key.shape().dims(), value.shape().dims()),
                operation: "cross_attention_validation".to_string(),
                context: Some(ErrorContext::new("compute_attention", "attention::variants::cross_attention")),
            });
        }
        
        self.mha.compute_attention(query, key, value, mask)
    }
    
    fn num_heads(&self) -> usize {
        self.config.num_heads
    }
    
    fn head_dim(&self) -> usize {
        self.config.head_dim()
    }
    
    fn scale_factor(&self) -> T {
        T::from_f32(self.config.effective_scale())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::cpu::CpuTensorFactory;
    use crate::tensor::TensorFactory;

    fn create_test_input(batch_size: usize, seq_len: usize, hidden_size: usize) -> CpuTensor<f32> {
        let shape = Shape::new(vec![batch_size, seq_len, hidden_size]);
        CpuTensorFactory::ones(&shape).unwrap()
    }

    #[test]
    fn test_cross_attention_creation() {
        let config = AttentionConfig::new(64, 4);
        let cross_attn = CrossAttention::<f32>::new(config.clone()).unwrap();
        
        assert_eq!(cross_attn.config(), &config);
        assert_eq!(cross_attn.num_heads(), 4);
        assert_eq!(cross_attn.head_dim(), 16);
        assert_eq!(cross_attn.attention_type(), "cross_attention");
        assert!(cross_attn.supports_cross_attention());
        assert!(!cross_attn.supports_causal());
    }

    #[test]
    fn test_cross_attention_forward() {
        let config = AttentionConfig::new(64, 4);
        let cross_attn = CrossAttention::<f32>::new(config).unwrap();
        
        let decoder_input = create_test_input(2, 3, 64);
        let encoder_output = create_test_input(2, 5, 64);
        
        let result = cross_attn.forward(&decoder_input, Some(&encoder_output), None);
        
        assert!(result.is_ok());
        let output = result.unwrap();
        assert_eq!(output.shape().dims(), &[2, 3, 64]); // Same as decoder input
    }

    #[test]
    fn test_cross_attention_with_weights() {
        let config = AttentionConfig::new(64, 4);
        let cross_attn = CrossAttention::<f32>::new(config).unwrap();
        
        let decoder_input = create_test_input(1, 3, 64);
        let encoder_output = create_test_input(1, 5, 64);
        
        let result = cross_attn.forward_with_weights(&decoder_input, &encoder_output, None);
        
        assert!(result.is_ok());
        let (output, attention_weights) = result.unwrap();
        
        assert_eq!(output.shape().dims(), &[1, 3, 64]);
        assert_eq!(attention_weights.shape().dims(), &[1, 4, 3, 5]); // [batch, heads, decoder_seq, encoder_seq]
    }

    #[test]
    fn test_cross_attention_without_context() {
        let config = AttentionConfig::new(64, 4);
        let cross_attn = CrossAttention::<f32>::new(config).unwrap();
        
        let decoder_input = create_test_input(1, 3, 64);
        
        // Should fail without encoder context
        let result = cross_attn.forward(&decoder_input, None, None);
        assert!(result.is_err());
    }

    #[test]
    fn test_cross_attention_validation() {
        let config = AttentionConfig::new(64, 4);
        let cross_attn = CrossAttention::<f32>::new(config).unwrap();
        
        let decoder_input = create_test_input(1, 3, 64);
        let encoder_output = create_test_input(2, 5, 64); // Different batch size
        
        let result = cross_attn.forward(&decoder_input, Some(&encoder_output), None);
        assert!(result.is_err());
        
        // Test different hidden sizes
        let encoder_wrong_hidden = create_test_input(1, 5, 32); // Wrong hidden size
        let result = cross_attn.forward(&decoder_input, Some(&encoder_wrong_hidden), None);
        assert!(result.is_err());
    }

    #[test]
    fn test_encoder_decoder_attention() {
        let config = AttentionConfig::new(64, 4);
        let cross_attn = CrossAttention::<f32>::new(config).unwrap();
        
        let decoder_queries = create_test_input(1, 3, 64);
        let encoder_keys_values = create_test_input(1, 7, 64);
        
        let result = cross_attn.encoder_decoder_attention(&decoder_queries, &encoder_keys_values, None);
        
        assert!(result.is_ok());
        let output = result.unwrap();
        assert_eq!(output.shape().dims(), &[1, 3, 64]);
    }

    #[test]
    fn test_attention_trait_implementation() {
        let config = AttentionConfig::new(64, 4);
        let cross_attn = CrossAttention::<f32>::new(config).unwrap();
        
        let decoder_input = create_test_input(1, 3, 64);
        let encoder_output = create_test_input(1, 5, 64);
        
        // Test Attention trait
        let result = cross_attn.compute_attention(&decoder_input, &encoder_output, &encoder_output, None);
        assert!(result.is_ok());
        
        let (output, attention_weights) = result.unwrap();
        assert_eq!(output.shape().dims(), &[1, 3, 64]);
        assert_eq!(attention_weights.shape().dims(), &[1, 4, 3, 5]);
    }

    #[test]
    fn test_cross_attention_key_value_validation() {
        let config = AttentionConfig::new(64, 4);
        let cross_attn = CrossAttention::<f32>::new(config).unwrap();
        
        let query = create_test_input(1, 3, 64);
        let key = create_test_input(1, 5, 64);
        let value = create_test_input(1, 4, 64); // Different sequence length from key
        
        // Should fail because K and V have different shapes
        let result = cross_attn.compute_attention(&query, &key, &value, None);
        assert!(result.is_err());
    }
}
