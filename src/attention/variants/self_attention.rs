//! Self-attention implementation.
//!
//! Self-attention is the core mechanism in transformer architectures where
//! each position in a sequence attends to all positions in the same sequence.
//! This is used in both encoder and decoder blocks.

use std::marker::PhantomData;
use crate::tensor::{Tensor, TensorOps, Shape, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::layers::{Layer, linear::{Linear, LinearConfig}};
use crate::error::{AttentionError, ErrorContext};
use super::AttentionVariant;
use crate::attention::{Attention, AttentionConfig, MultiHeadAttention};

/// Self-attention layer for transformer architectures.
///
/// Self-attention allows each position in a sequence to attend to all positions
/// in the same sequence. This is the fundamental building block of transformer
/// models.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::attention::variants::SelfAttention;
/// use qilin_inference::attention::AttentionConfig;
/// use qilin_inference::tensor::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};
///
/// // Create self-attention layer
/// let config = AttentionConfig::new(512, 8);
/// let self_attn = SelfAttention::new(config).unwrap();
///
/// // Forward pass
/// let input = CpuTensor::randn(&Shape::new(vec![2, 10, 512]), 0.0, 1.0).unwrap();
/// let output = self_attn.forward(&input, None, None).unwrap();
/// assert_eq!(output.shape().dims(), &[2, 10, 512]);
/// ```
#[derive(Debug)]
pub struct SelfAttention<T: Numeric> {
    /// Multi-head attention implementation.
    mha: MultiHeadAttention<T>,
    /// Configuration.
    config: AttentionConfig,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> SelfAttention<T> {
    /// Create a new self-attention layer.
    pub fn new(config: AttentionConfig) -> Result<Self, AttentionError> {
        let mha = MultiHeadAttention::new(config.clone())?;
        
        Ok(Self {
            mha,
            config,
            _phantom: PhantomData,
        })
    }
    
    /// Get the configuration.
    pub fn config(&self) -> &AttentionConfig {
        &self.config
    }
    
    /// Get the number of attention heads.
    pub fn num_heads(&self) -> usize {
        self.config.num_heads
    }
    
    /// Get the dimension of each head.
    pub fn head_dim(&self) -> usize {
        self.config.head_dim()
    }
    
    /// Forward pass with detailed output.
    ///
    /// Returns both the output tensor and attention weights for analysis.
    pub fn forward_with_weights(
        &self,
        input: &CpuTensor<T>,
        mask: Option<&CpuTensor<T>>,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), AttentionError> {
        // In self-attention: Q = K = V = input
        self.mha.compute_attention(input, input, input, mask)
    }
    
    /// Apply layer normalization before attention (Pre-LN).
    pub fn forward_pre_norm(
        &self,
        input: &CpuTensor<T>,
        mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, AttentionError> {
        // TODO: Implement layer normalization when available
        // For now, just apply attention
        self.forward(input, None, mask)
    }
    
    /// Apply layer normalization after attention (Post-LN).
    pub fn forward_post_norm(
        &self,
        input: &CpuTensor<T>,
        mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let output = self.forward(input, None, mask)?;
        // TODO: Implement layer normalization when available
        // For now, just return the output
        Ok(output)
    }
    
    /// Apply residual connection.
    pub fn forward_with_residual(
        &self,
        input: &CpuTensor<T>,
        mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let output = self.forward(input, None, mask)?;
        
        // Add residual connection: output = input + attention_output
        input.add(&output).map_err(AttentionError::Tensor)
    }
}

impl<T: Numeric> AttentionVariant<T> for SelfAttention<T> {
    fn forward(
        &self,
        input: &CpuTensor<T>,
        _context: Option<&CpuTensor<T>>, // Ignored in self-attention
        mask: Option<&CpuTensor<T>>,
    ) -> Result<CpuTensor<T>, AttentionError> {
        // In self-attention: Q = K = V = input
        let (output, _) = self.mha.compute_attention(input, input, input, mask)?;
        Ok(output)
    }
    
    fn attention_type(&self) -> &'static str {
        "self_attention"
    }
    
    fn supports_causal(&self) -> bool {
        self.config.causal
    }
    
    fn supports_cross_attention(&self) -> bool {
        false // Self-attention doesn't use external context
    }
}

impl<T: Numeric> Attention<T> for SelfAttention<T> {
    type Tensor = CpuTensor<T>;
    type Error = AttentionError;
    
    fn compute_attention(
        &self,
        query: &Self::Tensor,
        key: &Self::Tensor,
        value: &Self::Tensor,
        mask: Option<&Self::Tensor>,
    ) -> Result<(Self::Tensor, Self::Tensor), Self::Error> {
        // Validate that Q, K, V are the same (self-attention constraint)
        if query.shape().dims() != key.shape().dims() || 
           query.shape().dims() != value.shape().dims() {
            return Err(AttentionError::DimensionMismatch {
                expected: "Q, K, V must have same shape in self-attention".to_string(),
                actual: format!("Q: {:?}, K: {:?}, V: {:?}", 
                    query.shape().dims(), key.shape().dims(), value.shape().dims()),
                operation: "self_attention_validation".to_string(),
                context: Some(ErrorContext::new("compute_attention", "attention::variants::self_attention")),
            });
        }
        
        self.mha.compute_attention(query, key, value, mask)
    }
    
    fn num_heads(&self) -> usize {
        self.config.num_heads
    }
    
    fn head_dim(&self) -> usize {
        self.config.head_dim()
    }
    
    fn scale_factor(&self) -> T {
        T::from_f32(self.config.effective_scale())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::cpu::CpuTensorFactory;
    use crate::tensor::TensorFactory;

    fn create_test_input(batch_size: usize, seq_len: usize, hidden_size: usize) -> CpuTensor<f32> {
        let shape = Shape::new(vec![batch_size, seq_len, hidden_size]);
        CpuTensorFactory::ones(&shape).unwrap()
    }

    #[test]
    fn test_self_attention_creation() {
        let config = AttentionConfig::new(64, 4);
        let self_attn = SelfAttention::<f32>::new(config.clone()).unwrap();
        
        assert_eq!(self_attn.config(), &config);
        assert_eq!(self_attn.num_heads(), 4);
        assert_eq!(self_attn.head_dim(), 16);
        assert_eq!(self_attn.attention_type(), "self_attention");
        assert!(!self_attn.supports_cross_attention());
    }

    #[test]
    fn test_self_attention_forward() {
        let config = AttentionConfig::new(64, 4);
        let self_attn = SelfAttention::<f32>::new(config).unwrap();
        
        let input = create_test_input(2, 5, 64);
        let result = self_attn.forward(&input, None, None);
        
        assert!(result.is_ok());
        let output = result.unwrap();
        assert_eq!(output.shape().dims(), &[2, 5, 64]);
    }

    #[test]
    fn test_self_attention_with_weights() {
        let config = AttentionConfig::new(64, 4);
        let self_attn = SelfAttention::<f32>::new(config).unwrap();
        
        let input = create_test_input(1, 3, 64);
        let result = self_attn.forward_with_weights(&input, None);
        
        assert!(result.is_ok());
        let (output, attention_weights) = result.unwrap();
        
        assert_eq!(output.shape().dims(), &[1, 3, 64]);
        assert_eq!(attention_weights.shape().dims(), &[1, 4, 3, 3]); // [batch, heads, seq, seq]
    }

    #[test]
    fn test_self_attention_with_residual() {
        let config = AttentionConfig::new(64, 4);
        let self_attn = SelfAttention::<f32>::new(config).unwrap();
        
        let input = create_test_input(1, 3, 64);
        let result = self_attn.forward_with_residual(&input, None);
        
        assert!(result.is_ok());
        let output = result.unwrap();
        assert_eq!(output.shape().dims(), &[1, 3, 64]);
    }

    #[test]
    fn test_causal_self_attention() {
        let config = AttentionConfig::causal(64, 4);
        let self_attn = SelfAttention::<f32>::new(config).unwrap();
        
        assert!(self_attn.supports_causal());
        
        let input = create_test_input(1, 4, 64);
        let result = self_attn.forward(&input, None, None);
        
        assert!(result.is_ok());
        let output = result.unwrap();
        assert_eq!(output.shape().dims(), &[1, 4, 64]);
    }

    #[test]
    fn test_attention_trait_implementation() {
        let config = AttentionConfig::new(64, 4);
        let self_attn = SelfAttention::<f32>::new(config).unwrap();
        
        let input = create_test_input(1, 3, 64);
        
        // Test Attention trait
        let result = self_attn.compute_attention(&input, &input, &input, None);
        assert!(result.is_ok());
        
        let (output, attention_weights) = result.unwrap();
        assert_eq!(output.shape().dims(), &[1, 3, 64]);
        assert_eq!(attention_weights.shape().dims(), &[1, 4, 3, 3]);
    }

    #[test]
    fn test_self_attention_validation() {
        let config = AttentionConfig::new(64, 4);
        let self_attn = SelfAttention::<f32>::new(config).unwrap();
        
        let input1 = create_test_input(1, 3, 64);
        let input2 = create_test_input(1, 4, 64); // Different sequence length
        
        // Should fail because Q, K, V have different shapes
        let result = self_attn.compute_attention(&input1, &input2, &input1, None);
        assert!(result.is_err());
    }
}
