//! Positional encoding implementations.
//!
//! This module provides various positional encoding schemes used in transformer
//! architectures to inject position information into the model:
//!
//! - Sinusoidal positional encoding (original Transformer)
//! - Learned positional encoding (BERT-style)
//! - Relative positional encoding (Transformer-XL, T5)
//! - Rotary positional encoding (RoPE)

pub mod sinusoidal;
pub mod learned;
pub mod relative;
pub mod rotary;

// Re-exports
pub use sinusoidal::SinusoidalPositionalEncoding;
pub use learned::LearnedPositionalEncoding;
pub use relative::RelativePositionalEncoding;
pub use rotary::RotaryPositionalEncoding;

use crate::tensor::Numeric;
use crate::tensor::cpu::CpuTensor;
use crate::error::AttentionError;

/// Common interface for positional encoding schemes.
pub trait PositionalEncoding<T: Numeric> {
    /// Apply positional encoding to input embeddings.
    ///
    /// # Arguments
    /// * `embeddings` - Input embeddings with shape [batch_size, seq_len, hidden_size]
    /// * `position_offset` - Optional offset for position indices (for incremental generation)
    ///
    /// # Returns
    /// * Embeddings with positional information added
    fn encode(
        &self,
        embeddings: &CpuTensor<T>,
        position_offset: Option<usize>,
    ) -> Result<CpuTensor<T>, AttentionError>;
    
    /// Get the maximum sequence length supported by this encoding.
    fn max_seq_len(&self) -> usize;
    
    /// Get the hidden dimension size.
    fn hidden_size(&self) -> usize;
    
    /// Get the encoding type name.
    fn encoding_type(&self) -> &'static str;
    
    /// Check if this encoding supports incremental generation.
    fn supports_incremental(&self) -> bool {
        true
    }
    
    /// Get positional encodings for specific positions.
    ///
    /// This is useful for caching or precomputing encodings.
    fn get_encodings(
        &self,
        positions: &[usize],
        hidden_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError>;
}

/// Configuration for positional encoding.
#[derive(Debug, Clone, PartialEq)]
pub struct PositionalConfig {
    /// Maximum sequence length.
    pub max_seq_len: usize,
    /// Hidden dimension size.
    pub hidden_size: usize,
    /// Dropout probability for positional encodings.
    pub dropout: f32,
    /// Whether to scale embeddings before adding positional encoding.
    pub scale_embeddings: bool,
    /// Scaling factor for embeddings (typically sqrt(hidden_size)).
    pub embedding_scale: Option<f32>,
}

impl PositionalConfig {
    /// Create a new positional encoding configuration.
    pub fn new(max_seq_len: usize, hidden_size: usize) -> Self {
        Self {
            max_seq_len,
            hidden_size,
            dropout: 0.0,
            scale_embeddings: false,
            embedding_scale: None,
        }
    }
    
    /// Set dropout probability.
    pub fn with_dropout(mut self, dropout: f32) -> Self {
        self.dropout = dropout;
        self
    }
    
    /// Enable embedding scaling with default factor (sqrt(hidden_size)).
    pub fn with_embedding_scaling(mut self) -> Self {
        self.scale_embeddings = true;
        self.embedding_scale = Some((self.hidden_size as f32).sqrt());
        self
    }
    
    /// Set custom embedding scale factor.
    pub fn with_custom_scale(mut self, scale: f32) -> Self {
        self.scale_embeddings = true;
        self.embedding_scale = Some(scale);
        self
    }
    
    /// Get the effective embedding scale factor.
    pub fn effective_scale(&self) -> f32 {
        if self.scale_embeddings {
            self.embedding_scale.unwrap_or_else(|| (self.hidden_size as f32).sqrt())
        } else {
            1.0
        }
    }
    
    /// Validate the configuration.
    pub fn validate(&self) -> Result<(), String> {
        if self.max_seq_len == 0 {
            return Err("max_seq_len must be greater than 0".to_string());
        }
        
        if self.hidden_size == 0 {
            return Err("hidden_size must be greater than 0".to_string());
        }
        
        if self.dropout < 0.0 || self.dropout > 1.0 {
            return Err(format!("dropout must be between 0.0 and 1.0, got {}", self.dropout));
        }
        
        if let Some(scale) = self.embedding_scale {
            if scale <= 0.0 {
                return Err(format!("embedding_scale must be positive, got {}", scale));
            }
        }
        
        Ok(())
    }
    
    /// Get a summary of the configuration.
    pub fn summary(&self) -> String {
        format!(
            "PositionalConfig {{ max_seq_len: {}, hidden_size: {}, dropout: {:.3}, scale: {:.3} }}",
            self.max_seq_len,
            self.hidden_size,
            self.dropout,
            self.effective_scale()
        )
    }
}

impl Default for PositionalConfig {
    fn default() -> Self {
        Self::new(2048, 512) // Common defaults
    }
}

/// Utility functions for positional encoding.
pub mod utils {
    use super::*;
    use crate::tensor::{Tensor, Shape, TensorOps};
    use crate::tensor::cpu::CpuTensorFactory;
    use crate::tensor::TensorFactory;
    use std::f32::consts::PI;
    
    /// Generate sinusoidal position encodings.
    pub fn generate_sinusoidal_encodings<T: Numeric>(
        max_seq_len: usize,
        hidden_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        if hidden_size % 2 != 0 {
            return Err(AttentionError::InvalidConfiguration {
                message: "hidden_size must be even for sinusoidal encoding".to_string(),
                config_field: Some("hidden_size".to_string()),
                context: None,
            });
        }
        
        let mut encodings = vec![T::ZERO; max_seq_len * hidden_size];
        
        for pos in 0..max_seq_len {
            for i in 0..(hidden_size / 2) {
                let angle = pos as f32 / 10000.0_f32.powf(2.0 * i as f32 / hidden_size as f32);
                
                // Even indices: sin
                encodings[pos * hidden_size + 2 * i] = T::from_f32(angle.sin());
                // Odd indices: cos
                encodings[pos * hidden_size + 2 * i + 1] = T::from_f32(angle.cos());
            }
        }
        
        let shape = Shape::new(vec![max_seq_len, hidden_size]);
        CpuTensor::from_data(encodings, shape).map_err(AttentionError::Tensor)
    }
    
    /// Apply dropout to tensor.
    pub fn apply_dropout<T: Numeric>(
        tensor: &CpuTensor<T>,
        dropout_rate: f32,
        training: bool,
    ) -> Result<CpuTensor<T>, AttentionError> {
        if !training || dropout_rate == 0.0 {
            return Ok(tensor.clone());
        }
        
        // Simple dropout implementation
        let data = tensor.data();
        let mut result_data = Vec::with_capacity(data.len());
        let keep_prob = 1.0 - dropout_rate;
        let scale = 1.0 / keep_prob;
        
        for &value in data {
            // Simple random number generation (not cryptographically secure)
            let random = (rand::random::<u32>() as f32) / (u32::MAX as f32);
            if random < keep_prob {
                result_data.push(value * T::from_f32(scale));
            } else {
                result_data.push(T::ZERO);
            }
        }
        
        CpuTensor::from_data(result_data, tensor.shape().clone()).map_err(AttentionError::Tensor)
    }
    
    /// Scale embeddings by a factor.
    pub fn scale_embeddings<T: Numeric>(
        embeddings: &CpuTensor<T>,
        scale: f32,
    ) -> Result<CpuTensor<T>, AttentionError> {
        if scale == 1.0 {
            return Ok(embeddings.clone());
        }
        
        embeddings.mul_scalar(T::from_f32(scale)).map_err(AttentionError::Tensor)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::{Tensor, Shape};
    use crate::tensor::cpu::CpuTensorFactory;
    use crate::tensor::TensorFactory;
    
    #[test]
    fn test_positional_config() {
        let config = PositionalConfig::new(1024, 512);
        assert_eq!(config.max_seq_len, 1024);
        assert_eq!(config.hidden_size, 512);
        assert_eq!(config.dropout, 0.0);
        assert!(!config.scale_embeddings);
        assert!(config.validate().is_ok());
    }
    
    #[test]
    fn test_config_builder_pattern() {
        let config = PositionalConfig::new(2048, 768)
            .with_dropout(0.1)
            .with_embedding_scaling();
        
        assert_eq!(config.dropout, 0.1);
        assert!(config.scale_embeddings);
        assert!((config.effective_scale() - (768.0_f32).sqrt()).abs() < 1e-6);
        assert!(config.validate().is_ok());
    }
    
    #[test]
    fn test_config_validation() {
        // Valid config
        let config = PositionalConfig::new(1024, 512);
        assert!(config.validate().is_ok());
        
        // Invalid configs
        let config = PositionalConfig::new(0, 512);
        assert!(config.validate().is_err());
        
        let config = PositionalConfig::new(1024, 0);
        assert!(config.validate().is_err());
        
        let config = PositionalConfig::new(1024, 512).with_dropout(1.5);
        assert!(config.validate().is_err());
        
        let config = PositionalConfig::new(1024, 512).with_custom_scale(-1.0);
        assert!(config.validate().is_err());
    }
    
    #[test]
    fn test_sinusoidal_generation() {
        let encodings = utils::generate_sinusoidal_encodings::<f32>(10, 8).unwrap();
        assert_eq!(encodings.shape().dims(), &[10, 8]);
        
        // Test that odd hidden_size fails
        let result = utils::generate_sinusoidal_encodings::<f32>(10, 7);
        assert!(result.is_err());
    }
    
    #[test]
    fn test_embedding_scaling() {
        use crate::tensor::cpu::CpuTensorFactory;
        use crate::tensor::TensorFactory;
        
        let embeddings = CpuTensorFactory::ones(&Shape::new(vec![2, 5, 8])).unwrap();
        let scaled = utils::scale_embeddings(&embeddings, 2.0).unwrap();

        assert_eq!(scaled.shape().dims(), embeddings.shape().dims());
        
        // Check that values are scaled
        let original_data = embeddings.data();
        let scaled_data = scaled.data();
        for (orig, scaled_val) in original_data.iter().zip(scaled_data.iter()) {
            assert!((scaled_val - orig * 2.0_f32).abs() < 1e-6);
        }
    }
}
