//! Rotary positional encoding (RoPE) implementation.
//!
//! This module implements Rotary Position Embedding (RoPE) as introduced in
//! "RoFormer: Enhanced Transformer with Rotary Position Embedding". RoPE
//! encodes position information by rotating the query and key representations
//! using rotation matrices, which naturally incorporates relative position
//! information into the attention mechanism.

use std::marker::PhantomData;
use std::f32::consts::PI;
use crate::tensor::{Tensor, TensorOps, Shape, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::tensor::cpu::CpuTensorFactory;
use crate::tensor::TensorFactory;
use crate::error::{AttentionError, ErrorContext};
use super::{PositionalEncoding, PositionalConfig, utils};

/// Rotary positional encoding layer.
///
/// RoPE applies rotation matrices to query and key vectors based on their
/// position, which naturally encodes relative position information. This
/// approach has shown excellent performance in models like LLaMA and GPT-NeoX.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::attention::positional::{RotaryPositionalEncoding, PositionalConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create rotary positional encoding
/// let config = PositionalConfig::new(1024, 512);
/// let rope = RotaryPositionalEncoding::new(config, 10000.0).unwrap();
///
/// // Apply to query and key tensors
/// let query = CpuTensor::zeros(&Shape::new(vec![2, 10, 512])).unwrap();
/// let key = CpuTensor::zeros(&Shape::new(vec![2, 10, 512])).unwrap();
/// let (rotated_q, rotated_k) = rope.apply_rotary_encoding(&query, &key, None).unwrap();
/// ```
#[derive(Debug, Clone)]
pub struct RotaryPositionalEncoding<T: Numeric> {
    /// Configuration.
    config: PositionalConfig,
    /// Base frequency for rotation.
    base: f32,
    /// Precomputed cosine values: [max_seq_len, hidden_size/2].
    cos_cached: CpuTensor<T>,
    /// Precomputed sine values: [max_seq_len, hidden_size/2].
    sin_cached: CpuTensor<T>,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> RotaryPositionalEncoding<T> {
    /// Create a new rotary positional encoding layer.
    ///
    /// # Arguments
    /// * `config` - Positional encoding configuration
    /// * `base` - Base frequency for rotation (typically 10000.0)
    pub fn new(config: PositionalConfig, base: f32) -> Result<Self, AttentionError> {
        // Validate configuration
        config.validate().map_err(|msg| AttentionError::InvalidConfiguration {
            message: msg,
            config_field: None,
            context: Some(ErrorContext::new("new", "attention::positional::rotary")),
        })?;
        
        if config.hidden_size % 2 != 0 {
            return Err(AttentionError::InvalidConfiguration {
                message: "hidden_size must be even for rotary encoding".to_string(),
                config_field: Some("hidden_size".to_string()),
                context: Some(ErrorContext::new("new", "attention::positional::rotary")),
            });
        }
        
        if base <= 0.0 {
            return Err(AttentionError::InvalidConfiguration {
                message: "base must be positive".to_string(),
                config_field: Some("base".to_string()),
                context: Some(ErrorContext::new("new", "attention::positional::rotary")),
            });
        }
        
        // Precompute rotation matrices
        let (cos_cached, sin_cached) = Self::precompute_rotation_matrices(
            config.max_seq_len,
            config.hidden_size,
            base,
        )?;
        
        Ok(Self {
            config,
            base,
            cos_cached,
            sin_cached,
            _phantom: PhantomData,
        })
    }
    
    /// Get the configuration.
    pub fn config(&self) -> &PositionalConfig {
        &self.config
    }
    
    /// Get the base frequency.
    pub fn base(&self) -> f32 {
        self.base
    }
    
    /// Get the cached cosine values.
    pub fn cos_cached(&self) -> &CpuTensor<T> {
        &self.cos_cached
    }
    
    /// Get the cached sine values.
    pub fn sin_cached(&self) -> &CpuTensor<T> {
        &self.sin_cached
    }
    
    /// Precompute rotation matrices for all positions.
    fn precompute_rotation_matrices(
        max_seq_len: usize,
        hidden_size: usize,
        base: f32,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), AttentionError> {
        let half_dim = hidden_size / 2;
        
        // Compute frequency for each dimension pair
        let mut freqs = Vec::with_capacity(half_dim);
        for i in 0..half_dim {
            let freq = 1.0 / base.powf(2.0 * i as f32 / hidden_size as f32);
            freqs.push(freq);
        }
        
        // Precompute cos and sin for all positions
        let mut cos_data = Vec::with_capacity(max_seq_len * half_dim);
        let mut sin_data = Vec::with_capacity(max_seq_len * half_dim);
        
        for pos in 0..max_seq_len {
            for &freq in &freqs {
                let angle = pos as f32 * freq;
                cos_data.push(T::from_f32(angle.cos()));
                sin_data.push(T::from_f32(angle.sin()));
            }
        }
        
        let shape = Shape::new(vec![max_seq_len, half_dim]);
        let cos_tensor = CpuTensor::<T>::from_data(cos_data, shape.clone()).map_err(AttentionError::Tensor)?;
        let sin_tensor = CpuTensor::<T>::from_data(sin_data, shape).map_err(AttentionError::Tensor)?;
        
        Ok((cos_tensor, sin_tensor))
    }
    
    /// Apply rotary encoding to query and key tensors.
    ///
    /// # Arguments
    /// * `query` - Query tensor with shape [batch_size, seq_len, hidden_size]
    /// * `key` - Key tensor with shape [batch_size, seq_len, hidden_size]
    /// * `position_offset` - Optional offset for position indices
    ///
    /// # Returns
    /// * Tuple of (rotated_query, rotated_key)
    pub fn apply_rotary_encoding(
        &self,
        query: &CpuTensor<T>,
        key: &CpuTensor<T>,
        position_offset: Option<usize>,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), AttentionError> {
        // Validate inputs
        self.validate_tensor(query, "query")?;
        self.validate_tensor(key, "key")?;
        
        let shape = query.shape().dims();
        let batch_size = shape[0];
        let seq_len = shape[1];
        let hidden_size = shape[2];
        
        // Check that query and key have the same shape
        if query.shape().dims() != key.shape().dims() {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("query shape: {:?}", query.shape().dims()),
                actual: format!("key shape: {:?}", key.shape().dims()),
                operation: "rotary_encoding_shape_match".to_string(),
                context: Some(ErrorContext::new("apply_rotary_encoding", "attention::positional::rotary")),
            });
        }
        
        // Extract rotation matrices for the sequence
        let offset = position_offset.unwrap_or(0);
        let (cos_seq, sin_seq) = self.extract_rotation_matrices(seq_len, offset)?;
        
        // Apply rotation to query and key
        let rotated_query = self.rotate_tensor(query, &cos_seq, &sin_seq)?;
        let rotated_key = self.rotate_tensor(key, &cos_seq, &sin_seq)?;
        
        Ok((rotated_query, rotated_key))
    }
    
    /// Extract rotation matrices for a specific sequence.
    fn extract_rotation_matrices(
        &self,
        seq_len: usize,
        offset: usize,
    ) -> Result<(CpuTensor<T>, CpuTensor<T>), AttentionError> {
        let total_len = seq_len + offset;
        if total_len > self.config.max_seq_len {
            return Err(AttentionError::InvalidConfiguration {
                message: format!(
                    "Total sequence length {} (seq_len {} + offset {}) exceeds maximum {}",
                    total_len, seq_len, offset, self.config.max_seq_len
                ),
                config_field: Some("seq_len".to_string()),
                context: Some(ErrorContext::new("extract_rotation_matrices", "attention::positional::rotary")),
            });
        }
        
        let half_dim = self.config.hidden_size / 2;
        let cos_data = self.cos_cached.data();
        let sin_data = self.sin_cached.data();
        
        // Extract the relevant portion
        let mut cos_seq_data = Vec::with_capacity(seq_len * half_dim);
        let mut sin_seq_data = Vec::with_capacity(seq_len * half_dim);
        
        for pos in offset..(offset + seq_len) {
            let start_idx = pos * half_dim;
            let end_idx = start_idx + half_dim;
            cos_seq_data.extend_from_slice(&cos_data[start_idx..end_idx]);
            sin_seq_data.extend_from_slice(&sin_data[start_idx..end_idx]);
        }
        
        let shape = Shape::new(vec![seq_len, half_dim]);
        let cos_seq = CpuTensor::<T>::from_data(cos_seq_data, shape.clone()).map_err(AttentionError::Tensor)?;
        let sin_seq = CpuTensor::<T>::from_data(sin_seq_data, shape).map_err(AttentionError::Tensor)?;
        
        Ok((cos_seq, sin_seq))
    }
    
    /// Apply rotation to a tensor using cos and sin matrices.
    fn rotate_tensor(
        &self,
        tensor: &CpuTensor<T>,
        cos_seq: &CpuTensor<T>,
        sin_seq: &CpuTensor<T>,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let shape = tensor.shape().dims();
        let batch_size = shape[0];
        let seq_len = shape[1];
        let hidden_size = shape[2];
        let half_dim = hidden_size / 2;
        
        let tensor_data = tensor.data();
        let cos_data = cos_seq.data();
        let sin_data = sin_seq.data();
        
        let mut rotated_data = vec![T::ZERO; tensor_data.len()];
        
        for batch_idx in 0..batch_size {
            for pos in 0..seq_len {
                for dim_pair in 0..half_dim {
                    // Get the two dimensions that form a pair
                    let dim1 = 2 * dim_pair;
                    let dim2 = 2 * dim_pair + 1;
                    
                    // Calculate tensor indices
                    let tensor_idx1 = batch_idx * (seq_len * hidden_size) + pos * hidden_size + dim1;
                    let tensor_idx2 = batch_idx * (seq_len * hidden_size) + pos * hidden_size + dim2;
                    
                    // Calculate rotation matrix indices
                    let rot_idx = pos * half_dim + dim_pair;
                    
                    // Get original values
                    let x1 = tensor_data[tensor_idx1];
                    let x2 = tensor_data[tensor_idx2];
                    
                    // Get rotation values
                    let cos_val = cos_data[rot_idx];
                    let sin_val = sin_data[rot_idx];
                    
                    // Apply rotation: [cos -sin; sin cos] * [x1; x2]
                    rotated_data[tensor_idx1] = x1 * cos_val - x2 * sin_val;
                    rotated_data[tensor_idx2] = x1 * sin_val + x2 * cos_val;
                }
            }
        }
        
        CpuTensor::<T>::from_data(rotated_data, tensor.shape().clone()).map_err(AttentionError::Tensor)
    }
    
    /// Validate tensor shape for rotary encoding.
    fn validate_tensor(&self, tensor: &CpuTensor<T>, name: &str) -> Result<(), AttentionError> {
        let shape = tensor.shape().dims();
        
        if shape.len() != 3 {
            return Err(AttentionError::DimensionMismatch {
                expected: "3D tensor [batch_size, seq_len, hidden_size]".to_string(),
                actual: format!("{}: {}D tensor", name, shape.len()),
                operation: "tensor_validation".to_string(),
                context: Some(ErrorContext::new("validate_tensor", "attention::positional::rotary")),
            });
        }
        
        if shape[2] != self.config.hidden_size {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", self.config.hidden_size),
                actual: format!("{} hidden_size: {}", name, shape[2]),
                operation: "hidden_size_validation".to_string(),
                context: Some(ErrorContext::new("validate_tensor", "attention::positional::rotary")),
            });
        }
        
        Ok(())
    }
}

impl<T: Numeric> PositionalEncoding<T> for RotaryPositionalEncoding<T> {
    fn encode(
        &self,
        embeddings: &CpuTensor<T>,
        _position_offset: Option<usize>,
    ) -> Result<CpuTensor<T>, AttentionError> {
        // RoPE doesn't modify embeddings directly; it's applied to Q and K in attention
        // For compatibility with the trait, we return scaled embeddings if configured
        if self.config.scale_embeddings {
            utils::scale_embeddings(embeddings, self.config.effective_scale())
        } else {
            Ok(embeddings.clone())
        }
    }
    
    fn max_seq_len(&self) -> usize {
        self.config.max_seq_len
    }
    
    fn hidden_size(&self) -> usize {
        self.config.hidden_size
    }
    
    fn encoding_type(&self) -> &'static str {
        "rotary"
    }
    
    fn supports_incremental(&self) -> bool {
        true
    }
    
    fn get_encodings(
        &self,
        positions: &[usize],
        hidden_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        if hidden_size != self.config.hidden_size {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", self.config.hidden_size),
                actual: format!("hidden_size: {}", hidden_size),
                operation: "get_encodings_validation".to_string(),
                context: Some(ErrorContext::new("get_encodings", "attention::positional::rotary")),
            });
        }
        
        // For RoPE, we return the rotation matrices for the given positions
        let half_dim = hidden_size / 2;
        let cos_data = self.cos_cached.data();
        let sin_data = self.sin_cached.data();
        
        let mut result_data = Vec::with_capacity(positions.len() * hidden_size);
        
        for &pos in positions {
            if pos >= self.config.max_seq_len {
                return Err(AttentionError::InvalidConfiguration {
                    message: format!("Position {} exceeds maximum {}", pos, self.config.max_seq_len),
                    config_field: Some("position".to_string()),
                    context: Some(ErrorContext::new("get_encodings", "attention::positional::rotary")),
                });
            }
            
            // Interleave cos and sin values to form the rotation encoding
            let start_idx = pos * half_dim;
            let end_idx = start_idx + half_dim;
            
            for i in start_idx..end_idx {
                result_data.push(cos_data[i]);
                result_data.push(sin_data[i]);
            }
        }
        
        let shape = Shape::new(vec![positions.len(), hidden_size]);
        CpuTensor::from_data(result_data, shape).map_err(AttentionError::Tensor)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::cpu::CpuTensorFactory;
    use crate::tensor::TensorFactory;

    fn create_test_tensor(batch_size: usize, seq_len: usize, hidden_size: usize) -> CpuTensor<f32> {
        CpuTensorFactory::randn(&Shape::new(vec![batch_size, seq_len, hidden_size]), 0.0, 0.1).unwrap()
    }

    #[test]
    fn test_rotary_creation() {
        let config = PositionalConfig::new(1024, 512);
        let rope = RotaryPositionalEncoding::<f32>::new(config.clone(), 10000.0).unwrap();
        
        assert_eq!(rope.max_seq_len(), 1024);
        assert_eq!(rope.hidden_size(), 512);
        assert_eq!(rope.encoding_type(), "rotary");
        assert!(rope.supports_incremental());
        assert_eq!(rope.config(), &config);
        assert_eq!(rope.base(), 10000.0);
        
        // Check cached matrices
        assert_eq!(rope.cos_cached().shape().dims(), &[1024, 256]); // hidden_size/2
        assert_eq!(rope.sin_cached().shape().dims(), &[1024, 256]);
    }

    #[test]
    fn test_rotary_encoding_application() {
        let config = PositionalConfig::new(100, 64);
        let rope = RotaryPositionalEncoding::<f32>::new(config, 10000.0).unwrap();

        // Create test tensors with specific values to ensure rotation is visible
        let mut query_data = vec![0.0; 2 * 10 * 64];
        let mut key_data = vec![0.0; 2 * 10 * 64];

        // Set some specific values that will be affected by rotation
        for i in 0..query_data.len() {
            query_data[i] = if i % 2 == 0 { 1.0 } else { 0.0 };
            key_data[i] = if i % 2 == 0 { 0.0 } else { 1.0 };
        }

        let query = CpuTensor::from_data(query_data.clone(), Shape::new(vec![2, 10, 64])).unwrap();
        let key = CpuTensor::from_data(key_data.clone(), Shape::new(vec![2, 10, 64])).unwrap();

        let (rotated_q, rotated_k) = rope.apply_rotary_encoding(&query, &key, None).unwrap();

        assert_eq!(rotated_q.shape().dims(), &[2, 10, 64]);
        assert_eq!(rotated_k.shape().dims(), &[2, 10, 64]);

        // Check that rotation actually changed the values
        // For position > 0, we should definitely see changes
        let orig_q_data = query.data();
        let rot_q_data = rotated_q.data();

        // Check specifically at position 1 (not position 0 which might have minimal rotation)
        let pos1_start = 64; // Start of position 1 data
        let pos1_end = 128;  // End of position 1 data

        let mut different_count = 0;
        for i in pos1_start..pos1_end {
            if (orig_q_data[i] - rot_q_data[i]).abs() > 1e-6 {
                different_count += 1;
            }
        }

        assert!(different_count > 0, "Rotation should change values at position 1");
    }

    #[test]
    fn test_rotary_with_offset() {
        let config = PositionalConfig::new(100, 64);
        let rope = RotaryPositionalEncoding::<f32>::new(config, 10000.0).unwrap();

        // Create test tensor with specific pattern
        let mut query_data = vec![0.0; 1 * 5 * 64];
        for i in 0..query_data.len() {
            query_data[i] = if i % 2 == 0 { 1.0 } else { 0.0 };
        }
        let query = CpuTensor::from_data(query_data.clone(), Shape::new(vec![1, 5, 64])).unwrap();
        let key = query.clone();

        // Apply without offset
        let (rot_q1, _) = rope.apply_rotary_encoding(&query, &key, None).unwrap();

        // Apply with offset
        let (rot_q2, _) = rope.apply_rotary_encoding(&query, &key, Some(3)).unwrap();

        // Results should be different due to different positions
        // Check at position 1 where we should see clear differences
        let data1 = rot_q1.data();
        let data2 = rot_q2.data();

        let pos1_start = 64; // Start of position 1
        let pos1_end = 128;  // End of position 1

        let mut different_count = 0;
        for i in pos1_start..pos1_end {
            if (data1[i] - data2[i]).abs() > 1e-5 {
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Different offsets should produce different rotations");
    }

    #[test]
    fn test_rotation_properties() {
        let config = PositionalConfig::new(100, 4); // Small size for testing
        let rope = RotaryPositionalEncoding::<f32>::new(config, 10000.0).unwrap();
        
        // Create a simple tensor
        let tensor_data = vec![1.0, 0.0, 0.0, 1.0]; // [1, 0, 0, 1]
        let tensor = CpuTensor::from_data(tensor_data, Shape::new(vec![1, 1, 4])).unwrap();
        
        let (rotated, _) = rope.apply_rotary_encoding(&tensor, &tensor, None).unwrap();
        let rotated_data = rotated.data();
        
        // For position 0, rotation should be identity (cos=1, sin=0 for first frequency)
        // But higher frequencies will have different values
        assert_eq!(rotated_data.len(), 4);
        
        // Check that the rotation preserves the norm (approximately)
        let original_norm_sq = 1.0 * 1.0 + 0.0 * 0.0 + 0.0 * 0.0 + 1.0 * 1.0; // 2.0
        let rotated_norm_sq = rotated_data[0] * rotated_data[0] + 
                             rotated_data[1] * rotated_data[1] + 
                             rotated_data[2] * rotated_data[2] + 
                             rotated_data[3] * rotated_data[3];
        
        assert!((original_norm_sq - rotated_norm_sq).abs() < 1e-5, "Rotation should preserve norm");
    }

    #[test]
    fn test_get_specific_encodings() {
        let config = PositionalConfig::new(100, 8);
        let rope = RotaryPositionalEncoding::<f32>::new(config, 10000.0).unwrap();
        
        let positions = vec![0, 5, 10];
        let encodings = rope.get_encodings(&positions, 8).unwrap();
        
        assert_eq!(encodings.shape().dims(), &[3, 8]);
        
        // Check that different positions have different encodings
        let data = encodings.data();
        let pos0 = &data[0..8];
        let pos5 = &data[8..16];
        
        let mut different_count = 0;
        for (p0, p5) in pos0.iter().zip(pos5.iter()) {
            if (p0 - p5).abs() > 1e-6 {
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Different positions should have different encodings");
    }

    #[test]
    fn test_input_validation() {
        let config = PositionalConfig::new(100, 64);
        let rope = RotaryPositionalEncoding::<f32>::new(config, 10000.0).unwrap();
        
        let query = create_test_tensor(1, 5, 64);
        let key_wrong_shape = create_test_tensor(1, 3, 64); // Different seq_len
        
        // Mismatched shapes should fail
        let result = rope.apply_rotary_encoding(&query, &key_wrong_shape, None);
        assert!(result.is_err());
        
        // Wrong hidden size should fail
        let query_wrong_hidden = create_test_tensor(1, 5, 32);
        let result = rope.apply_rotary_encoding(&query_wrong_hidden, &query_wrong_hidden, None);
        assert!(result.is_err());
    }

    #[test]
    fn test_invalid_config() {
        // Odd hidden size should fail
        let config = PositionalConfig::new(100, 63);
        let result = RotaryPositionalEncoding::<f32>::new(config, 10000.0);
        assert!(result.is_err());
        
        // Negative base should fail
        let config = PositionalConfig::new(100, 64);
        let result = RotaryPositionalEncoding::<f32>::new(config, -1.0);
        assert!(result.is_err());
    }

    #[test]
    fn test_sequence_length_limits() {
        let config = PositionalConfig::new(10, 8); // Small max_seq_len
        let rope = RotaryPositionalEncoding::<f32>::new(config, 10000.0).unwrap();
        
        let query = create_test_tensor(1, 5, 8);
        let key = create_test_tensor(1, 5, 8);
        
        // Should work within limits
        let result = rope.apply_rotary_encoding(&query, &key, Some(3));
        assert!(result.is_ok());
        
        // Should fail when exceeding limits
        let result = rope.apply_rotary_encoding(&query, &key, Some(8)); // 5 + 8 > 10
        assert!(result.is_err());
    }
}
