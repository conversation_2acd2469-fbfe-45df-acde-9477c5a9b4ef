//! Learned positional encoding implementation.
//!
//! This module implements learned positional embeddings similar to those used
//! in BERT and other models. Unlike sinusoidal encodings, these are trainable
//! parameters that the model learns during training.

use std::marker::PhantomData;
use crate::tensor::{Tensor, TensorOps, <PERSON>hape, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::tensor::cpu::CpuTensorFactory;
use crate::tensor::TensorFactory;
use crate::error::{AttentionError, ErrorContext};
use super::{PositionalEncoding, PositionalConfig, utils};

/// Learned positional encoding layer.
///
/// This encoding uses trainable embedding parameters for each position.
/// The embeddings are initialized randomly and learned during training.
/// This approach is used in models like BERT and GPT.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::attention::positional::{LearnedPositionalEncoding, PositionalConfig};
/// use qilin_inference::tensor::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};
///
/// // Create learned positional encoding
/// let config = PositionalConfig::new(1024, 512);
/// let mut pos_enc = LearnedPositionalEncoding::new(config).unwrap();
///
/// // Apply to embeddings
/// let embeddings = CpuTensor::zeros(&Shape::new(vec![2, 10, 512])).unwrap();
/// let encoded = pos_enc.encode(&embeddings, None).unwrap();
/// assert_eq!(encoded.shape().dims(), &[2, 10, 512]);
/// ```
#[derive(Debug, Clone)]
pub struct LearnedPositionalEncoding<T: Numeric> {
    /// Configuration.
    config: PositionalConfig,
    /// Learnable position embeddings: [max_seq_len, hidden_size].
    position_embeddings: CpuTensor<T>,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> LearnedPositionalEncoding<T> {
    /// Create a new learned positional encoding layer.
    pub fn new(config: PositionalConfig) -> Result<Self, AttentionError> {
        // Validate configuration
        config.validate().map_err(|msg| AttentionError::InvalidConfiguration {
            message: msg,
            config_field: None,
            context: Some(ErrorContext::new("new", "attention::positional::learned")),
        })?;
        
        // Initialize position embeddings with small random values
        let position_embeddings_f32 = CpuTensorFactory::randn(
            &Shape::new(vec![config.max_seq_len, config.hidden_size]),
            0.0,
            0.02, // Small standard deviation for initialization
        ).map_err(AttentionError::Tensor)?;

        // Convert to the target type
        let position_embeddings = Self::convert_tensor_type(position_embeddings_f32)?;
        
        Ok(Self {
            config,
            position_embeddings,
            _phantom: PhantomData,
        })
    }
    
    /// Create a learned positional encoding with custom initialization.
    pub fn with_custom_init(
        config: PositionalConfig,
        init_std: f32,
    ) -> Result<Self, AttentionError> {
        config.validate().map_err(|msg| AttentionError::InvalidConfiguration {
            message: msg,
            config_field: None,
            context: Some(ErrorContext::new("with_custom_init", "attention::positional::learned")),
        })?;
        
        let position_embeddings_f32 = CpuTensorFactory::randn(
            &Shape::new(vec![config.max_seq_len, config.hidden_size]),
            0.0,
            init_std,
        ).map_err(AttentionError::Tensor)?;

        let position_embeddings = Self::convert_tensor_type(position_embeddings_f32)?;
        
        Ok(Self {
            config,
            position_embeddings,
            _phantom: PhantomData,
        })
    }
    
    /// Create a learned positional encoding initialized to zeros.
    pub fn zeros(config: PositionalConfig) -> Result<Self, AttentionError> {
        config.validate().map_err(|msg| AttentionError::InvalidConfiguration {
            message: msg,
            config_field: None,
            context: Some(ErrorContext::new("zeros", "attention::positional::learned")),
        })?;
        
        let position_embeddings_f32 = CpuTensorFactory::zeros(
            &Shape::new(vec![config.max_seq_len, config.hidden_size])
        ).map_err(AttentionError::Tensor)?;

        let position_embeddings = Self::convert_tensor_type(position_embeddings_f32)?;
        
        Ok(Self {
            config,
            position_embeddings,
            _phantom: PhantomData,
        })
    }
    
    /// Get the configuration.
    pub fn config(&self) -> &PositionalConfig {
        &self.config
    }
    
    /// Get the position embeddings tensor.
    pub fn position_embeddings(&self) -> &CpuTensor<T> {
        &self.position_embeddings
    }
    
    /// Get mutable reference to position embeddings for training.
    pub fn position_embeddings_mut(&mut self) -> &mut CpuTensor<T> {
        &mut self.position_embeddings
    }
    
    /// Update position embeddings (for training).
    pub fn update_embeddings(&mut self, new_embeddings: CpuTensor<T>) -> Result<(), AttentionError> {
        let expected_shape = vec![self.config.max_seq_len, self.config.hidden_size];
        if new_embeddings.shape().dims() != expected_shape {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("{:?}", expected_shape),
                actual: format!("{:?}", new_embeddings.shape().dims()),
                operation: "update_embeddings".to_string(),
                context: Some(ErrorContext::new("update_embeddings", "attention::positional::learned")),
            });
        }
        
        self.position_embeddings = new_embeddings;
        Ok(())
    }
    
    /// Extract positional encodings for specific sequence length.
    fn extract_encodings(
        &self,
        seq_len: usize,
        batch_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        if seq_len > self.config.max_seq_len {
            return Err(AttentionError::InvalidConfiguration {
                message: format!(
                    "Sequence length {} exceeds maximum {}",
                    seq_len, self.config.max_seq_len
                ),
                config_field: Some("seq_len".to_string()),
                context: Some(ErrorContext::new("extract_encodings", "attention::positional::learned")),
            });
        }
        
        // Extract the first seq_len positions
        let embedding_data = self.position_embeddings.data();
        let hidden_size = self.config.hidden_size;
        
        // Create batch of encodings: [batch_size, seq_len, hidden_size]
        let mut batch_data = Vec::with_capacity(batch_size * seq_len * hidden_size);
        
        for _ in 0..batch_size {
            for pos in 0..seq_len {
                let start_idx = pos * hidden_size;
                let end_idx = start_idx + hidden_size;
                batch_data.extend_from_slice(&embedding_data[start_idx..end_idx]);
            }
        }
        
        let shape = Shape::new(vec![batch_size, seq_len, hidden_size]);
        CpuTensor::from_data(batch_data, shape).map_err(AttentionError::Tensor)
    }
    
    /// Extract positional encodings with offset for incremental generation.
    fn extract_encodings_with_offset(
        &self,
        seq_len: usize,
        batch_size: usize,
        offset: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let total_len = seq_len + offset;
        if total_len > self.config.max_seq_len {
            return Err(AttentionError::InvalidConfiguration {
                message: format!(
                    "Total sequence length {} (seq_len {} + offset {}) exceeds maximum {}",
                    total_len, seq_len, offset, self.config.max_seq_len
                ),
                config_field: Some("seq_len".to_string()),
                context: Some(ErrorContext::new("extract_encodings_with_offset", "attention::positional::learned")),
            });
        }
        
        let embedding_data = self.position_embeddings.data();
        let hidden_size = self.config.hidden_size;
        
        // Create batch of encodings starting from offset
        let mut batch_data = Vec::with_capacity(batch_size * seq_len * hidden_size);
        
        for _ in 0..batch_size {
            for pos in offset..(offset + seq_len) {
                let start_idx = pos * hidden_size;
                let end_idx = start_idx + hidden_size;
                batch_data.extend_from_slice(&embedding_data[start_idx..end_idx]);
            }
        }
        
        let shape = Shape::new(vec![batch_size, seq_len, hidden_size]);
        CpuTensor::from_data(batch_data, shape).map_err(AttentionError::Tensor)
    }
    
    /// Convert f32 tensor to target type.
    fn convert_tensor_type(tensor_f32: CpuTensor<f32>) -> Result<CpuTensor<T>, AttentionError> {
        let data_f32 = tensor_f32.data();
        let converted_data: Vec<T> = data_f32.iter().map(|&x| T::from_f32(x)).collect();
        CpuTensor::from_data(converted_data, tensor_f32.shape().clone()).map_err(AttentionError::Tensor)
    }

    /// Validate input embeddings shape.
    fn validate_embeddings(&self, embeddings: &CpuTensor<T>) -> Result<(), AttentionError> {
        let shape = embeddings.shape().dims();
        
        if shape.len() != 3 {
            return Err(AttentionError::DimensionMismatch {
                expected: "3D tensor [batch_size, seq_len, hidden_size]".to_string(),
                actual: format!("{}D tensor", shape.len()),
                operation: "embeddings_validation".to_string(),
                context: Some(ErrorContext::new("validate_embeddings", "attention::positional::learned")),
            });
        }
        
        if shape[2] != self.config.hidden_size {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", self.config.hidden_size),
                actual: format!("hidden_size: {}", shape[2]),
                operation: "hidden_size_validation".to_string(),
                context: Some(ErrorContext::new("validate_embeddings", "attention::positional::learned")),
            });
        }
        
        Ok(())
    }
}

impl<T: Numeric> PositionalEncoding<T> for LearnedPositionalEncoding<T> {
    fn encode(
        &self,
        embeddings: &CpuTensor<T>,
        position_offset: Option<usize>,
    ) -> Result<CpuTensor<T>, AttentionError> {
        // Validate input
        self.validate_embeddings(embeddings)?;
        
        let shape = embeddings.shape().dims();
        let batch_size = shape[0];
        let seq_len = shape[1];
        
        // Scale embeddings if configured
        let scaled_embeddings = if self.config.scale_embeddings {
            utils::scale_embeddings(embeddings, self.config.effective_scale())?
        } else {
            embeddings.clone()
        };
        
        // Extract positional encodings
        let pos_encodings = if let Some(offset) = position_offset {
            self.extract_encodings_with_offset(seq_len, batch_size, offset)?
        } else {
            self.extract_encodings(seq_len, batch_size)?
        };
        
        // Add positional encodings to embeddings
        let result = scaled_embeddings.add(&pos_encodings).map_err(AttentionError::Tensor)?;
        
        // Apply dropout if configured
        if self.config.dropout > 0.0 {
            utils::apply_dropout(&result, self.config.dropout, true)
        } else {
            Ok(result)
        }
    }
    
    fn max_seq_len(&self) -> usize {
        self.config.max_seq_len
    }
    
    fn hidden_size(&self) -> usize {
        self.config.hidden_size
    }
    
    fn encoding_type(&self) -> &'static str {
        "learned"
    }
    
    fn supports_incremental(&self) -> bool {
        true
    }
    
    fn get_encodings(
        &self,
        positions: &[usize],
        hidden_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        if hidden_size != self.config.hidden_size {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", self.config.hidden_size),
                actual: format!("hidden_size: {}", hidden_size),
                operation: "get_encodings_validation".to_string(),
                context: Some(ErrorContext::new("get_encodings", "attention::positional::learned")),
            });
        }
        
        let embedding_data = self.position_embeddings.data();
        let mut result_data = Vec::with_capacity(positions.len() * hidden_size);
        
        for &pos in positions {
            if pos >= self.config.max_seq_len {
                return Err(AttentionError::InvalidConfiguration {
                    message: format!("Position {} exceeds maximum {}", pos, self.config.max_seq_len),
                    config_field: Some("position".to_string()),
                    context: Some(ErrorContext::new("get_encodings", "attention::positional::learned")),
                });
            }
            
            let start_idx = pos * hidden_size;
            let end_idx = start_idx + hidden_size;
            result_data.extend_from_slice(&embedding_data[start_idx..end_idx]);
        }
        
        let shape = Shape::new(vec![positions.len(), hidden_size]);
        CpuTensor::from_data(result_data, shape).map_err(AttentionError::Tensor)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::cpu::CpuTensorFactory;
    use crate::tensor::TensorFactory;

    fn create_test_embeddings(batch_size: usize, seq_len: usize, hidden_size: usize) -> CpuTensor<f32> {
        CpuTensorFactory::ones(&Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap()
    }

    #[test]
    fn test_learned_creation() {
        let config = PositionalConfig::new(1024, 512);
        let pos_enc = LearnedPositionalEncoding::<f32>::new(config.clone()).unwrap();
        
        assert_eq!(pos_enc.max_seq_len(), 1024);
        assert_eq!(pos_enc.hidden_size(), 512);
        assert_eq!(pos_enc.encoding_type(), "learned");
        assert!(pos_enc.supports_incremental());
        assert_eq!(pos_enc.config(), &config);
        
        // Check that embeddings are initialized
        let embeddings = pos_enc.position_embeddings();
        assert_eq!(embeddings.shape().dims(), &[1024, 512]);
    }

    #[test]
    fn test_learned_encoding() {
        let config = PositionalConfig::new(100, 64);
        let mut pos_enc = LearnedPositionalEncoding::<f32>::zeros(config).unwrap();

        // Manually set some non-zero values in position embeddings
        let mut pos_data = vec![0.0; 100 * 64];
        for i in 0..pos_data.len() {
            pos_data[i] = (i % 10) as f32 * 0.1; // Create a pattern
        }
        let pos_embeddings = CpuTensor::from_data(pos_data, Shape::new(vec![100, 64])).unwrap();
        pos_enc.update_embeddings(pos_embeddings).unwrap();

        let embeddings = create_test_embeddings(2, 10, 64);
        let encoded = pos_enc.encode(&embeddings, None).unwrap();

        assert_eq!(encoded.shape().dims(), &[2, 10, 64]);

        // Check that encoding is different from original embeddings
        let orig_data = embeddings.data();
        let encoded_data = encoded.data();
        let mut different_count = 0;
        for (orig, enc) in orig_data.iter().zip(encoded_data.iter()) {
            if (orig - enc).abs() > 1e-6 {
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Encoding should modify the embeddings");
    }

    #[test]
    fn test_learned_with_offset() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = LearnedPositionalEncoding::<f32>::with_custom_init(config, 1.0).unwrap(); // Large init

        let embeddings = create_test_embeddings(1, 5, 64);

        // Encode without offset
        let encoded1 = pos_enc.encode(&embeddings, None).unwrap();

        // Encode with offset
        let encoded2 = pos_enc.encode(&embeddings, Some(3)).unwrap();

        // Results should be different due to different positions
        let data1 = encoded1.data();
        let data2 = encoded2.data();
        let mut different_count = 0;
        for (d1, d2) in data1.iter().zip(data2.iter()) {
            if (d1 - d2).abs() > 1e-2 { // More lenient
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Different offsets should produce different encodings");
    }

    #[test]
    fn test_custom_initialization() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = LearnedPositionalEncoding::<f32>::with_custom_init(config, 0.5).unwrap(); // Larger init

        let embeddings = pos_enc.position_embeddings();
        assert_eq!(embeddings.shape().dims(), &[100, 64]);

        // Check that values are not all zeros (random initialization)
        let data = embeddings.data();
        let non_zero_count = data.iter().filter(|&&x| x.abs() > 1e-3).count(); // More lenient
        assert!(non_zero_count > 0, "Should have non-zero initialized values");
    }

    #[test]
    fn test_zero_initialization() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = LearnedPositionalEncoding::<f32>::zeros(config).unwrap();
        
        let embeddings = pos_enc.position_embeddings();
        let data = embeddings.data();
        
        // All values should be zero
        for &value in data {
            assert!((value).abs() < 1e-6, "All values should be zero");
        }
    }

    #[test]
    fn test_embedding_update() {
        let config = PositionalConfig::new(10, 8);
        let mut pos_enc = LearnedPositionalEncoding::<f32>::new(config).unwrap();
        
        // Create new embeddings
        let new_embeddings = CpuTensorFactory::ones(&Shape::new(vec![10, 8])).unwrap();
        
        // Update embeddings
        pos_enc.update_embeddings(new_embeddings.clone()).unwrap();
        
        // Check that embeddings were updated
        let updated = pos_enc.position_embeddings();
        let updated_data = updated.data();
        let new_data = new_embeddings.data();
        
        for (updated_val, new_val) in updated_data.iter().zip(new_data.iter()) {
            assert!((updated_val - new_val).abs() < 1e-6);
        }
    }

    #[test]
    fn test_get_specific_encodings() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = LearnedPositionalEncoding::<f32>::with_custom_init(config, 1.0).unwrap(); // Large init

        let positions = vec![0, 5, 10, 15];
        let encodings = pos_enc.get_encodings(&positions, 64).unwrap();

        assert_eq!(encodings.shape().dims(), &[4, 64]);

        // Check that different positions have different encodings
        let data = encodings.data();
        let pos0 = &data[0..64];
        let pos5 = &data[64..128];

        let mut different_count = 0;
        for (p0, p5) in pos0.iter().zip(pos5.iter()) {
            if (p0 - p5).abs() > 1e-2 { // More lenient
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Different positions should have different encodings");
    }

    #[test]
    fn test_input_validation() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = LearnedPositionalEncoding::<f32>::new(config).unwrap();
        
        // Wrong number of dimensions
        let embeddings_2d = CpuTensorFactory::ones(&Shape::new(vec![10, 64])).unwrap();
        let result = pos_enc.encode(&embeddings_2d, None);
        assert!(result.is_err());
        
        // Wrong hidden size
        let embeddings_wrong_hidden = create_test_embeddings(1, 5, 32);
        let result = pos_enc.encode(&embeddings_wrong_hidden, None);
        assert!(result.is_err());
        
        // Sequence too long
        let embeddings_too_long = create_test_embeddings(1, 150, 64);
        let result = pos_enc.encode(&embeddings_too_long, None);
        assert!(result.is_err());
    }

    #[test]
    fn test_update_validation() {
        let config = PositionalConfig::new(10, 8);
        let mut pos_enc = LearnedPositionalEncoding::<f32>::new(config).unwrap();
        
        // Wrong shape should fail
        let wrong_shape = CpuTensorFactory::ones(&Shape::new(vec![5, 8])).unwrap();
        let result = pos_enc.update_embeddings(wrong_shape);
        assert!(result.is_err());
    }
}
