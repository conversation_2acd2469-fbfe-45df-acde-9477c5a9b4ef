//! Sinusoidal positional encoding implementation.
//!
//! This module implements the original sinusoidal positional encoding from
//! "Attention Is All You Need" (<PERSON><PERSON><PERSON><PERSON> et al., 2017). This encoding uses
//! sine and cosine functions of different frequencies to encode position
//! information without requiring learned parameters.

use std::marker::PhantomData;
use crate::tensor::{<PERSON>sor, TensorOps, Shape, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::error::{AttentionError, ErrorContext};
use super::{PositionalEncoding, PositionalConfig, utils};

/// Sinusoidal positional encoding layer.
///
/// This encoding uses sine and cosine functions to create position-dependent
/// patterns that the model can learn to interpret as positional information.
/// The encoding is deterministic and doesn't require training.
///
/// # Formula
/// For position `pos` and dimension `i`:
/// - PE(pos, 2i) = sin(pos / 10000^(2i/d_model))
/// - PE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))
///
/// # Examples
///
/// ```rust
/// use qilin_inference::attention::positional::{SinusoidalPositionalEncoding, PositionalConfig};
/// use qilin_inference::tensor::{CpuTensor, Shape};
///
/// // Create sinusoidal positional encoding
/// let config = PositionalConfig::new(1024, 512);
/// let pos_enc = SinusoidalPositionalEncoding::new(config).unwrap();
///
/// // Apply to embeddings
/// let embeddings = CpuTensor::zeros(&Shape::new(vec![2, 10, 512])).unwrap();
/// let encoded = pos_enc.encode(&embeddings, None).unwrap();
/// assert_eq!(encoded.shape().dims(), &[2, 10, 512]);
/// ```
#[derive(Debug, Clone)]
pub struct SinusoidalPositionalEncoding<T: Numeric> {
    /// Configuration.
    config: PositionalConfig,
    /// Precomputed positional encodings.
    encodings: CpuTensor<T>,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> SinusoidalPositionalEncoding<T> {
    /// Create a new sinusoidal positional encoding layer.
    pub fn new(config: PositionalConfig) -> Result<Self, AttentionError> {
        // Validate configuration
        config.validate().map_err(|msg| AttentionError::InvalidConfiguration {
            message: msg,
            config_field: None,
            context: Some(ErrorContext::new("new", "attention::positional::sinusoidal")),
        })?;
        
        // Generate precomputed encodings
        let encodings = utils::generate_sinusoidal_encodings(
            config.max_seq_len,
            config.hidden_size,
        )?;
        
        Ok(Self {
            config,
            encodings,
            _phantom: PhantomData,
        })
    }
    
    /// Get the configuration.
    pub fn config(&self) -> &PositionalConfig {
        &self.config
    }
    
    /// Get the precomputed encodings tensor.
    pub fn encodings(&self) -> &CpuTensor<T> {
        &self.encodings
    }
    
    /// Extract positional encodings for specific sequence length.
    fn extract_encodings(
        &self,
        seq_len: usize,
        batch_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        if seq_len > self.config.max_seq_len {
            return Err(AttentionError::InvalidConfiguration {
                message: format!(
                    "Sequence length {} exceeds maximum {}",
                    seq_len, self.config.max_seq_len
                ),
                config_field: Some("seq_len".to_string()),
                context: Some(ErrorContext::new("extract_encodings", "attention::positional::sinusoidal")),
            });
        }
        
        // Extract the first seq_len positions
        let encoding_data = self.encodings.data();
        let hidden_size = self.config.hidden_size;
        
        // Create batch of encodings: [batch_size, seq_len, hidden_size]
        let mut batch_data = Vec::with_capacity(batch_size * seq_len * hidden_size);
        
        for _ in 0..batch_size {
            for pos in 0..seq_len {
                let start_idx = pos * hidden_size;
                let end_idx = start_idx + hidden_size;
                batch_data.extend_from_slice(&encoding_data[start_idx..end_idx]);
            }
        }
        
        let shape = Shape::new(vec![batch_size, seq_len, hidden_size]);
        CpuTensor::from_data(batch_data, shape).map_err(AttentionError::Tensor)
    }
    
    /// Extract positional encodings with offset for incremental generation.
    fn extract_encodings_with_offset(
        &self,
        seq_len: usize,
        batch_size: usize,
        offset: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let total_len = seq_len + offset;
        if total_len > self.config.max_seq_len {
            return Err(AttentionError::InvalidConfiguration {
                message: format!(
                    "Total sequence length {} (seq_len {} + offset {}) exceeds maximum {}",
                    total_len, seq_len, offset, self.config.max_seq_len
                ),
                config_field: Some("seq_len".to_string()),
                context: Some(ErrorContext::new("extract_encodings_with_offset", "attention::positional::sinusoidal")),
            });
        }
        
        let encoding_data = self.encodings.data();
        let hidden_size = self.config.hidden_size;
        
        // Create batch of encodings starting from offset
        let mut batch_data = Vec::with_capacity(batch_size * seq_len * hidden_size);
        
        for _ in 0..batch_size {
            for pos in offset..(offset + seq_len) {
                let start_idx = pos * hidden_size;
                let end_idx = start_idx + hidden_size;
                batch_data.extend_from_slice(&encoding_data[start_idx..end_idx]);
            }
        }
        
        let shape = Shape::new(vec![batch_size, seq_len, hidden_size]);
        CpuTensor::from_data(batch_data, shape).map_err(AttentionError::Tensor)
    }
    
    /// Validate input embeddings shape.
    fn validate_embeddings(&self, embeddings: &CpuTensor<T>) -> Result<(), AttentionError> {
        let shape = embeddings.shape().dims();
        
        if shape.len() != 3 {
            return Err(AttentionError::DimensionMismatch {
                expected: "3D tensor [batch_size, seq_len, hidden_size]".to_string(),
                actual: format!("{}D tensor", shape.len()),
                operation: "embeddings_validation".to_string(),
                context: Some(ErrorContext::new("validate_embeddings", "attention::positional::sinusoidal")),
            });
        }
        
        if shape[2] != self.config.hidden_size {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", self.config.hidden_size),
                actual: format!("hidden_size: {}", shape[2]),
                operation: "hidden_size_validation".to_string(),
                context: Some(ErrorContext::new("validate_embeddings", "attention::positional::sinusoidal")),
            });
        }
        
        Ok(())
    }
}

impl<T: Numeric> PositionalEncoding<T> for SinusoidalPositionalEncoding<T> {
    fn encode(
        &self,
        embeddings: &CpuTensor<T>,
        position_offset: Option<usize>,
    ) -> Result<CpuTensor<T>, AttentionError> {
        // Validate input
        self.validate_embeddings(embeddings)?;
        
        let shape = embeddings.shape().dims();
        let batch_size = shape[0];
        let seq_len = shape[1];
        
        // Scale embeddings if configured
        let scaled_embeddings = if self.config.scale_embeddings {
            utils::scale_embeddings(embeddings, self.config.effective_scale())?
        } else {
            embeddings.clone()
        };
        
        // Extract positional encodings
        let pos_encodings = if let Some(offset) = position_offset {
            self.extract_encodings_with_offset(seq_len, batch_size, offset)?
        } else {
            self.extract_encodings(seq_len, batch_size)?
        };
        
        // Add positional encodings to embeddings
        let result = scaled_embeddings.add(&pos_encodings).map_err(AttentionError::Tensor)?;
        
        // Apply dropout if configured
        if self.config.dropout > 0.0 {
            utils::apply_dropout(&result, self.config.dropout, true)
        } else {
            Ok(result)
        }
    }
    
    fn max_seq_len(&self) -> usize {
        self.config.max_seq_len
    }
    
    fn hidden_size(&self) -> usize {
        self.config.hidden_size
    }
    
    fn encoding_type(&self) -> &'static str {
        "sinusoidal"
    }
    
    fn supports_incremental(&self) -> bool {
        true
    }
    
    fn get_encodings(
        &self,
        positions: &[usize],
        hidden_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        if hidden_size != self.config.hidden_size {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", self.config.hidden_size),
                actual: format!("hidden_size: {}", hidden_size),
                operation: "get_encodings_validation".to_string(),
                context: Some(ErrorContext::new("get_encodings", "attention::positional::sinusoidal")),
            });
        }
        
        let encoding_data = self.encodings.data();
        let mut result_data = Vec::with_capacity(positions.len() * hidden_size);
        
        for &pos in positions {
            if pos >= self.config.max_seq_len {
                return Err(AttentionError::InvalidConfiguration {
                    message: format!("Position {} exceeds maximum {}", pos, self.config.max_seq_len),
                    config_field: Some("position".to_string()),
                    context: Some(ErrorContext::new("get_encodings", "attention::positional::sinusoidal")),
                });
            }
            
            let start_idx = pos * hidden_size;
            let end_idx = start_idx + hidden_size;
            result_data.extend_from_slice(&encoding_data[start_idx..end_idx]);
        }
        
        let shape = Shape::new(vec![positions.len(), hidden_size]);
        CpuTensor::from_data(result_data, shape).map_err(AttentionError::Tensor)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::cpu::CpuTensorFactory;
    use crate::tensor::TensorFactory;

    fn create_test_embeddings(batch_size: usize, seq_len: usize, hidden_size: usize) -> CpuTensor<f32> {
        CpuTensorFactory::ones(&Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap()
    }

    #[test]
    fn test_sinusoidal_creation() {
        let config = PositionalConfig::new(1024, 512);
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(config.clone()).unwrap();
        
        assert_eq!(pos_enc.max_seq_len(), 1024);
        assert_eq!(pos_enc.hidden_size(), 512);
        assert_eq!(pos_enc.encoding_type(), "sinusoidal");
        assert!(pos_enc.supports_incremental());
        assert_eq!(pos_enc.config(), &config);
    }

    #[test]
    fn test_sinusoidal_encoding() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(config).unwrap();
        
        let embeddings = create_test_embeddings(2, 10, 64);
        let encoded = pos_enc.encode(&embeddings, None).unwrap();
        
        assert_eq!(encoded.shape().dims(), &[2, 10, 64]);
        
        // Check that encoding is different from original embeddings
        let orig_data = embeddings.data();
        let encoded_data = encoded.data();
        let mut different_count = 0;
        for (orig, enc) in orig_data.iter().zip(encoded_data.iter()) {
            if (orig - enc).abs() > 1e-6 {
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Encoding should modify the embeddings");
    }

    #[test]
    fn test_sinusoidal_with_offset() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(config).unwrap();
        
        let embeddings = create_test_embeddings(1, 5, 64);
        
        // Encode without offset
        let encoded1 = pos_enc.encode(&embeddings, None).unwrap();
        
        // Encode with offset
        let encoded2 = pos_enc.encode(&embeddings, Some(3)).unwrap();
        
        // Results should be different due to different positions
        let data1 = encoded1.data();
        let data2 = encoded2.data();
        let mut different_count = 0;
        for (d1, d2) in data1.iter().zip(data2.iter()) {
            if (d1 - d2).abs() > 1e-6 {
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Different offsets should produce different encodings");
    }

    #[test]
    fn test_sinusoidal_with_scaling() {
        let config = PositionalConfig::new(100, 64).with_embedding_scaling();
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(config).unwrap();
        
        let embeddings = create_test_embeddings(1, 5, 64);
        let encoded = pos_enc.encode(&embeddings, None).unwrap();
        
        assert_eq!(encoded.shape().dims(), &[1, 5, 64]);
        
        // With scaling, the magnitude should be larger
        let orig_sum: f32 = embeddings.data().iter().sum();
        let encoded_sum: f32 = encoded.data().iter().sum();
        assert!(encoded_sum.abs() > orig_sum.abs());
    }

    #[test]
    fn test_get_specific_encodings() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(config).unwrap();
        
        let positions = vec![0, 5, 10, 15];
        let encodings = pos_enc.get_encodings(&positions, 64).unwrap();
        
        assert_eq!(encodings.shape().dims(), &[4, 64]);
        
        // Check that different positions have different encodings
        let data = encodings.data();
        let pos0 = &data[0..64];
        let pos5 = &data[64..128];
        
        let mut different_count = 0;
        for (p0, p5) in pos0.iter().zip(pos5.iter()) {
            if (p0 - p5).abs() > 1e-6 {
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Different positions should have different encodings");
    }

    #[test]
    fn test_input_validation() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(config).unwrap();
        
        // Wrong number of dimensions
        let embeddings_2d = CpuTensorFactory::ones(&Shape::new(vec![10, 64])).unwrap();
        let result = pos_enc.encode(&embeddings_2d, None);
        assert!(result.is_err());
        
        // Wrong hidden size
        let embeddings_wrong_hidden = create_test_embeddings(1, 5, 32);
        let result = pos_enc.encode(&embeddings_wrong_hidden, None);
        assert!(result.is_err());
        
        // Sequence too long
        let embeddings_too_long = create_test_embeddings(1, 150, 64);
        let result = pos_enc.encode(&embeddings_too_long, None);
        assert!(result.is_err());
    }

    #[test]
    fn test_invalid_config() {
        // Odd hidden size should fail
        let config = PositionalConfig::new(100, 63);
        let result = SinusoidalPositionalEncoding::<f32>::new(config);
        assert!(result.is_err());
    }

    #[test]
    fn test_encoding_properties() {
        let config = PositionalConfig::new(100, 8);
        let pos_enc = SinusoidalPositionalEncoding::<f32>::new(config).unwrap();
        
        // Get encodings for positions 0 and 1
        let pos0_enc = pos_enc.get_encodings(&[0], 8).unwrap();
        let pos1_enc = pos_enc.get_encodings(&[1], 8).unwrap();
        
        let pos0_data = pos0_enc.data();
        let pos1_data = pos1_enc.data();
        
        // Check that even indices use sin, odd indices use cos pattern
        // This is a basic sanity check for the sinusoidal pattern
        assert_eq!(pos0_data.len(), 8);
        assert_eq!(pos1_data.len(), 8);
        
        // Position 0 should have specific values
        assert!((pos0_data[0] - 0.0).abs() < 1e-6); // sin(0) = 0
        assert!((pos0_data[1] - 1.0).abs() < 1e-6); // cos(0) = 1
    }
}
