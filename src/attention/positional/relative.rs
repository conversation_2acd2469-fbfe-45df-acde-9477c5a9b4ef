//! Relative positional encoding implementation.
//!
//! This module implements relative positional encoding as used in models like
//! Transformer-XL and T5. Instead of encoding absolute positions, it encodes
//! the relative distance between positions, which can be more effective for
//! certain tasks and allows for better length generalization.

use std::marker::PhantomData;
use crate::tensor::{Tensor, <PERSON>sorO<PERSON>, <PERSON>hape, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::tensor::cpu::CpuTensorFactory;
use crate::tensor::TensorFactory;
use crate::error::{AttentionError, ErrorContext};
use super::{PositionalEncoding, PositionalConfig, utils};

/// Relative positional encoding layer.
///
/// This encoding represents the relative distance between positions rather
/// than absolute positions. It's particularly useful for tasks where the
/// relative ordering matters more than absolute position.
///
/// # Examples
///
/// ```rust
/// use qilin_inference::attention::positional::{RelativePositionalEncoding, PositionalConfig};
/// use qilin_inference::tensor::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};
///
/// // Create relative positional encoding
/// let config = PositionalConfig::new(1024, 512);
/// let pos_enc = RelativePositionalEncoding::new(config, 64).unwrap();
///
/// // Apply to embeddings
/// let embeddings = CpuTensor::zeros(&Shape::new(vec![2, 10, 512])).unwrap();
/// let encoded = pos_enc.encode(&embeddings, None).unwrap();
/// assert_eq!(encoded.shape().dims(), &[2, 10, 512]);
/// ```
#[derive(Debug, Clone)]
pub struct RelativePositionalEncoding<T: Numeric> {
    /// Configuration.
    config: PositionalConfig,
    /// Maximum relative distance to consider.
    max_relative_distance: usize,
    /// Relative position embeddings: [2 * max_relative_distance + 1, hidden_size].
    relative_embeddings: CpuTensor<T>,
    /// Phantom data for type parameter.
    _phantom: PhantomData<T>,
}

impl<T: Numeric> RelativePositionalEncoding<T> {
    /// Create a new relative positional encoding layer.
    ///
    /// # Arguments
    /// * `config` - Positional encoding configuration
    /// * `max_relative_distance` - Maximum relative distance to encode
    pub fn new(
        config: PositionalConfig,
        max_relative_distance: usize,
    ) -> Result<Self, AttentionError> {
        // Validate configuration
        config.validate().map_err(|msg| AttentionError::InvalidConfiguration {
            message: msg,
            config_field: None,
            context: Some(ErrorContext::new("new", "attention::positional::relative")),
        })?;
        
        if max_relative_distance == 0 {
            return Err(AttentionError::InvalidConfiguration {
                message: "max_relative_distance must be greater than 0".to_string(),
                config_field: Some("max_relative_distance".to_string()),
                context: Some(ErrorContext::new("new", "attention::positional::relative")),
            });
        }
        
        // Create relative embeddings
        // We need embeddings for distances from -max_relative_distance to +max_relative_distance
        let num_embeddings = 2 * max_relative_distance + 1;
        let relative_embeddings_f32 = CpuTensorFactory::randn(
            &Shape::new(vec![num_embeddings, config.hidden_size]),
            0.0,
            0.02,
        ).map_err(AttentionError::Tensor)?;

        let relative_embeddings = Self::convert_tensor_type(relative_embeddings_f32)?;
        
        Ok(Self {
            config,
            max_relative_distance,
            relative_embeddings,
            _phantom: PhantomData,
        })
    }
    
    /// Create relative positional encoding with custom initialization.
    pub fn with_custom_init(
        config: PositionalConfig,
        max_relative_distance: usize,
        init_std: f32,
    ) -> Result<Self, AttentionError> {
        config.validate().map_err(|msg| AttentionError::InvalidConfiguration {
            message: msg,
            config_field: None,
            context: Some(ErrorContext::new("with_custom_init", "attention::positional::relative")),
        })?;
        
        if max_relative_distance == 0 {
            return Err(AttentionError::InvalidConfiguration {
                message: "max_relative_distance must be greater than 0".to_string(),
                config_field: Some("max_relative_distance".to_string()),
                context: Some(ErrorContext::new("with_custom_init", "attention::positional::relative")),
            });
        }
        
        let num_embeddings = 2 * max_relative_distance + 1;
        let relative_embeddings_f32 = CpuTensorFactory::randn(
            &Shape::new(vec![num_embeddings, config.hidden_size]),
            0.0,
            init_std,
        ).map_err(AttentionError::Tensor)?;

        let relative_embeddings = Self::convert_tensor_type(relative_embeddings_f32)?;
        
        Ok(Self {
            config,
            max_relative_distance,
            relative_embeddings,
            _phantom: PhantomData,
        })
    }
    
    /// Get the configuration.
    pub fn config(&self) -> &PositionalConfig {
        &self.config
    }
    
    /// Get the maximum relative distance.
    pub fn max_relative_distance(&self) -> usize {
        self.max_relative_distance
    }
    
    /// Get the relative embeddings tensor.
    pub fn relative_embeddings(&self) -> &CpuTensor<T> {
        &self.relative_embeddings
    }
    
    /// Get mutable reference to relative embeddings for training.
    pub fn relative_embeddings_mut(&mut self) -> &mut CpuTensor<T> {
        &mut self.relative_embeddings
    }
    
    /// Convert relative distance to embedding index.
    fn relative_distance_to_index(&self, relative_distance: i32) -> usize {
        // Clamp the relative distance to the maximum range
        let clamped_distance = relative_distance.clamp(
            -(self.max_relative_distance as i32),
            self.max_relative_distance as i32,
        );
        
        // Convert to index: distance of -max_relative_distance maps to index 0,
        // distance of 0 maps to index max_relative_distance,
        // distance of +max_relative_distance maps to index 2*max_relative_distance
        (clamped_distance + self.max_relative_distance as i32) as usize
    }
    
    /// Generate relative position matrix for a sequence.
    fn generate_relative_positions(&self, seq_len: usize) -> Vec<Vec<usize>> {
        let mut relative_positions = vec![vec![0; seq_len]; seq_len];
        
        for i in 0..seq_len {
            for j in 0..seq_len {
                let relative_distance = j as i32 - i as i32;
                relative_positions[i][j] = self.relative_distance_to_index(relative_distance);
            }
        }
        
        relative_positions
    }
    
    /// Extract relative positional encodings for a sequence.
    fn extract_relative_encodings(
        &self,
        seq_len: usize,
        batch_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        let relative_positions = self.generate_relative_positions(seq_len);
        let embedding_data = self.relative_embeddings.data();
        let hidden_size = self.config.hidden_size;
        
        // Create tensor: [batch_size, seq_len, seq_len, hidden_size]
        // This represents the relative encoding between each pair of positions
        let mut result_data = Vec::with_capacity(batch_size * seq_len * seq_len * hidden_size);
        
        for _ in 0..batch_size {
            for i in 0..seq_len {
                for j in 0..seq_len {
                    let embedding_idx = relative_positions[i][j];
                    let start_idx = embedding_idx * hidden_size;
                    let end_idx = start_idx + hidden_size;
                    result_data.extend_from_slice(&embedding_data[start_idx..end_idx]);
                }
            }
        }
        
        let shape = Shape::new(vec![batch_size, seq_len, seq_len, hidden_size]);
        CpuTensor::from_data(result_data, shape).map_err(AttentionError::Tensor)
    }
    
    /// Convert f32 tensor to target type.
    fn convert_tensor_type(tensor_f32: CpuTensor<f32>) -> Result<CpuTensor<T>, AttentionError> {
        let data_f32 = tensor_f32.data();
        let converted_data: Vec<T> = data_f32.iter().map(|&x| T::from_f32(x)).collect();
        CpuTensor::from_data(converted_data, tensor_f32.shape().clone()).map_err(AttentionError::Tensor)
    }

    /// Validate input embeddings shape.
    fn validate_embeddings(&self, embeddings: &CpuTensor<T>) -> Result<(), AttentionError> {
        let shape = embeddings.shape().dims();
        
        if shape.len() != 3 {
            return Err(AttentionError::DimensionMismatch {
                expected: "3D tensor [batch_size, seq_len, hidden_size]".to_string(),
                actual: format!("{}D tensor", shape.len()),
                operation: "embeddings_validation".to_string(),
                context: Some(ErrorContext::new("validate_embeddings", "attention::positional::relative")),
            });
        }
        
        if shape[2] != self.config.hidden_size {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", self.config.hidden_size),
                actual: format!("hidden_size: {}", shape[2]),
                operation: "hidden_size_validation".to_string(),
                context: Some(ErrorContext::new("validate_embeddings", "attention::positional::relative")),
            });
        }
        
        Ok(())
    }
}

impl<T: Numeric> PositionalEncoding<T> for RelativePositionalEncoding<T> {
    fn encode(
        &self,
        embeddings: &CpuTensor<T>,
        _position_offset: Option<usize>, // Not used in relative encoding
    ) -> Result<CpuTensor<T>, AttentionError> {
        // Validate input
        self.validate_embeddings(embeddings)?;
        
        // For relative positional encoding, we typically don't add to embeddings directly
        // Instead, the relative encodings are used in the attention computation
        // For now, we'll return the scaled embeddings
        
        if self.config.scale_embeddings {
            utils::scale_embeddings(embeddings, self.config.effective_scale())
        } else {
            Ok(embeddings.clone())
        }
    }
    
    fn max_seq_len(&self) -> usize {
        self.config.max_seq_len
    }
    
    fn hidden_size(&self) -> usize {
        self.config.hidden_size
    }
    
    fn encoding_type(&self) -> &'static str {
        "relative"
    }
    
    fn supports_incremental(&self) -> bool {
        false // Relative encoding is more complex for incremental generation
    }
    
    fn get_encodings(
        &self,
        positions: &[usize],
        hidden_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        if hidden_size != self.config.hidden_size {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("hidden_size: {}", self.config.hidden_size),
                actual: format!("hidden_size: {}", hidden_size),
                operation: "get_encodings_validation".to_string(),
                context: Some(ErrorContext::new("get_encodings", "attention::positional::relative")),
            });
        }
        
        // For relative encoding, we return the relative embeddings for the given positions
        // This is a simplified version - in practice, you'd want the relative encodings
        // between all pairs of positions
        let embedding_data = self.relative_embeddings.data();
        let mut result_data = Vec::with_capacity(positions.len() * hidden_size);
        
        for &pos in positions {
            // Use position as relative distance (simplified)
            let relative_distance = pos as i32;
            let embedding_idx = self.relative_distance_to_index(relative_distance);
            
            let start_idx = embedding_idx * hidden_size;
            let end_idx = start_idx + hidden_size;
            result_data.extend_from_slice(&embedding_data[start_idx..end_idx]);
        }
        
        let shape = Shape::new(vec![positions.len(), hidden_size]);
        CpuTensor::from_data(result_data, shape).map_err(AttentionError::Tensor)
    }
}

impl<T: Numeric> RelativePositionalEncoding<T> {
    /// Get relative positional encodings for attention computation.
    ///
    /// This method returns the relative encodings between all pairs of positions
    /// in a sequence, which can be used in attention mechanisms.
    pub fn get_relative_encodings_for_attention(
        &self,
        seq_len: usize,
        batch_size: usize,
    ) -> Result<CpuTensor<T>, AttentionError> {
        if seq_len > self.config.max_seq_len {
            return Err(AttentionError::InvalidConfiguration {
                message: format!(
                    "Sequence length {} exceeds maximum {}",
                    seq_len, self.config.max_seq_len
                ),
                config_field: Some("seq_len".to_string()),
                context: Some(ErrorContext::new("get_relative_encodings_for_attention", "attention::positional::relative")),
            });
        }
        
        self.extract_relative_encodings(seq_len, batch_size)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::cpu::CpuTensorFactory;
    use crate::tensor::TensorFactory;

    fn create_test_embeddings(batch_size: usize, seq_len: usize, hidden_size: usize) -> CpuTensor<f32> {
        CpuTensorFactory::ones(&Shape::new(vec![batch_size, seq_len, hidden_size])).unwrap()
    }

    #[test]
    fn test_relative_creation() {
        let config = PositionalConfig::new(1024, 512);
        let pos_enc = RelativePositionalEncoding::<f32>::new(config.clone(), 64).unwrap();
        
        assert_eq!(pos_enc.max_seq_len(), 1024);
        assert_eq!(pos_enc.hidden_size(), 512);
        assert_eq!(pos_enc.encoding_type(), "relative");
        assert!(!pos_enc.supports_incremental());
        assert_eq!(pos_enc.config(), &config);
        assert_eq!(pos_enc.max_relative_distance(), 64);
        
        // Check that relative embeddings are initialized
        let embeddings = pos_enc.relative_embeddings();
        assert_eq!(embeddings.shape().dims(), &[129, 512]); // 2*64+1 = 129
    }

    #[test]
    fn test_relative_distance_to_index() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = RelativePositionalEncoding::<f32>::new(config, 10).unwrap();
        
        // Test various relative distances
        assert_eq!(pos_enc.relative_distance_to_index(-10), 0);   // -max_distance
        assert_eq!(pos_enc.relative_distance_to_index(0), 10);   // zero distance
        assert_eq!(pos_enc.relative_distance_to_index(10), 20);  // +max_distance
        
        // Test clamping
        assert_eq!(pos_enc.relative_distance_to_index(-15), 0);  // Clamped to -10
        assert_eq!(pos_enc.relative_distance_to_index(15), 20);  // Clamped to +10
    }

    #[test]
    fn test_relative_encoding() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = RelativePositionalEncoding::<f32>::new(config, 10).unwrap();
        
        let embeddings = create_test_embeddings(2, 5, 64);
        let encoded = pos_enc.encode(&embeddings, None).unwrap();
        
        // For relative encoding, the output should be the same as input (or scaled)
        assert_eq!(encoded.shape().dims(), &[2, 5, 64]);
    }

    #[test]
    fn test_relative_encodings_for_attention() {
        let config = PositionalConfig::new(100, 8);
        let pos_enc = RelativePositionalEncoding::<f32>::with_custom_init(config, 5, 1.0).unwrap(); // Large init

        let relative_encodings = pos_enc.get_relative_encodings_for_attention(3, 1).unwrap();

        // Should have shape [batch_size, seq_len, seq_len, hidden_size]
        assert_eq!(relative_encodings.shape().dims(), &[1, 3, 3, 8]);

        // Check that different position pairs have different encodings
        let data = relative_encodings.data();
        let pos_00 = &data[0..8];   // Position (0,0)
        let pos_01 = &data[8..16];  // Position (0,1)

        let mut different_count = 0;
        for (p00, p01) in pos_00.iter().zip(pos_01.iter()) {
            if (p00 - p01).abs() > 1e-2 { // More lenient
                different_count += 1;
            }
        }
        assert!(different_count > 0, "Different position pairs should have different encodings");
    }

    #[test]
    fn test_get_specific_encodings() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = RelativePositionalEncoding::<f32>::new(config, 10).unwrap();
        
        let positions = vec![0, 3, 7];
        let encodings = pos_enc.get_encodings(&positions, 64).unwrap();
        
        assert_eq!(encodings.shape().dims(), &[3, 64]);
    }

    #[test]
    fn test_custom_initialization() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = RelativePositionalEncoding::<f32>::with_custom_init(config, 10, 0.5).unwrap(); // Larger init

        let embeddings = pos_enc.relative_embeddings();
        assert_eq!(embeddings.shape().dims(), &[21, 64]); // 2*10+1 = 21

        // Check that values are not all zeros
        let data = embeddings.data();
        let non_zero_count = data.iter().filter(|&&x| x.abs() > 1e-3).count(); // More lenient
        assert!(non_zero_count > 0, "Should have non-zero initialized values");
    }

    #[test]
    fn test_input_validation() {
        let config = PositionalConfig::new(100, 64);
        let pos_enc = RelativePositionalEncoding::<f32>::new(config, 10).unwrap();
        
        // Wrong number of dimensions
        let embeddings_2d = CpuTensorFactory::ones(&Shape::new(vec![10, 64])).unwrap();
        let result = pos_enc.encode(&embeddings_2d, None);
        assert!(result.is_err());
        
        // Wrong hidden size
        let embeddings_wrong_hidden = create_test_embeddings(1, 5, 32);
        let result = pos_enc.encode(&embeddings_wrong_hidden, None);
        assert!(result.is_err());
    }

    #[test]
    fn test_invalid_config() {
        let config = PositionalConfig::new(100, 64);
        
        // Zero max_relative_distance should fail
        let result = RelativePositionalEncoding::<f32>::new(config, 0);
        assert!(result.is_err());
    }

    #[test]
    fn test_relative_position_matrix() {
        let config = PositionalConfig::new(100, 8);
        let pos_enc = RelativePositionalEncoding::<f32>::new(config, 3).unwrap();
        
        let relative_positions = pos_enc.generate_relative_positions(4);
        
        // Check the relative position matrix
        assert_eq!(relative_positions.len(), 4);
        assert_eq!(relative_positions[0].len(), 4);
        
        // Position (0,0) should have relative distance 0 -> index 3
        assert_eq!(relative_positions[0][0], 3);
        
        // Position (0,1) should have relative distance 1 -> index 4
        assert_eq!(relative_positions[0][1], 4);
        
        // Position (1,0) should have relative distance -1 -> index 2
        assert_eq!(relative_positions[1][0], 2);
    }
}
