//! Scaled dot-product attention implementation.
//!
//! This module implements the core scaled dot-product attention mechanism:
//! Attention(Q, K, V) = softmax(QK^T / √d_k)V
//!
//! Features:
//! - Numerically stable softmax computation
//! - Support for various mask types (causal, padding, custom)
//! - Memory-efficient computation
//! - Batch processing support

use crate::tensor::{Tensor, TensorOps, Shape, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::tensor::reduction::ReductionOps;
use crate::error::{AttentionError, ErrorContext};
use super::{Attention, AttentionConfig};

/// Scaled dot-product attention implementation.
#[derive(Debug, Clone)]
pub struct ScaledDotProductAttention<T: Numeric> {
    /// Scale factor for attention scores (typically 1/√d_k).
    scale: T,
    /// Dropout probability for attention weights.
    dropout: f32,
    /// Whether to use causal masking.
    causal: bool,
}

impl<T: Numeric> ScaledDotProductAttention<T> {
    /// Create a new scaled dot-product attention layer.
    ///
    /// # Arguments
    /// * `head_dim` - Dimension of each attention head
    /// * `dropout` - Dropout probability for attention weights
    /// * `causal` - Whether to apply causal masking
    pub fn new(head_dim: usize, dropout: f32, causal: bool) -> Result<Self, AttentionError> {
        if dropout < 0.0 || dropout > 1.0 {
            return Err(AttentionError::InvalidConfiguration {
                message: format!("dropout must be between 0.0 and 1.0, got {}", dropout),
                config_field: Some("dropout".to_string()),
                context: Some(ErrorContext::new("new", "attention::scaled_dot_product")),
            });
        }

        let scale = T::ONE / T::from_f32((head_dim as f32).sqrt());

        Ok(Self {
            scale,
            dropout,
            causal,
        })
    }

    /// Create from attention configuration.
    pub fn from_config(config: &AttentionConfig, causal: bool) -> Result<Self, AttentionError> {
        config.validate().map_err(|msg| AttentionError::InvalidConfiguration {
            message: msg,
            config_field: None,
            context: Some(ErrorContext::new("from_config", "attention::scaled_dot_product")),
        })?;

        Self::new(config.head_dim(), config.dropout, causal)
    }

    /// Apply causal mask to attention scores.
    fn apply_causal_mask(&self, scores: &CpuTensor<T>, seq_len: usize) -> Result<CpuTensor<T>, AttentionError> {
        if !self.causal {
            return Ok(scores.clone());
        }

        let scores_data = scores.data();
        let scores_shape = scores.shape().dims();

        // Create new data with causal mask applied
        let mut new_data = scores_data.to_vec();
        let neg_inf = T::NEG_INFINITY;

        // Handle different tensor shapes
        match scores_shape.len() {
            3 => {
                // Shape: [batch_size, seq_len_q, seq_len_k]
                let batch_size = scores_shape[0];
                let seq_len_q = scores_shape[1];
                let seq_len_k = scores_shape[2];

                for batch_idx in 0..batch_size {
                    for i in 0..seq_len_q {
                        for j in (i + 1)..seq_len_k.min(seq_len) {
                            let idx = batch_idx * (seq_len_q * seq_len_k) + i * seq_len_k + j;
                            if idx < new_data.len() {
                                new_data[idx] = neg_inf;
                            }
                        }
                    }
                }
            }
            4 => {
                // Shape: [batch_size, num_heads, seq_len_q, seq_len_k]
                let batch_size = scores_shape[0];
                let num_heads = scores_shape[1];
                let seq_len_q = scores_shape[2];
                let seq_len_k = scores_shape[3];

                for batch_idx in 0..batch_size {
                    for head_idx in 0..num_heads {
                        for i in 0..seq_len_q {
                            for j in (i + 1)..seq_len_k.min(seq_len) {
                                let idx = batch_idx * (num_heads * seq_len_q * seq_len_k) +
                                         head_idx * (seq_len_q * seq_len_k) +
                                         i * seq_len_k + j;
                                if idx < new_data.len() {
                                    new_data[idx] = neg_inf;
                                }
                            }
                        }
                    }
                }
            }
            _ => {
                return Err(AttentionError::DimensionMismatch {
                    expected: "3D or 4D tensor".to_string(),
                    actual: format!("{}D tensor", scores_shape.len()),
                    operation: "causal_mask".to_string(),
                    context: Some(ErrorContext::new("apply_causal_mask", "attention::scaled_dot_product")),
                });
            }
        }

        CpuTensor::from_data(new_data, scores.shape().clone()).map_err(AttentionError::Tensor)
    }

    /// Apply custom mask to attention scores.
    fn apply_custom_mask(&self, scores: &CpuTensor<T>, mask: &CpuTensor<T>) -> Result<CpuTensor<T>, AttentionError> {
        // Validate mask shape
        let scores_shape = scores.shape().dims();
        let mask_shape = mask.shape().dims();

        // Mask can be broadcastable to scores shape
        if !self.is_broadcastable(mask_shape, scores_shape) {
            return Err(AttentionError::InvalidMask {
                reason: "mask shape is not broadcastable to scores shape".to_string(),
                mask_shape: Some(mask_shape.to_vec()),
                expected_shape: Some(scores_shape.to_vec()),
                context: Some(ErrorContext::new("apply_custom_mask", "attention::scaled_dot_product")),
            });
        }

        // Apply mask: where mask is 0, set scores to -inf
        let scores_data = scores.data();
        let mask_data = mask.data();
        let neg_inf = T::NEG_INFINITY;

        // Create new data with mask applied
        let mut new_data = scores_data.to_vec();

        // Simple case: same shape
        if mask_shape == scores_shape {
            for (score, &mask_val) in new_data.iter_mut().zip(mask_data.iter()) {
                if mask_val == T::ZERO {
                    *score = neg_inf;
                }
            }
        } else {
            // TODO: Implement proper broadcasting for mask application
            return Err(AttentionError::InvalidMask {
                reason: "broadcasting not yet implemented for masks".to_string(),
                mask_shape: Some(mask_shape.to_vec()),
                expected_shape: Some(scores_shape.to_vec()),
                context: Some(ErrorContext::new("apply_custom_mask", "attention::scaled_dot_product")),
            });
        }

        CpuTensor::from_data(new_data, scores.shape().clone()).map_err(AttentionError::Tensor)
    }

    /// Check if two shapes are broadcastable.
    fn is_broadcastable(&self, shape1: &[usize], shape2: &[usize]) -> bool {
        let len1 = shape1.len();
        let len2 = shape2.len();
        let max_len = len1.max(len2);

        for i in 0..max_len {
            let dim1 = if i < len1 { shape1[len1 - 1 - i] } else { 1 };
            let dim2 = if i < len2 { shape2[len2 - 1 - i] } else { 1 };

            if dim1 != dim2 && dim1 != 1 && dim2 != 1 {
                return false;
            }
        }

        true
    }
}

impl<T: Numeric> Attention<T> for ScaledDotProductAttention<T> {
    type Tensor = CpuTensor<T>;
    type Error = AttentionError;

    fn compute_attention(
        &self,
        query: &Self::Tensor,
        key: &Self::Tensor,
        value: &Self::Tensor,
        mask: Option<&Self::Tensor>,
    ) -> Result<(Self::Tensor, Self::Tensor), Self::Error> {
        // Validate input shapes
        self.validate_input_shapes(query, key, value)?;

        let query_shape = query.shape().dims();
        let batch_size = query_shape[0];
        let seq_len_q = query_shape[1];
        let seq_len_k = key.shape().dims()[1];
        let head_dim = query_shape[2];

        // Compute attention scores: Q @ K^T
        let key_transposed = key.transpose(1, 2).map_err(AttentionError::Tensor)?;
        let mut scores = query.matmul(&key_transposed).map_err(AttentionError::Tensor)?;

        // Scale scores
        scores = scores.mul_scalar(self.scale).map_err(AttentionError::Tensor)?;

        // Apply causal mask if enabled
        if self.causal {
            scores = self.apply_causal_mask(&scores, seq_len_q)?;
        }

        // Apply custom mask if provided
        if let Some(mask) = mask {
            scores = self.apply_custom_mask(&scores, mask)?;
        }

        // Apply softmax to get attention weights
        let attention_weights = self.stable_softmax(&scores, -1)?;

        // Apply dropout if training (for now, skip dropout implementation)
        // TODO: Implement dropout for attention weights

        // Compute output: attention_weights @ V
        let output = attention_weights.matmul(value).map_err(AttentionError::Tensor)?;

        Ok((output, attention_weights))
    }

    fn num_heads(&self) -> usize {
        1 // This is single-head attention; multi-head will wrap this
    }

    fn head_dim(&self) -> usize {
        // This will be determined by the input tensor dimensions
        64 // Default value, should be overridden by actual usage
    }

    fn scale_factor(&self) -> T {
        self.scale
    }
}

impl<T: Numeric> ScaledDotProductAttention<T> {
    /// Validate input tensor shapes for attention computation.
    fn validate_input_shapes(
        &self,
        query: &CpuTensor<T>,
        key: &CpuTensor<T>,
        value: &CpuTensor<T>,
    ) -> Result<(), AttentionError> {
        let q_shape = query.shape().dims();
        let k_shape = key.shape().dims();
        let v_shape = value.shape().dims();

        // All tensors should be 3D: [batch_size, seq_len, head_dim]
        if q_shape.len() != 3 || k_shape.len() != 3 || v_shape.len() != 3 {
            return Err(AttentionError::DimensionMismatch {
                expected: "3D tensors [batch_size, seq_len, head_dim]".to_string(),
                actual: format!("Q: {:?}, K: {:?}, V: {:?}", q_shape, k_shape, v_shape),
                operation: "attention_input_validation".to_string(),
                context: Some(ErrorContext::new("validate_input_shapes", "attention::scaled_dot_product")),
            });
        }

        // Batch sizes must match
        if q_shape[0] != k_shape[0] || q_shape[0] != v_shape[0] {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("batch_size: {}", q_shape[0]),
                actual: format!("Q: {}, K: {}, V: {}", q_shape[0], k_shape[0], v_shape[0]),
                operation: "batch_size_validation".to_string(),
                context: Some(ErrorContext::new("validate_input_shapes", "attention::scaled_dot_product")),
            });
        }

        // Head dimensions must match for Q and K
        if q_shape[2] != k_shape[2] {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("head_dim: {}", q_shape[2]),
                actual: format!("Q: {}, K: {}", q_shape[2], k_shape[2]),
                operation: "head_dim_validation".to_string(),
                context: Some(ErrorContext::new("validate_input_shapes", "attention::scaled_dot_product")),
            });
        }

        // Key and Value sequence lengths must match
        if k_shape[1] != v_shape[1] {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("seq_len: {}", k_shape[1]),
                actual: format!("K: {}, V: {}", k_shape[1], v_shape[1]),
                operation: "seq_len_validation".to_string(),
                context: Some(ErrorContext::new("validate_input_shapes", "attention::scaled_dot_product")),
            });
        }

        Ok(())
    }

    /// Numerically stable softmax computation.
    fn stable_softmax(&self, input: &CpuTensor<T>, dim: i32) -> Result<CpuTensor<T>, AttentionError> {
        // Convert negative dimension to positive
        let rank = input.shape().rank();
        let dim = if dim < 0 {
            (rank as i32 + dim) as usize
        } else {
            dim as usize
        };

        if dim >= rank {
            return Err(AttentionError::DimensionMismatch {
                expected: format!("dim < {}", rank),
                actual: format!("dim = {}", dim),
                operation: "softmax_dimension_check".to_string(),
                context: Some(ErrorContext::new("stable_softmax", "attention::scaled_dot_product")),
            });
        }

        // Implement a simple softmax for the last dimension only
        if dim != rank - 1 {
            return Err(AttentionError::DimensionMismatch {
                expected: "last dimension".to_string(),
                actual: format!("dimension {}", dim),
                operation: "softmax_dimension_limitation".to_string(),
                context: Some(ErrorContext::new("stable_softmax", "attention::scaled_dot_product")),
            });
        }

        let input_data = input.data();
        let input_shape = input.shape().dims();
        let mut result_data = vec![T::ZERO; input_data.len()];

        // For 3D tensor: [batch, seq_q, seq_k]
        if rank == 3 {
            let batch_size = input_shape[0];
            let seq_q = input_shape[1];
            let seq_k = input_shape[2];

            for batch_idx in 0..batch_size {
                for q_idx in 0..seq_q {
                    let start_idx = batch_idx * seq_q * seq_k + q_idx * seq_k;
                    let end_idx = start_idx + seq_k;

                    // Find max for numerical stability
                    let mut max_val = input_data[start_idx];
                    for i in start_idx..end_idx {
                        max_val = max_val.max(input_data[i]);
                    }

                    // Compute exp(x - max) and sum
                    let mut sum = T::ZERO;
                    for i in start_idx..end_idx {
                        let exp_val = (input_data[i] - max_val).exp();
                        result_data[i] = exp_val;
                        sum = sum + exp_val;
                    }

                    // Normalize
                    for i in start_idx..end_idx {
                        result_data[i] = result_data[i] / sum;
                    }
                }
            }
        } else {
            return Err(AttentionError::DimensionMismatch {
                expected: "3D tensor".to_string(),
                actual: format!("{}D tensor", rank),
                operation: "softmax_shape_limitation".to_string(),
                context: Some(ErrorContext::new("stable_softmax", "attention::scaled_dot_product")),
            });
        }

        CpuTensor::from_data(result_data, input.shape().clone()).map_err(AttentionError::Tensor)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tensor::Shape;

    #[test]
    fn test_scaled_dot_product_attention_creation() {
        let attention = ScaledDotProductAttention::<f32>::new(64, 0.1, false).unwrap();
        assert_eq!(attention.num_heads(), 1);
        assert!((attention.scale_factor() - (1.0 / 8.0)).abs() < 1e-6); // 1/sqrt(64) = 1/8
    }

    #[test]
    fn test_attention_config_creation() {
        let config = AttentionConfig::new(512, 8);
        let attention = ScaledDotProductAttention::<f32>::from_config(&config, true).unwrap();
        assert_eq!(attention.causal, true);
    }

    #[test]
    fn test_invalid_dropout() {
        let result = ScaledDotProductAttention::<f32>::new(64, 1.5, false);
        assert!(result.is_err());

        let result = ScaledDotProductAttention::<f32>::new(64, -0.1, false);
        assert!(result.is_err());
    }

    #[test]
    fn test_basic_attention_computation() {
        let attention = ScaledDotProductAttention::<f32>::new(4, 0.0, false).unwrap();

        // Create simple test tensors: [batch_size=1, seq_len=2, head_dim=4]
        let query_data = vec![1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0];
        let key_data = vec![1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0];
        let value_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];

        let query = CpuTensor::from_data(query_data, Shape::new(vec![1, 2, 4])).unwrap();
        let key = CpuTensor::from_data(key_data, Shape::new(vec![1, 2, 4])).unwrap();
        let value = CpuTensor::from_data(value_data, Shape::new(vec![1, 2, 4])).unwrap();

        let result = attention.compute_attention(&query, &key, &value, None);
        if let Err(ref e) = result {
            println!("Error: {:?}", e);
        }
        assert!(result.is_ok());

        let (output, attention_weights) = result.unwrap();

        // Check output shape: [batch_size=1, seq_len=2, head_dim=4]
        assert_eq!(output.shape().dims(), &[1, 2, 4]);

        // Check attention weights shape: [batch_size=1, seq_len=2, seq_len=2]
        assert_eq!(attention_weights.shape().dims(), &[1, 2, 2]);

        // Attention weights should sum to 1 along the last dimension
        let weights_data = attention_weights.data();
        let row1_sum = weights_data[0] + weights_data[1];
        let row2_sum = weights_data[2] + weights_data[3];
        assert!((row1_sum - 1.0).abs() < 1e-6);
        assert!((row2_sum - 1.0).abs() < 1e-6);
    }

    #[test]
    fn test_causal_attention() {
        let attention = ScaledDotProductAttention::<f32>::new(4, 0.0, true).unwrap();

        // Create test tensors
        let query_data = vec![1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0];
        let key_data = vec![1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0];
        let value_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];

        let query = CpuTensor::from_data(query_data, Shape::new(vec![1, 2, 4])).unwrap();
        let key = CpuTensor::from_data(key_data, Shape::new(vec![1, 2, 4])).unwrap();
        let value = CpuTensor::from_data(value_data, Shape::new(vec![1, 2, 4])).unwrap();

        let result = attention.compute_attention(&query, &key, &value, None);
        assert!(result.is_ok());

        let (_, attention_weights) = result.unwrap();
        let weights_data = attention_weights.data();

        // In causal attention, the upper triangular part should be 0
        // weights_data[1] should be 0 (position [0, 1] in the 2x2 attention matrix)
        assert!(weights_data[1].abs() < 1e-6);
    }

    #[test]
    fn test_input_validation() {
        let attention = ScaledDotProductAttention::<f32>::new(4, 0.0, false).unwrap();

        // Test mismatched batch sizes
        let query = CpuTensor::from_data(vec![1.0; 8], Shape::new(vec![1, 2, 4])).unwrap();
        let key = CpuTensor::from_data(vec![1.0; 16], Shape::new(vec![2, 2, 4])).unwrap(); // Different batch size
        let value = CpuTensor::from_data(vec![1.0; 8], Shape::new(vec![1, 2, 4])).unwrap();

        let result = attention.compute_attention(&query, &key, &value, None);
        assert!(result.is_err());

        // Test mismatched head dimensions
        let query = CpuTensor::from_data(vec![1.0; 8], Shape::new(vec![1, 2, 4])).unwrap();
        let key = CpuTensor::from_data(vec![1.0; 6], Shape::new(vec![1, 2, 3])).unwrap(); // Different head dim
        let value = CpuTensor::from_data(vec![1.0; 8], Shape::new(vec![1, 2, 4])).unwrap();

        let result = attention.compute_attention(&query, &key, &value, None);
        assert!(result.is_err());
    }
}
